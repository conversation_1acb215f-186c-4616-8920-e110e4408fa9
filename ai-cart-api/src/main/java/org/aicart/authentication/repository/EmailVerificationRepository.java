package org.aicart.authentication.repository;

import io.quarkus.hibernate.orm.panache.PanacheRepository;
import jakarta.enterprise.context.ApplicationScoped;
import org.aicart.authentication.entity.EmailVerification;

import java.util.Optional;

/**
 * Repository for managing EmailVerification entities.
 * 
 * This repository provides data access operations for email verification tokens,
 * including finding active tokens and cleaning up expired ones.
 * 
 * <AUTHOR> Cart Team
 * @version 1.0
 * @since 1.0
 */
@ApplicationScoped
public class EmailVerificationRepository implements PanacheRepository<EmailVerification> {

    /**
     * Finds an active email verification token for a specific entity.
     * 
     * @param entityId The ID of the entity (user/customer)
     * @param identifierName The identifier name (e.g., "user", "customer")
     * @param token The verification token
     * @return Optional containing the EmailVerification if found and not expired
     */
    public Optional<EmailVerification> findActiveToken(long entityId, String identifierName, String token) {
        long currentTime = System.currentTimeMillis() / 1000L;
        return find("entityId = ?1 AND identifierName = ?2 AND token = ?3 AND expiredAt > ?4", 
                   entityId, identifierName, token, currentTime)
               .firstResultOptional();
    }

    /**
     * Finds an email verification by entity ID and identifier name.
     * 
     * @param entityId The ID of the entity
     * @param identifierName The identifier name
     * @return Optional containing the EmailVerification if found
     */
    public Optional<EmailVerification> findByEntityAndIdentifier(long entityId, String identifierName) {
        return find("entityId = ?1 AND identifierName = ?2", entityId, identifierName)
               .firstResultOptional();
    }

    /**
     * Deletes expired email verification tokens.
     * 
     * @return Number of deleted tokens
     */
    public long deleteExpiredTokens() {
        long currentTime = System.currentTimeMillis() / 1000L;
        return delete("expiredAt <= ?1", currentTime);
    }

    /**
     * Deletes all tokens for a specific entity.
     * 
     * @param entityId The ID of the entity
     * @param identifierName The identifier name
     * @return Number of deleted tokens
     */
    public long deleteByEntityAndIdentifier(long entityId, String identifierName) {
        return delete("entityId = ?1 AND identifierName = ?2", entityId, identifierName);
    }
}
