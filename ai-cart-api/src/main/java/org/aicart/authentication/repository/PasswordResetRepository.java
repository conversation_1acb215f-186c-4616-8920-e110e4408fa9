package org.aicart.authentication.repository;

import io.quarkus.hibernate.orm.panache.PanacheRepository;
import jakarta.enterprise.context.ApplicationScoped;
import org.aicart.authentication.entity.PasswordReset;

import java.util.Optional;

/**
 * Repository for managing PasswordReset entities.
 * 
 * This repository provides data access operations for password reset tokens,
 * including finding active tokens and cleaning up expired ones.
 * 
 * <AUTHOR> Cart Team
 * @version 1.0
 * @since 1.0
 */
@ApplicationScoped
public class PasswordResetRepository implements PanacheRepository<PasswordReset> {

    /**
     * Finds an active password reset token for a specific entity.
     * 
     * @param entityId The ID of the entity (user/customer)
     * @param identifierName The identifier name (e.g., "user", "customer")
     * @param token The reset token
     * @return Optional containing the PasswordReset if found and not expired
     */
    public Optional<PasswordReset> findActiveToken(long entityId, String identifierName, String token) {
        long currentTime = System.currentTimeMillis() / 1000L;
        return find("entityId = ?1 AND identifierName = ?2 AND token = ?3 AND expiredAt > ?4", 
                   entityId, identifierName, token, currentTime)
               .firstResultOptional();
    }

    /**
     * Finds a password reset by entity ID and identifier name.
     * 
     * @param entityId The ID of the entity
     * @param identifierName The identifier name
     * @return Optional containing the PasswordReset if found
     */
    public Optional<PasswordReset> findByEntityAndIdentifier(long entityId, String identifierName) {
        return find("entityId = ?1 AND identifierName = ?2", entityId, identifierName)
               .firstResultOptional();
    }

    /**
     * Deletes expired password reset tokens.
     * 
     * @return Number of deleted tokens
     */
    public long deleteExpiredTokens() {
        long currentTime = System.currentTimeMillis() / 1000L;
        return delete("expiredAt <= ?1", currentTime);
    }

    /**
     * Deletes all tokens for a specific entity.
     * 
     * @param entityId The ID of the entity
     * @param identifierName The identifier name
     * @return Number of deleted tokens
     */
    public long deleteByEntityAndIdentifier(long entityId, String identifierName) {
        return delete("entityId = ?1 AND identifierName = ?2", entityId, identifierName);
    }

    /**
     * Finds a password reset token by token value only.
     * 
     * @param token The reset token
     * @return Optional containing the PasswordReset if found
     */
    public Optional<PasswordReset> findByToken(String token) {
        return find("token = ?1", token).firstResultOptional();
    }
}
