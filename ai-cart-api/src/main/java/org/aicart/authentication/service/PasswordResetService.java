package org.aicart.authentication.service;

import org.aicart.authentication.entity.PasswordReset;

public abstract class PasswordResetService {

    protected void storeToken(long entityId, String identifierName, String token, long expiredAt) {
        PasswordReset passwordReset = PasswordReset.find("entityId = ?1 AND identifierName = ?2", entityId, identifierName).firstResult();

        if (passwordReset == null) {
            passwordReset = new PasswordReset();
            passwordReset.entityId = entityId;
            passwordReset.identifierName = identifierName;
        }

        passwordReset.token = token;
        passwordReset.expiredAt = expiredAt;
        passwordReset.persist();
    }

    protected long getExpiryDuration() {
        final long expiryDuration = 10 * 60L;
        return System.currentTimeMillis() / 1000L + expiryDuration;
    }
}
