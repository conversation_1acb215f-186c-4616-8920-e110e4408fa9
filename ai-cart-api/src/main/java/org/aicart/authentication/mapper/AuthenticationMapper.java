package org.aicart.authentication.mapper;

import org.aicart.authentication.dto.TokenUser;
import org.aicart.authentication.entity.EmailVerification;
import org.aicart.authentication.entity.PasswordReset;

/**
 * Mapper class for Authentication domain objects.
 * 
 * This class provides mapping methods between entities and DTOs
 * for authentication-related operations.
 * 
 * <AUTHOR> Cart Team
 * @version 1.0
 * @since 1.0
 */
public class AuthenticationMapper {

    /**
     * Maps an entity to TokenUser DTO.
     *
     * @param entityId The ID of the entity
     * @param email The email of the entity
     * @param identifierName The identifier name (e.g., "user", "customer")
     * @return TokenUser DTO
     */
    public static TokenUser toTokenUser(long entityId, String email, String identifierName) {
        return new TokenUser(entityId, email, identifierName);
    }

    /**
     * Creates an EmailVerification entity from parameters.
     * 
     * @param entityId The ID of the entity
     * @param identifierName The identifier name
     * @param token The verification token
     * @return EmailVerification entity
     */
    public static EmailVerification createEmailVerification(long entityId, String identifierName, String token) {
        EmailVerification verification = new EmailVerification();
        verification.entityId = entityId;
        verification.identifierName = identifierName;
        verification.token = token;
        return verification;
    }

    /**
     * Creates a PasswordReset entity from parameters.
     * 
     * @param entityId The ID of the entity
     * @param identifierName The identifier name
     * @param token The reset token
     * @return PasswordReset entity
     */
    public static PasswordReset createPasswordReset(long entityId, String identifierName, String token) {
        PasswordReset passwordReset = new PasswordReset();
        passwordReset.entityId = entityId;
        passwordReset.identifierName = identifierName;
        passwordReset.token = token;
        return passwordReset;
    }

    /**
     * Checks if an EmailVerification token is valid (not expired).
     * 
     * @param verification The EmailVerification entity
     * @return true if valid, false otherwise
     */
    public static boolean isEmailVerificationValid(EmailVerification verification) {
        if (verification == null) {
            return false;
        }
        long currentTime = System.currentTimeMillis() / 1000L;
        return verification.expiredAt > currentTime;
    }

    /**
     * Checks if a PasswordReset token is valid (not expired).
     * 
     * @param passwordReset The PasswordReset entity
     * @return true if valid, false otherwise
     */
    public static boolean isPasswordResetValid(PasswordReset passwordReset) {
        if (passwordReset == null) {
            return false;
        }
        long currentTime = System.currentTimeMillis() / 1000L;
        return passwordReset.expiredAt > currentTime;
    }
}
