package org.aicart.user.auth.service;

import io.quarkus.elytron.security.common.BcryptUtil;
import io.quarkus.mailer.Mail;
import io.quarkus.mailer.reactive.ReactiveMailer;
import io.quarkus.qute.Location;
import io.quarkus.qute.Template;
import io.quarkus.qute.TemplateInstance;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.SecurityContext;
import org.aicart.authentication.service.PasswordResetService;
import org.aicart.authentication.service.TokenGenerator;
import org.aicart.authentication.dto.ResetPasswordDTO;
import org.aicart.authentication.dto.TokenUser;
import org.aicart.authentication.entity.PasswordReset;
import org.aicart.user.entity.User;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;

@ApplicationScoped
public class UserPasswordReset extends PasswordResetService {

    @Inject
    ReactiveMailer reactiveMailer;

    @Inject
    @Location("mail/reset-password.html")
    Template resetPasswordMailTemplate;

    @Context
    SecurityContext securityContext;

    @Transactional
    public Response forgetPassword(String email, String origin) {

        System.out.println("origin: "+ origin);

        // Check if the user is authenticated
        if (securityContext.getUserPrincipal() != null) {
            // If authenticated, block access to this route
            throw BusinessException.forbidden("Authenticated users cannot reset their password.");
        }

        User user = User.find("where email = ?1", email).firstResult();

        if(user == null) {
            throw BusinessException.badRequest("Invalid email address.");
        }

        sendMail(user, origin);

        return Response.ok(ApiResponse.success(null, "Reset link sent successfully")).build();
    }


    private void sendMail(User user, String origin) {

        long expiredAt = getExpiryDuration();

        String token = TokenGenerator.generateToken(user.id, user.getIdentifier(), user.email, expiredAt);

        TemplateInstance resetPasswordMailInstance = resetPasswordMailTemplate
                .data("origin", origin)
                .data("name", user.name)
                .data("token", token)
                .data("expiryMinutes", 10);

        System.out.println("SEND EMAIL VERIFY SERVICE");

        Mail mail = Mail.withHtml(user.email,
                "Test Email reset password Quarkus",
                resetPasswordMailInstance.render());


        storeToken(user.id, user.getIdentifier(), token, expiredAt);

        reactiveMailer.send(mail).subscribe().with(
                success -> System.out.println("Email sent successfully!"),
                failure -> System.err.println("Failed to send email: " + failure.getMessage())
        );

    }

    @Transactional
    public Response resetPassword(ResetPasswordDTO resetPasswordDTO) {

        // Check if the user is authenticated
        if (securityContext.getUserPrincipal() != null) {
            // If authenticated, block access to this route
            throw BusinessException.forbidden("Authenticated users cannot reset their password.");
        }

        long currentTime = System.currentTimeMillis() / 1000;

        TokenUser tokenUser = TokenGenerator.getTokenUser(resetPasswordDTO.getToken());
        if(tokenUser == null) {
            throw BusinessException.badRequest("Link expired");
        }

        PasswordReset passwordReset =
                PasswordReset.find("entityId = ?1 AND identifierName = ?2 AND token = ?3 AND expiredAt >= ?4", tokenUser.getUserId(), tokenUser.getIdentifierName(), resetPasswordDTO.getToken(), currentTime)
                        .firstResult();

        if (passwordReset == null) {
            throw BusinessException.badRequest("Link expired");
        }

        User user = User.findById(tokenUser.getUserId());
        if(user == null) {
            throw BusinessException.badRequest("Link expired");
        }

        user.password = BcryptUtil.bcryptHash(resetPasswordDTO.getPassword());
        user.persist();

        PasswordReset.delete("entityId = ?1 AND identifierName = ?2", user.id, user.getIdentifier());

        return Response.ok(ApiResponse.success(null, "Password reset successfully")).build();
    }
}
