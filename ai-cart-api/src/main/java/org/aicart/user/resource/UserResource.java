package org.aicart.user.resource;

import io.quarkus.security.Authenticated;
import io.quarkus.security.identity.SecurityIdentity;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.user.auth.dto.UserProfileDTO;
import org.aicart.user.service.UserDashboardService;
import org.aicart.user.service.UserService;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.jboss.resteasy.reactive.NoCache;
import org.aicart.order.dto.OrderBillingDTO;
import org.aicart.order.dto.OrderShippingDTO;
import org.aicart.user.entity.User;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;

import java.util.Map;

@Path("/users")
@Authenticated
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class UserResource {

    @Inject
    SecurityIdentity identity;

    @Inject
    JsonWebToken jwt;

    @Inject
    UserService userService;

    @Inject
    UserDashboardService userDashboardService;

    @GET
    @Path("/me")
    @NoCache
    public String me() {
        return identity.getPrincipal().toString();
    }

    @POST
    @Path("/update-billing")
    @Transactional
    public Response updateBilling(@Valid OrderBillingDTO orderBillingDTO) {

        if (orderBillingDTO == null) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(Map.of("message", "Request body is required"))
                    .build();
        }

        String subject = jwt.getSubject();

        // Find the user by id
        User user = User.find("id", subject).firstResult();

        if (user == null) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(Map.of("message", "Invalid action"))
                    .build();
        }

        userService.storeUserBilling(user, orderBillingDTO);

        return Response.status(Response.Status.OK)
                .entity(Map.of("message", "Billing address saved successfully"))
                .build();

    }


    @POST
    @Path("/update-shipping")
    @Transactional
    public Response updateShipping(@Valid OrderShippingDTO orderShippingDTO) {

        if (orderShippingDTO == null) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(Map.of("message", "Request body is required"))
                    .build();
        }

        String subject = jwt.getSubject();

        // Find the user by id
        User user = User.find("id", subject).firstResult();

        if (user == null) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(Map.of("message", "Invalid action"))
                    .build();
        }

        userService.storeUserShipping(user, orderShippingDTO);

        return Response.status(Response.Status.OK)
                .entity(Map.of("message", "Shipping address saved successfully"))
                .build();
    }

    @PUT
    @Path("/update-profile")
    @Transactional
    public Response updateProfile(@Valid UserProfileDTO userProfileDTO) {

        if (userProfileDTO == null) {
            throw BusinessException.badRequest("Request body is required");
        }

        String subject = jwt.getSubject();

        // Find the user by id
        User user = User.find("id", subject).firstResult();

        if (user == null) {
            throw BusinessException.badRequest("Invalid action");
        }

        userService.updateProfile(user, userProfileDTO);

        return Response.status(Response.Status.OK)
                .entity(ApiResponse.success(null, "Profile saved successfully"))
                .build();
    }

    // Dashboard APIs for user data (used by frontend server actions)
    @GET
    @Path("/{userId}/stats")
    public Response getUserStats(@PathParam("userId") Long userId) {
        Map<String, Object> stats = userDashboardService.getUserStats(userId);
        return Response.ok(stats).build();
    }

    @GET
    @Path("/{userId}/recent-orders")
    public Response getRecentOrders(@PathParam("userId") Long userId) {
        var orders = userDashboardService.getRecentOrders(userId);
        return Response.ok(orders).build();
    }

    @GET
    @Path("/{userId}/orders/{orderId}")
    public Response getOrderDetail(@PathParam("userId") Long userId,
                                   @PathParam("orderId") Long orderId) {
        var order = userDashboardService.getOrderDetail(userId, orderId);
        if (order == null) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }
        return Response.ok(order).build();
    }

    @GET
    @Path("/{userId}/billing")
    public Response getUserBilling(@PathParam("userId") Long userId) {
        var billing = userDashboardService.getUserBilling(userId);
        return Response.ok(billing).build();
    }

    @GET
    @Path("/{userId}/shipping")
    public Response getUserShipping(@PathParam("userId") Long userId) {
        var shipping = userDashboardService.getUserShipping(userId);
        return Response.ok(shipping).build();
    }

    @GET
    @Path("/{userId}/profile")
    public Response getUserProfile(@PathParam("userId") Long userId) {
        var profile = userDashboardService.getUserProfile(userId);
        if (profile == null) {
            throw BusinessException.notFound("User profile");
        }
        return Response.ok(ApiResponse.success(profile)).build();
    }

    @GET
    @Path("/password-token/{token}/validity")
    public Response getPasswordTokenValidity(@PathParam("token") String token) {
        boolean isValid = userDashboardService.getPasswordTokenValidity(token);
        return Response.ok(ApiResponse.success(Map.of("valid", isValid))).build();
    }
}
