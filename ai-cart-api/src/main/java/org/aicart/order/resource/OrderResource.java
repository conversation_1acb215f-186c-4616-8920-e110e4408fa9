package org.aicart.order.resource;

import io.quarkus.security.Authenticated;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.order.entity.PaymentStatusEnum;
import org.aicart.order.dto.*;
import org.aicart.order.entity.OrderStatusEnum;
import org.aicart.order.service.OrderService;
import org.aicart.order.entity.Order;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.shop.context.ShopContext;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Path("/orders")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Authenticated
@RequireShopId
public class OrderResource {

    @Inject
    OrderService orderService;

    @Inject
    ShopContext shopContext;

    private String getCurrentUser() {
        // TODO:: In a real application, this would come from authentication context
        return "<EMAIL>";
    }

    /**
     * Create a new order
     */
    @POST
    public Response createOrder(@Valid OrderCreateRequestDTO createRequest) {
        OrderDetailDTO order = orderService.createOrder(shopContext.getShop(), createRequest, getCurrentUser());
        return Response.status(Response.Status.CREATED).entity(ApiResponse.success(order, "Order created successfully")).build();
    }

    /**
     * Get paginated list of orders with filters
     */
    @GET
    public Response getOrders(
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("20") int size,
            @QueryParam("search") String search,
            @QueryParam("status") OrderStatusEnum status,
            @QueryParam("paymentStatus") PaymentStatusEnum paymentStatus,
            @QueryParam("startDate") String startDate,
            @QueryParam("endDate") String endDate,
            @QueryParam("sortBy") @DefaultValue("createdAt") String sortBy,
            @QueryParam("order") @DefaultValue("desc") String order) {

        LocalDateTime start = null;
        LocalDateTime end = null;

        if (startDate != null && !startDate.trim().isEmpty()) {
            start = LocalDateTime.parse(startDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
        if (endDate != null && !endDate.trim().isEmpty()) {
            end = LocalDateTime.parse(endDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }

        OrderListResponse response = orderService.findOrdersWithFilters(
            shopContext.getShop(), search, status, paymentStatus, start, end, page, size, sortBy, order);

        return Response.ok(ApiResponse.success(response)).build();
    }

    /**
     * Get order details by ID
     */
    @GET
    @Path("/{id}")
    public Response getOrderById(@PathParam("id") Long id) {
        OrderDetailDTO order = orderService.getOrderDetails(shopContext.getShop(), id);
        return Response.ok(ApiResponse.success(order)).build();
    }

    /**
     * Update an existing order
     */
    @PUT
    @Path("/{id}")
    public Response updateOrder(@PathParam("id") Long id, @Valid OrderUpdateRequestDTO updateRequest) {
        OrderDetailDTO order = orderService.updateOrder(shopContext.getShop(), id, updateRequest, getCurrentUser());
        return Response.ok(ApiResponse.success(order, "Order updated successfully")).build();
    }

    /**
     * Update order status
     */
    @PUT
    @Path("/{id}/status")
    public Response updateOrderStatus(@PathParam("id") Long id, @Valid OrderStatusUpdateDTO updateDTO) {
        OrderDetailDTO order = orderService.updateOrderStatus(shopContext.getShop(), id, updateDTO, getCurrentUser());
        return Response.ok(ApiResponse.success(order, "Order status updated successfully")).build();
    }

    /**
     * Cancel an order
     */
    @PUT
    @Path("/{id}/cancel")
    public Response cancelOrder(@PathParam("id") Long id, Map<String, String> requestBody) {
        String reason = requestBody.getOrDefault("reason", "Cancelled by admin");
        OrderDetailDTO order = orderService.cancelOrder(shopContext.getShop(), id, reason, getCurrentUser());
        return Response.ok(ApiResponse.success(order, "Order cancelled successfully")).build();
    }

    /**
     * Create a refund for an order
     */
    @POST
    @Path("/{id}/refunds")
    public Response createRefund(@PathParam("id") Long id, @Valid OrderRefundRequestDTO refundRequest) {
        OrderRefundDTO refund = orderService.createRefund(shopContext.getShop(), id, refundRequest, getCurrentUser());
        return Response.status(Response.Status.CREATED).entity(ApiResponse.success(refund, "Refund created successfully")).build();
    }

    /**
     * Create an exchange for an order
     */
    @POST
    @Path("/{id}/exchanges")
    public Response createExchange(@PathParam("id") Long id, @Valid OrderExchangeRequestDTO exchangeRequest) {
        OrderExchangeDTO exchange = orderService.createExchange(shopContext.getShop(), id, exchangeRequest, getCurrentUser());
        return Response.status(Response.Status.CREATED).entity(ApiResponse.success(exchange, "Exchange created successfully")).build();
    }

    /**
     * Create a credit note for an order
     */
    @POST
    @Path("/{id}/credit-notes")
    public Response createCreditNote(@PathParam("id") Long id, @Valid OrderCreditNoteRequestDTO creditNoteRequest) {
        OrderCreditNoteDTO creditNote = orderService.createCreditNote(shopContext.getShop(), id, creditNoteRequest, getCurrentUser());
        return Response.status(Response.Status.CREATED).entity(ApiResponse.success(creditNote, "Credit note created successfully")).build();
    }

    /**
     * Get order statistics
     */
    @GET
    @Path("/stats")
    public Response getOrderStats(
            @QueryParam("startDate") String startDate,
            @QueryParam("endDate") String endDate) {
        LocalDateTime start = startDate != null ?
            LocalDateTime.parse(startDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME) :
            LocalDateTime.now().minusDays(30);

        LocalDateTime end = endDate != null ?
            LocalDateTime.parse(endDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME) :
            LocalDateTime.now();

        OrderStatsDTO stats = orderService.getOrderStats(shopContext.getShop(), start, end);
        return Response.ok(ApiResponse.success(stats)).build();
    }

    /**
     * Delete an order (soft delete)
     */
    @DELETE
    @Path("/{id}")
    public Response deleteOrder(@PathParam("id") Long id) {
        Order order = orderService.orderRepository.findByIdAndShop(id, shopContext.getShop());
        if (order == null) {
            throw BusinessException.notFound("Order");
        }

        // Only allow deletion of cancelled or draft orders
        if (order.status != OrderStatusEnum.CANCELED && order.status != OrderStatusEnum.PENDING) {
            throw BusinessException.badRequest("Cannot delete order in current status: " + order.status);
        }

        // Soft delete by updating status
        order.status = OrderStatusEnum.CANCELED;
        order.persist();

        return Response.ok(ApiResponse.success(null, "Order deleted successfully")).build();
    }

    /**
     * Bulk update order status
     */
    @PUT
    @Path("/bulk/status")
    public Response bulkUpdateStatus(@Valid BulkOrderStatusUpdateDTO bulkUpdate) {
        List<Long> updatedOrders = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        for (Long orderId : bulkUpdate.getOrderIds()) {
            try {
                orderService.updateOrderStatus(shopContext.getShop(), orderId, bulkUpdate.getStatusUpdate(), getCurrentUser());
                updatedOrders.add(orderId);
            } catch (Exception e) {
                errors.add("Order " + orderId + ": " + e.getMessage());
            }
        }

        Map<String, Object> response = new HashMap<>();
        response.put("updated", updatedOrders);
        response.put("errors", errors);
        response.put("totalUpdated", updatedOrders.size());
        response.put("totalErrors", errors.size());

        return Response.ok(ApiResponse.success(response, "Bulk status update completed")).build();
    }

    /**
     * Bulk cancel orders
     */
    @PUT
    @Path("/bulk/cancel")
    public Response bulkCancelOrders(@Valid BulkOrderCancelDTO bulkCancel) {
        List<Long> cancelledOrders = new ArrayList<>();
        List<String> errors = new ArrayList<>();

        for (Long orderId : bulkCancel.getOrderIds()) {
            try {
                orderService.cancelOrder(shopContext.getShop(), orderId, bulkCancel.getReason(), getCurrentUser());
                cancelledOrders.add(orderId);
            } catch (Exception e) {
                errors.add("Order " + orderId + ": " + e.getMessage());
            }
        }

        Map<String, Object> responseData = new HashMap<>();
        responseData.put("cancelled", cancelledOrders);
        responseData.put("errors", errors);
        responseData.put("totalCancelled", cancelledOrders.size());
        responseData.put("totalErrors", errors.size());

        return Response.ok(ApiResponse.success(responseData)).build();
    }

    /**
     * Export orders to CSV
     */
    @GET
    @Path("/export")
    @Produces("text/csv")
    public Response exportOrders(
            @QueryParam("search") String search,
            @QueryParam("status") OrderStatusEnum status,
            @QueryParam("paymentStatus") PaymentStatusEnum paymentStatus,
            @QueryParam("startDate") String startDate,
            @QueryParam("endDate") String endDate) {
        LocalDateTime start = null;
        LocalDateTime end = null;

        if (startDate != null && !startDate.trim().isEmpty()) {
            start = LocalDateTime.parse(startDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
        if (endDate != null && !endDate.trim().isEmpty()) {
            end = LocalDateTime.parse(endDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }

        // Get all orders for export (no pagination)
        OrderListResponse response = orderService.findOrdersWithFilters(
            shopContext.getShop(), search, status, paymentStatus, start, end, 0, Integer.MAX_VALUE, "createdAt", "desc");

        String csv = orderService.exportOrdersToCSV(response.getOrders());

        return Response.ok(csv)
                .header("Content-Disposition", "attachment; filename=orders_" +
                       LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".csv")
                .build();
    }

    /**
     * Get order status options
     */
    @GET
    @Path("/status-options")
    public Response getOrderStatusOptions() {
        List<Map<String, Object>> statusOptions = new ArrayList<>();
        for (OrderStatusEnum status : OrderStatusEnum.values()) {
            Map<String, Object> option = new HashMap<>();
            option.put("value", status.name());
            option.put("label", formatStatusLabel(status));
            statusOptions.add(option);
        }

        return Response.ok(ApiResponse.success(statusOptions)).build();
    }

    /**
     * Get payment status options
     */
    @GET
    @Path("/payment-status-options")
    public Response getPaymentStatusOptions() {
        List<Map<String, Object>> statusOptions = new ArrayList<>();
        for (PaymentStatusEnum status : PaymentStatusEnum.values()) {
            Map<String, Object> option = new HashMap<>();
            option.put("value", status.name());
            option.put("label", formatPaymentStatusLabel(status));
            statusOptions.add(option);
        }

        return Response.ok(ApiResponse.success(statusOptions)).build();
    }

    // Helper methods
    private String formatStatusLabel(OrderStatusEnum status) {
        return switch (status) {
            case PENDING -> "Pending";
            case CONFIRMED -> "Confirmed";
            case PROCESSING -> "Processing";
            case PACKED -> "Packed";
            case SHIPPED -> "Shipped";
            case OUT_FOR_DELIVERY -> "Out for Delivery";
            case DELIVERED -> "Delivered";
            case COMPLETED -> "Completed";
            case CANCELED -> "Cancelled";
            case REFUNDED -> "Refunded";
            case PARTIALLY_REFUNDED -> "Partially Refunded";
            case RETURNED -> "Returned";
            case EXCHANGED -> "Exchanged";
            case FAILED -> "Failed";
        };
    }

    private String formatPaymentStatusLabel(PaymentStatusEnum status) {
        return switch (status) {
            case PENDING -> "Pending";
            case COMPLETED -> "Completed";
            case FAILED -> "Failed";
            case REFUNDED -> "Refunded";
        };
    }
}
