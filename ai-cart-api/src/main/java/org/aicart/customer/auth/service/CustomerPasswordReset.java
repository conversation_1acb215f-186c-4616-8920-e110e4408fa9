package org.aicart.customer.auth.service;

import io.quarkus.elytron.security.common.BcryptUtil;
import io.quarkus.mailer.Mail;
import io.quarkus.mailer.reactive.ReactiveMailer;
import io.quarkus.qute.Location;
import io.quarkus.qute.Template;
import io.quarkus.qute.TemplateInstance;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.SecurityContext;
import org.aicart.authentication.service.PasswordResetService;
import org.aicart.authentication.service.TokenGenerator;
import org.aicart.authentication.dto.ResetPasswordDTO;
import org.aicart.authentication.dto.TokenUser;
import org.aicart.authentication.entity.PasswordReset;
import org.aicart.shop.context.ShopContext;
import org.aicart.customer.entity.Customer;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;

@ApplicationScoped
public class CustomerPasswordReset extends PasswordResetService {

    @Inject
    ReactiveMailer reactiveMailer;

    @Inject
    @Location("mail/reset-password.html")
    Template resetPasswordMailTemplate;

    @Context
    SecurityContext securityContext;

    @Inject
    ShopContext shopContext;

    @Transactional
    public Response forgetPassword(String email, String origin) {

        // Check if the user is authenticated
        if (securityContext.getUserPrincipal() != null) {
            // If authenticated, block access to this route
            throw BusinessException.forbidden("Authenticated users cannot reset their password.");
        }

        Customer customer = Customer.find("where email = ?1 AND shop.id = ?2", email, shopContext.getShopId()).firstResult();

        if(customer == null) {
            throw BusinessException.badRequest("Invalid email address.");
        }

        sendMail(customer, origin);

        return Response.ok(ApiResponse.success(null, "Reset link sent successfully")).build();
    }


    private void sendMail(Customer customer, String origin) {

        long expiredAt = getExpiryDuration();

        String token = TokenGenerator.generateToken(customer.id, customer.getIdentifier(), customer.email, expiredAt);

        TemplateInstance resetPasswordMailInstance = resetPasswordMailTemplate
                .data("origin", origin)
                .data("name", customer.firstName)
                .data("token", token)
                .data("expiryMinutes", 10);

        System.out.println("SEND EMAIL VERIFY SERVICE");

        Mail mail = Mail.withHtml(customer.email,
                "Test Email reset password Quarkus",
                resetPasswordMailInstance.render());


        storeToken(customer.id, customer.getIdentifier(), token, expiredAt);

        reactiveMailer.send(mail).subscribe().with(
                success -> System.out.println("Email sent successfully!"),
                failure -> System.err.println("Failed to send email: " + failure.getMessage())
        );

    }

    @Transactional
    public Response resetPassword(ResetPasswordDTO resetPasswordDTO) {

        // Check if the user is authenticated
        if (securityContext.getUserPrincipal() != null) {
            // If authenticated, block access to this route
            throw BusinessException.forbidden("Authenticated users cannot reset their password.");
        }

        long currentTime = System.currentTimeMillis() / 1000;

        TokenUser tokenUser = TokenGenerator.getTokenUser(resetPasswordDTO.getToken());
        if(tokenUser == null) {
            throw BusinessException.badRequest("Link expired");
        }

        PasswordReset passwordReset =
                PasswordReset.find("entityId = ?1 AND identifierName = ?2 AND token = ?3 AND expiredAt >= ?4", tokenUser.getUserId(), tokenUser.getIdentifierName(), resetPasswordDTO.getToken(), currentTime)
                .firstResult();

        if (passwordReset == null) {
            throw BusinessException.badRequest("Link expired");
        }

        Customer customer = Customer.findById(tokenUser.getUserId());
        if(customer == null) {
            throw BusinessException.badRequest("Link expired");
        }

        customer.password = BcryptUtil.bcryptHash(resetPasswordDTO.getPassword());
        customer.persist();

        PasswordReset.delete("entityId = ?1 AND identifierName = ?2", customer.id, customer.getIdentifier());

        return Response.ok(ApiResponse.success(null, "Password reset successfully")).build();
    }
}
