package org.aicart.customer.resource;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.customer.dto.*;
import org.aicart.customer.service.CustomerService;
import org.aicart.customer.entity.CustomerType;
import org.aicart.customer.entity.CustomerTier;
import org.aicart.customer.mapper.CustomerAddressMapper;
import org.aicart.customer.service.CustomerTagService;
import org.aicart.customer.entity.Customer;
import org.aicart.customer.entity.CustomerTag;
import org.aicart.customer.mapper.CustomerTagMapper;
import org.aicart.customer.repository.CustomerRepository;
import java.util.List;
import java.util.Optional;
import jakarta.transaction.Transactional;
import org.aicart.customer.mapper.CustomerMapper;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.shop.context.ShopContext;
import org.aicart.shop.entity.Shop;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;

@Path("/customers")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequireShopId
public class CustomerResource {

    @Inject
    CustomerService customerService;

    @Inject
    CustomerAddressMapper customerAddressMapper;

    @Inject
    CustomerTagService customerTagService;

    @Inject
    CustomerRepository customerRepository;

    @Inject
    ShopContext shopContext;

    private String getCurrentUser() {
        // TODO:: In a real application, this would come from authentication context
        return "<EMAIL>";
    }



    @PUT
    @Path("/{customerId}/primary-address/{addressId}")
    public Response setPrimaryAddress(
            @PathParam("customerId") Long customerId,
            @PathParam("addressId") Long addressId
    ) {
        return Response.ok(CustomerMapper.toDto(customerService.setPrimaryAddress(customerId, addressId))).build();
    }

    @GET
    @Path("/{customerId}/primary-address")
    public Response getPrimaryAddress(@PathParam("customerId") Long customerId) {
        return customerService.getCustomer(customerId)
                .map(customer -> Response.ok(customerAddressMapper.toDTO(customer.primaryAddress)).build())
                .orElse(Response.status(Response.Status.NOT_FOUND).build());
    }

    // New comprehensive endpoints

    /**
     * Create a new customer
     */
    @POST
    public Response createCustomer(@Valid CustomerCreateRequestDTO createRequest) {
        CustomerDetailDTO customer = customerService.createCustomer(shopContext.getShop(), createRequest, getCurrentUser());
        return Response.status(Response.Status.CREATED).entity(ApiResponse.success(customer, "Customer created successfully")).build();
    }

    /**
     * Get paginated list of customers with filters
     */
    @GET
    public Response getCustomers(
            @QueryParam("page") @DefaultValue("1") int page,
            @QueryParam("size") @DefaultValue("20") int size,
            @QueryParam("search") String search,
            @QueryParam("customerType") CustomerType customerType,
            @QueryParam("customerTier") CustomerTier customerTier,
            @QueryParam("emailVerified") Boolean emailVerified,
            @QueryParam("accountLocked") Boolean accountLocked,
            @QueryParam("startDate") String startDate,
            @QueryParam("endDate") String endDate,
            @QueryParam("sortBy") @DefaultValue("createdAt") String sortBy,
            @QueryParam("order") @DefaultValue("desc") String order) {

        // Parse dates
        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;

        if (startDate != null && !startDate.trim().isEmpty()) {
            startDateTime = LocalDateTime.parse(startDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }

        if (endDate != null && !endDate.trim().isEmpty()) {
            endDateTime = LocalDateTime.parse(endDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }

        // Convert 1-based page to 0-based for Panache
        int zeroBasedPage = Math.max(0, page - 1);

        CustomerListResponseDTO response = customerService.getCustomers(
            shopContext.getShop(), search, customerType, customerTier, emailVerified,
            accountLocked, startDateTime, endDateTime, sortBy, order, zeroBasedPage, size
        );

        return Response.ok(ApiResponse.success(response)).build();
    }

    /**
     * Get customer by ID
     */
    @GET
    @Path("/{id}")
    public Response getCustomer(@PathParam("id") Long id) {
        CustomerDetailDTO customer = customerService.getCustomerDetail(shopContext.getShop(), id);
        return Response.ok(ApiResponse.success(customer)).build();
    }

    /**
     * Update an existing customer
     */
    @PUT
    @Path("/{id}")
    public Response updateCustomer(@PathParam("id") Long id, @Valid CustomerUpdateRequestDTO updateRequest) {
        CustomerDetailDTO customer = customerService.updateCustomer(shopContext.getShop(), id, updateRequest, getCurrentUser());
        return Response.ok(ApiResponse.success(customer, "Customer updated successfully")).build();
    }

    /**
     * Delete a customer (soft delete)
     */
    @DELETE
    @Path("/{id}")
    public Response deleteCustomer(@PathParam("id") Long id) {
        customerService.deleteCustomer(shopContext.getShop(), id, getCurrentUser());
        return Response.ok(ApiResponse.success(null, "Customer deleted successfully")).build();
    }

    /**
     * Create customer from email signup (newsletter, lead generation)
     * This is the minimal customer creation endpoint for email capture forms
     */
    @POST
    @Path("/email-signup")
    public Response createCustomerFromEmail(@Valid CustomerEmailSignupDTO signupRequest) {
        try {
            CustomerDetailDTO customer = customerService.createCustomerFromEmail(shopContext.getShop(), signupRequest);

            // Return 201 for new customer, 200 for existing customer updated
            boolean isNewCustomer = customer.getCreatedAt().isAfter(LocalDateTime.now().minusMinutes(1));
            Response.Status status = isNewCustomer ? Response.Status.CREATED : Response.Status.OK;

            return Response.status(status)
                    .entity(Map.of(
                        "message", isNewCustomer ? "Customer created successfully" : "Customer preferences updated",
                        "customer", customer,
                        "isNewCustomer", isNewCustomer
                    ))
                    .build();

        } catch (RuntimeException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(Map.of("message", e.getMessage()))
                    .build();
        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity(Map.of("message", e.getMessage()))
                    .build();
        }
    }

    /**
     * Get customer statistics
     */
    @GET
    @Path("/stats")
    public Response getCustomerStats() {
        CustomerStatsDTO stats = customerService.getCustomerStats(shopContext.getShop());
        return Response.ok(ApiResponse.success(stats)).build();
    }

    /**
     * Export customers to CSV
     */
    @GET
    @Path("/export")
    @Produces("text/csv")
    public Response exportCustomers(
            @QueryParam("search") String search,
            @QueryParam("customerType") CustomerType customerType,
            @QueryParam("customerTier") CustomerTier customerTier,
            @QueryParam("emailVerified") Boolean emailVerified,
            @QueryParam("accountLocked") Boolean accountLocked,
            @QueryParam("startDate") String startDate,
            @QueryParam("endDate") String endDate) {

        // Parse dates
        LocalDateTime startDateTime = null;
        LocalDateTime endDateTime = null;

        if (startDate != null && !startDate.trim().isEmpty()) {
            startDateTime = LocalDateTime.parse(startDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }

        if (endDate != null && !endDate.trim().isEmpty()) {
            endDateTime = LocalDateTime.parse(endDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }

            // Get all customers (no pagination for export)
            CustomerListResponseDTO response = customerService.getCustomers(
                shopContext.getShop(), search, customerType, customerTier, emailVerified,
                accountLocked, startDateTime, endDateTime, "createdAt", "desc", 0, Integer.MAX_VALUE
            );

            // Generate CSV
            StringBuilder csv = new StringBuilder();
            csv.append("ID,First Name,Last Name,Email,Phone,Company,Customer Type,Customer Tier,Email Verified,Account Locked,Total Orders,Total Spent,Lifetime Value,Created At\n");

            for (CustomerListDTO customer : response.getCustomers()) {
                csv.append(customer.getId()).append(",")
                   .append(escapeCSV(customer.getFirstName())).append(",")
                   .append(escapeCSV(customer.getLastName())).append(",")
                   .append(escapeCSV(customer.getEmail())).append(",")
                   .append(escapeCSV(customer.getPhone())).append(",")
                   .append(escapeCSV(customer.getCompany())).append(",")
                   .append(customer.getCustomerType()).append(",")
                   .append(customer.getCustomerTier()).append(",")
                   .append(customer.isEmailVerified()).append(",")
                   .append(customer.isAccountLocked()).append(",")
                   .append(customer.getTotalOrders()).append(",")
                   .append(customer.getTotalSpent()).append(",")
                   .append(customer.getLifetimeValue()).append(",")
                   .append(customer.getCreatedAt()).append("\n");
            }

            return Response.ok(csv.toString())
                    .header("Content-Disposition", "attachment; filename=\"customers.csv\"")
                    .build();


    }

    // Helper method to escape CSV values
    private String escapeCSV(String value) {
        if (value == null) return "";
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }

    /**
     * Get customer tags
     */
    @GET
    @Path("/{id}/tags")
    public Response getCustomerTags(@PathParam("id") Long customerId) {
        Customer customer = customerRepository.findByIdAndShop(customerId, shopContext.getShop());

        if (customer == null) {
            throw BusinessException.notFound("Customer");
        }

        List<CustomerTagDTO> tags = customer.tags.stream()
                .map(CustomerTagMapper::toDto)
                .collect(java.util.stream.Collectors.toList());

        return Response.ok(ApiResponse.success(tags)).build();
    }

    /**
     * Add tag to customer
     */
    @POST
    @Path("/{id}/tags/{tagId}")
    @Transactional
    public Response addTagToCustomer(
            @PathParam("id") Long customerId,
            @PathParam("tagId") Long tagId) {
        try {
            Shop shop = shopContext.getShop();
            Customer customer = customerRepository.findByIdAndShop(customerId, shop);

            if (customer == null) {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity(Map.of("error", "Customer not found"))
                        .build();
            }

            Optional<CustomerTagDTO> tagDto = customerTagService.findById(tagId, shop);
            if (tagDto.isEmpty()) {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity(Map.of("error", "Tag not found"))
                        .build();
            }

            CustomerTag tag = CustomerTag.findById(tagId);
            customer.addTag(tag);
            customerRepository.persist(customer);

            return Response.ok(Map.of("message", "Tag added successfully")).build();

        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity(Map.of("error", "Failed to add tag: " + e.getMessage()))
                    .build();
        }
    }

    /**
     * Remove tag from customer
     */
    @DELETE
    @Path("/{id}/tags/{tagId}")
    @Transactional
    public Response removeTagFromCustomer(
            @PathParam("id") Long customerId,
            @PathParam("tagId") Long tagId) {
        try {
            Shop shop = shopContext.getShop();
            Customer customer = customerRepository.findByIdAndShop(customerId, shop);

            if (customer == null) {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity(Map.of("error", "Customer not found"))
                        .build();
            }

            CustomerTag tag = CustomerTag.findById(tagId);
            if (tag == null || !tag.shop.id.equals(shop.id)) {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity(Map.of("error", "Tag not found"))
                        .build();
            }

            customer.removeTag(tag);
            customerRepository.persist(customer);

            return Response.ok(Map.of("message", "Tag removed successfully")).build();

        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity(Map.of("error", "Failed to remove tag: " + e.getMessage()))
                    .build();
        }
    }

    /**
     * Update customer tags (replace all tags)
     */
    @PUT
    @Path("/{id}/tags")
    @Transactional
    public Response updateCustomerTags(
            @PathParam("id") Long customerId,
            List<Long> tagIds) {
        try {
            Shop shop = shopContext.getShop();
            Customer customer = customerRepository.findByIdAndShop(customerId, shop);

            if (customer == null) {
                return Response.status(Response.Status.NOT_FOUND)
                        .entity(Map.of("error", "Customer not found"))
                        .build();
            }

            // Clear existing tags
            customer.clearTags();

            // Add new tags
            for (Long tagId : tagIds) {
                Optional<CustomerTagDTO> tagDto = customerTagService.findById(tagId, shop);
                if (tagDto.isPresent()) {
                    CustomerTag tag = CustomerTag.findById(tagId);
                    customer.addTag(tag);
                }
            }

            customerRepository.persist(customer);

            List<CustomerTagDTO> updatedTags = customer.tags.stream()
                    .map(CustomerTagMapper::toDto)
                    .collect(java.util.stream.Collectors.toList());

            return Response.ok(Map.of("data", updatedTags)).build();

        } catch (Exception e) {
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                    .entity(Map.of("error", "Failed to update tags: " + e.getMessage()))
                    .build();
        }
    }
}
