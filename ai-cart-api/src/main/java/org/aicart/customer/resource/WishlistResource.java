package org.aicart.customer.resource;

import io.quarkus.security.Authenticated;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.SecurityContext;
import org.aicart.customer.dto.WishlistRequestDTO;
import org.aicart.customer.dto.WishlistResponseDTO;
import org.aicart.customer.entity.Customer;
import org.aicart.customer.service.WishlistService;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.common.response.ApiResponse;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.util.List;
import java.util.Map;

@Path("/customers/wishlist")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Authenticated
@RequireShopId
public class WishlistResource {

    @Inject
    JsonWebToken jwt;

    @Context
    SecurityContext securityContext;

    @Inject
    WishlistService wishlistService;

    private Customer getCurrentCustomer() {
        if (jwt.getSubject() == null) {
            return null;
        }
        return Customer.findById(Long.valueOf(jwt.getSubject()));
    }

    @POST
    public Response addToWishlist(@Valid WishlistRequestDTO request) {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        boolean added;
        if (request.variantId != null) {
            // Use variant-based wishlist (preferred)
            added = wishlistService.addToWishlist(customer, request.variantId);
        } else if (request.productId != null) {
            // Fallback to product-based wishlist for backward compatibility
            added = wishlistService.addToWishlistByProduct(customer, request.productId);
        } else {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(Map.of("message", "Either variantId or productId is required"))
                    .build();
        }

        if (!added) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(Map.of("message", "Item already in wishlist or not found"))
                    .build();
        }

        return Response.ok(Map.of("message", "Item added to wishlist")).build();
    }

    @DELETE
    @Path("/{variantId}")
    public Response removeFromWishlist(@PathParam("variantId") Long variantId) {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        boolean removed = wishlistService.removeFromWishlist(customer, variantId);
        if (!removed) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity(Map.of("message", "Item not found in wishlist"))
                    .build();
        }

        return Response.ok(Map.of("message", "Item removed from wishlist")).build();
    }

    // Keep backward compatibility endpoint for product-based removal
    @DELETE
    @Path("/product/{productId}")
    public Response removeFromWishlistByProduct(@PathParam("productId") Long productId) {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        boolean removed = wishlistService.removeFromWishlistByProduct(customer, productId);
        if (!removed) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity(Map.of("message", "Product not found in wishlist"))
                    .build();
        }

        return Response.ok(Map.of("message", "Product removed from wishlist")).build();
    }

    @GET
    public Response getWishlist(
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("20") int size) {
        
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        WishlistResponseDTO response = wishlistService.getWishlist(customer, page, size);
        return Response.ok(response).build();
    }

    @GET
    @Path("/check/{variantId}")
    public Response checkWishlist(@PathParam("variantId") Long variantId) {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        boolean inWishlist = wishlistService.isInWishlist(customer, variantId);
        return Response.ok(Map.of("inWishlist", inWishlist)).build();
    }

    // Keep backward compatibility endpoint for product-based check
    @GET
    @Path("/check/product/{productId}")
    public Response checkWishlistByProduct(@PathParam("productId") Long productId) {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        boolean inWishlist = wishlistService.isInWishlistByProduct(customer, productId);
        return Response.ok(Map.of("inWishlist", inWishlist)).build();
    }

    @GET
    @Path("/count")
    public Response getWishlistCount() {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        long count = wishlistService.getWishlistCount(customer);
        return Response.ok(Map.of("count", count)).build();
    }

    @GET
    @Path("/product-ids")
    public Response getWishlistProductIds() {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        List<Long> productIds = wishlistService.getWishlistProductIds(customer);
        return Response.ok(Map.of("productIds", productIds)).build();
    }

    @GET
    @Path("/variant-ids")
    public Response getWishlistVariantIds() {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        List<Long> variantIds = wishlistService.getWishlistVariantIds(customer);
        return Response.ok(Map.of("variantIds", variantIds)).build();
    }
}
