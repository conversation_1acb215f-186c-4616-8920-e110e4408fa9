package org.aicart.store.customer.dto;

import jakarta.validation.constraints.NotNull;

public class ProductCompareRequestDTO {
    
    @NotNull(message = "Variant ID is required")
    public Long variantId;

    // Keep productId for backward compatibility
    public Long productId;

    public ProductCompareRequestDTO() {}

    public ProductCompareRequestDTO(Long variantId) {
        this.variantId = variantId;
    }

    public ProductCompareRequestDTO(Long variantId, Long productId) {
        this.variantId = variantId;
        this.productId = productId;
    }
}
