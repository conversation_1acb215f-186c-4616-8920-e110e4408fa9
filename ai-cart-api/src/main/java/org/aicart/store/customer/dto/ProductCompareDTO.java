package org.aicart.store.customer.dto;

import org.aicart.store.customer.entity.ProductCompare;
import org.aicart.store.product.dto.ProductItemDTO;
import org.aicart.store.product.dto.ProductVariantDTO;

import java.time.LocalDateTime;

public class ProductCompareDTO {
    public Long id;
    public ProductItemDTO product;
    public ProductVariantDTO variant;
    public LocalDateTime createdAt;
    public LocalDateTime updatedAt;

    public static ProductCompareDTO fromEntity(ProductCompare compare, ProductItemDTO productDTO, ProductVariantDTO variantDTO) {
        ProductCompareDTO dto = new ProductCompareDTO();
        dto.id = compare.id;
        dto.product = productDTO;
        dto.variant = variantDTO;
        dto.createdAt = compare.createdAt;
        dto.updatedAt = compare.updatedAt;
        return dto;
    }

    // Keep backward compatibility method
    public static ProductCompareDTO fromEntity(ProductCompare compare, ProductItemDTO productDTO) {
        ProductCompareDTO dto = new ProductCompareDTO();
        dto.id = compare.id;
        dto.product = productDTO;
        dto.variant = null; // For backward compatibility
        dto.createdAt = compare.createdAt;
        dto.updatedAt = compare.updatedAt;
        return dto;
    }
}
