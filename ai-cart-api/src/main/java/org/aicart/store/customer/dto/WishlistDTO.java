package org.aicart.store.customer.dto;

import org.aicart.store.customer.entity.Wishlist;
import org.aicart.store.product.dto.ProductItemDTO;
import org.aicart.store.product.dto.ProductVariantDTO;

import java.time.LocalDateTime;

public class WishlistDTO {
    public Long id;
    public ProductItemDTO product;
    public ProductVariantDTO variant;
    public LocalDateTime createdAt;
    public LocalDateTime updatedAt;

    public static WishlistDTO fromEntity(Wishlist wishlist, ProductItemDTO productDTO, ProductVariantDTO variantDTO) {
        WishlistDTO dto = new WishlistDTO();
        dto.id = wishlist.id;
        dto.product = productDTO;
        dto.variant = variantDTO;
        dto.createdAt = wishlist.createdAt;
        dto.updatedAt = wishlist.updatedAt;
        return dto;
    }

    // Keep backward compatibility method
    public static WishlistDTO fromEntity(Wishlist wishlist, ProductItemDTO productDTO) {
        WishlistDTO dto = new WishlistDTO();
        dto.id = wishlist.id;
        dto.product = productDTO;
        dto.variant = null; // For backward compatibility
        dto.createdAt = wishlist.createdAt;
        dto.updatedAt = wishlist.updatedAt;
        return dto;
    }
}
