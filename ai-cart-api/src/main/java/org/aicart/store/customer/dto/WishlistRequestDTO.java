package org.aicart.store.customer.dto;

import jakarta.validation.constraints.NotNull;

public class WishlistRequestDTO {

    @NotNull(message = "Variant ID is required")
    public Long variantId;

    // Keep productId for backward compatibility
    public Long productId;

    public WishlistRequestDTO() {}

    public WishlistRequestDTO(Long variantId) {
        this.variantId = variantId;
    }

    public WishlistRequestDTO(Long variantId, Long productId) {
        this.variantId = variantId;
        this.productId = productId;
    }
}
