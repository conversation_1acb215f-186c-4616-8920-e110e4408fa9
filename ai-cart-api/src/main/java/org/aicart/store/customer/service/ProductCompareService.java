package org.aicart.store.customer.service;

import io.quarkus.panache.common.Page;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.aicart.store.context.ShopContext;
import org.aicart.store.customer.dto.ProductCompareDTO;
import org.aicart.store.customer.dto.ProductCompareResponseDTO;
import org.aicart.store.customer.entity.Customer;
import org.aicart.store.customer.entity.ProductCompare;
import org.aicart.store.product.ProductService;
import org.aicart.store.product.dto.ProductItemDTO;
import org.aicart.store.product.dto.ProductVariantDTO;
import org.aicart.store.product.entity.Product;
import org.aicart.store.product.entity.ProductVariant;
import org.aicart.store.user.entity.Shop;

import java.util.List;
import java.util.stream.Collectors;

@ApplicationScoped
public class ProductCompareService {

    @Inject
    ShopContext shopContext;

    @Inject
    ProductService productService;

    @Transactional
    public boolean addToCompare(Customer customer, Long variantId) {
        Shop shop = Shop.findById(shopContext.getShopId());
        if (shop == null) {
            return false;
        }

        ProductVariant variant = ProductVariant.findById(variantId);
        if (variant == null || !variant.product.shop.id.equals(shop.id)) {
            return false;
        }

        // Check if already exists
        if (ProductCompare.existsByCustomerAndVariant(customer, variant)) {
            return false; // Already in compare list
        }

        // Check compare limit (typically 3-4 products)
        long currentCount = ProductCompare.countByCustomerAndShop(customer, shop);
        if (currentCount >= 4) {
            return false; // Compare list is full
        }

        ProductCompare compare = new ProductCompare();
        compare.customer = customer;
        compare.product = variant.product;
        compare.variant = variant;
        compare.shop = shop;
        compare.persist();

        return true;
    }

    // Keep backward compatibility method for product-level compare
    @Transactional
    public boolean addToCompareByProduct(Customer customer, Long productId) {
        Shop shop = Shop.findById(shopContext.getShopId());
        if (shop == null) {
            return false;
        }

        Product product = Product.findById(productId);
        if (product == null || !product.shop.id.equals(shop.id)) {
            return false;
        }

        // Get the first variant of the product
        ProductVariant firstVariant = ProductVariant.find("product = ?1", product).firstResult();
        if (firstVariant == null) {
            return false;
        }

        return addToCompare(customer, firstVariant.id);
    }

    @Transactional
    public boolean removeFromCompare(Customer customer, Long variantId) {
        ProductVariant variant = ProductVariant.findById(variantId);
        if (variant == null) {
            return false;
        }

        ProductCompare compare = ProductCompare.findByCustomerAndVariant(customer, variant);
        if (compare == null) {
            return false; // Not in compare list
        }

        compare.delete();
        return true;
    }

    // Keep backward compatibility method for product-level removal
    @Transactional
    public boolean removeFromCompareByProduct(Customer customer, Long productId) {
        Product product = Product.findById(productId);
        if (product == null) {
            return false;
        }

        ProductCompare compare = ProductCompare.findByCustomerAndProduct(customer, product);
        if (compare == null) {
            return false; // Not in compare list
        }

        compare.delete();
        return true;
    }

    public ProductCompareResponseDTO getCompareList(Customer customer, int page, int size) {
        Shop shop = Shop.findById(shopContext.getShopId());
        if (shop == null) {
            return new ProductCompareResponseDTO(List.of(), 0, page, size);
        }

        List<ProductCompare> compares = ProductCompare.find("customer = ?1 and shop = ?2 order by createdAt desc", customer, shop)
                .page(Page.of(page, size))
                .list();

        long totalCount = ProductCompare.count("customer = ?1 and shop = ?2", customer, shop);

        List<ProductCompareDTO> compareDTOs = compares.stream()
                .map(compare -> {
                    ProductItemDTO productDTO = productService.getProductById(compare.product.id);
                    ProductVariantDTO variantDTO = convertVariantToDTO(compare.variant);
                    return ProductCompareDTO.fromEntity(compare, productDTO, variantDTO);
                })
                .collect(Collectors.toList());

        return new ProductCompareResponseDTO(compareDTOs, totalCount, page, size);
    }

    public boolean isInCompare(Customer customer, Long variantId) {
        ProductVariant variant = ProductVariant.findById(variantId);
        if (variant == null) {
            return false;
        }
        return ProductCompare.existsByCustomerAndVariant(customer, variant);
    }

    // Keep backward compatibility method for product-level check
    public boolean isInCompareByProduct(Customer customer, Long productId) {
        Product product = Product.findById(productId);
        if (product == null) {
            return false;
        }
        return ProductCompare.existsByCustomerAndProduct(customer, product);
    }

    public long getCompareCount(Customer customer) {
        Shop shop = Shop.findById(shopContext.getShopId());
        if (shop == null) {
            return 0;
        }
        return ProductCompare.countByCustomerAndShop(customer, shop);
    }

    @Transactional
    public boolean clearCompareList(Customer customer) {
        Shop shop = Shop.findById(shopContext.getShopId());
        if (shop == null) {
            return false;
        }

        long deletedCount = ProductCompare.delete("customer = ?1 and shop = ?2", customer, shop);
        return deletedCount > 0;
    }

    public List<Long> getCompareVariantIds(Customer customer) {
        Shop shop = Shop.findById(shopContext.getShopId());
        if (shop == null) {
            return List.of();
        }

        List<ProductCompare> compares = ProductCompare.find("customer = ?1 and shop = ?2", customer, shop).list();
        return compares.stream()
                .map(compare -> compare.variant.id)
                .collect(Collectors.toList());
    }

    // Helper method to convert ProductVariant entity to ProductVariantDTO
    private ProductVariantDTO convertVariantToDTO(ProductVariant variant) {
        if (variant == null) {
            return null;
        }

        ProductVariantDTO dto = new ProductVariantDTO();
        dto.setId(variant.id);
        dto.setSku(variant.sku);
        dto.setImageId(variant.imageId);
        
        // Note: For now, we're setting basic fields.
        // In a full implementation, you'd also convert prices, stock, and attributes
        // This can be enhanced later with proper price and stock information

        return dto;
    }

    public List<Long> getCompareProductIds(Customer customer) {
        Shop shop = Shop.findById(shopContext.getShopId());
        if (shop == null) {
            return List.of();
        }

        List<ProductCompare> compares = ProductCompare.find("customer = ?1 and shop = ?2", customer, shop).list();
        return compares.stream()
                .map(compare -> compare.product.id)
                .collect(Collectors.toList());
    }
}
