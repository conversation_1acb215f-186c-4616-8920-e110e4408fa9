package org.aicart.store.customer.dto;

import java.util.List;

public class ProductCompareResponseDTO {
    public List<ProductCompareDTO> items;
    public long totalCount;
    public int page;
    public int size;
    public boolean hasMore;

    public ProductCompareResponseDTO() {}

    public ProductCompareResponseDTO(List<ProductCompareDTO> items, long totalCount, int page, int size) {
        this.items = items;
        this.totalCount = totalCount;
        this.page = page;
        this.size = size;
        this.hasMore = (page + 1) * size < totalCount;
    }
}
