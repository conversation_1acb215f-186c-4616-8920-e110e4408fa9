package org.aicart.store.customer;

import io.quarkus.security.Authenticated;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.SecurityContext;
import org.aicart.store.customer.dto.ProductCompareRequestDTO;
import org.aicart.store.customer.dto.ProductCompareResponseDTO;
import org.aicart.store.customer.entity.Customer;
import org.aicart.store.customer.service.ProductCompareService;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.util.List;
import java.util.Map;

@Path("/customers/compare")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Authenticated
public class ProductCompareResource {

    @Inject
    JsonWebToken jwt;

    @Context
    SecurityContext securityContext;

    @Inject
    ProductCompareService compareService;

    private Customer getCurrentCustomer() {
        if (jwt.getSubject() == null) {
            return null;
        }
        return Customer.findById(Long.valueOf(jwt.getSubject()));
    }

    @POST
    public Response addToCompare(@Valid ProductCompareRequestDTO request) {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        boolean added;
        if (request.variantId != null) {
            // Use variant-based compare (preferred)
            added = compareService.addToCompare(customer, request.variantId);
        } else if (request.productId != null) {
            // Fallback to product-based compare for backward compatibility
            added = compareService.addToCompareByProduct(customer, request.productId);
        } else {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(Map.of("message", "Either variantId or productId is required"))
                    .build();
        }

        if (!added) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(Map.of("message", "Item already in compare list, not found, or compare list is full (max 4 items)"))
                    .build();
        }

        return Response.ok(Map.of("message", "Item added to compare list")).build();
    }

    @DELETE
    @Path("/{variantId}")
    public Response removeFromCompare(@PathParam("variantId") Long variantId) {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        boolean removed = compareService.removeFromCompare(customer, variantId);
        if (!removed) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity(Map.of("message", "Item not found in compare list"))
                    .build();
        }

        return Response.ok(Map.of("message", "Item removed from compare list")).build();
    }

    // Keep backward compatibility endpoint for product-based removal
    @DELETE
    @Path("/product/{productId}")
    public Response removeFromCompareByProduct(@PathParam("productId") Long productId) {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        boolean removed = compareService.removeFromCompareByProduct(customer, productId);
        if (!removed) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity(Map.of("message", "Product not found in compare list"))
                    .build();
        }

        return Response.ok(Map.of("message", "Product removed from compare list")).build();
    }

    @GET
    public Response getCompareList(
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("20") int size) {
        
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        ProductCompareResponseDTO response = compareService.getCompareList(customer, page, size);
        return Response.ok(response).build();
    }

    @GET
    @Path("/check/{variantId}")
    public Response checkCompare(@PathParam("variantId") Long variantId) {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        boolean inCompare = compareService.isInCompare(customer, variantId);
        return Response.ok(Map.of("inCompare", inCompare)).build();
    }

    // Keep backward compatibility endpoint for product-based check
    @GET
    @Path("/check/product/{productId}")
    public Response checkCompareByProduct(@PathParam("productId") Long productId) {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        boolean inCompare = compareService.isInCompareByProduct(customer, productId);
        return Response.ok(Map.of("inCompare", inCompare)).build();
    }

    @GET
    @Path("/count")
    public Response getCompareCount() {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        long count = compareService.getCompareCount(customer);
        return Response.ok(Map.of("count", count)).build();
    }

    @DELETE
    @Path("/clear")
    public Response clearCompareList() {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        boolean cleared = compareService.clearCompareList(customer);
        if (!cleared) {
            return Response.status(Response.Status.NOT_FOUND)
                    .entity(Map.of("message", "Compare list is already empty"))
                    .build();
        }

        return Response.ok(Map.of("message", "Compare list cleared")).build();
    }

    @GET
    @Path("/variant-ids")
    public Response getCompareVariantIds() {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        List<Long> variantIds = compareService.getCompareVariantIds(customer);
        return Response.ok(Map.of("variantIds", variantIds)).build();
    }

    @GET
    @Path("/product-ids")
    public Response getCompareProductIds() {
        Customer customer = getCurrentCustomer();
        if (customer == null) {
            return Response.status(Response.Status.UNAUTHORIZED)
                    .entity(Map.of("message", "Authentication required"))
                    .build();
        }

        List<Long> productIds = compareService.getCompareProductIds(customer);
        return Response.ok(Map.of("productIds", productIds)).build();
    }
}
