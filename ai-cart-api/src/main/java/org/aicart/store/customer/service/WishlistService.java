package org.aicart.store.customer.service;

import io.quarkus.panache.common.Page;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.aicart.store.customer.dto.WishlistDTO;
import org.aicart.store.customer.dto.WishlistResponseDTO;
import org.aicart.store.customer.entity.Customer;
import org.aicart.store.customer.entity.Wishlist;
import org.aicart.store.product.ProductService;
import org.aicart.store.product.dto.ProductItemDTO;
import org.aicart.store.product.dto.ProductVariantDTO;
import org.aicart.store.product.entity.Product;
import org.aicart.store.product.entity.ProductVariant;
import org.aicart.store.user.entity.Shop;
import org.aicart.store.context.ShopContext;

import java.util.List;
import java.util.stream.Collectors;

@ApplicationScoped
public class WishlistService {

    @Inject
    ShopContext shopContext;

    @Inject
    ProductService productService;

    @Transactional
    public boolean addToWishlist(Customer customer, Long variantId) {
        Shop shop = Shop.findById(shopContext.getShopId());
        if (shop == null) {
            return false;
        }

        ProductVariant variant = ProductVariant.findById(variantId);
        if (variant == null || !variant.product.shop.id.equals(shop.id)) {
            return false;
        }

        // Check if already exists
        if (Wishlist.existsByCustomerAndVariant(customer, variant)) {
            return false; // Already in wishlist
        }

        Wishlist wishlist = new Wishlist();
        wishlist.customer = customer;
        wishlist.product = variant.product;
        wishlist.variant = variant;
        wishlist.shop = shop;
        wishlist.persist();

        return true;
    }

    // Keep backward compatibility method for product-level wishlist
    @Transactional
    public boolean addToWishlistByProduct(Customer customer, Long productId) {
        Shop shop = Shop.findById(shopContext.getShopId());
        if (shop == null) {
            return false;
        }

        Product product = Product.findById(productId);
        if (product == null || !product.shop.id.equals(shop.id)) {
            return false;
        }

        // Get the first variant of the product
        ProductVariant firstVariant = ProductVariant.find("product = ?1", product).firstResult();
        if (firstVariant == null) {
            return false;
        }

        return addToWishlist(customer, firstVariant.id);
    }

    @Transactional
    public boolean removeFromWishlist(Customer customer, Long variantId) {
        ProductVariant variant = ProductVariant.findById(variantId);
        if (variant == null) {
            return false;
        }

        Wishlist wishlist = Wishlist.findByCustomerAndVariant(customer, variant);
        if (wishlist == null) {
            return false; // Not in wishlist
        }

        wishlist.delete();
        return true;
    }

    // Keep backward compatibility method for product-level removal
    @Transactional
    public boolean removeFromWishlistByProduct(Customer customer, Long productId) {
        Product product = Product.findById(productId);
        if (product == null) {
            return false;
        }

        Wishlist wishlist = Wishlist.findByCustomerAndProduct(customer, product);
        if (wishlist == null) {
            return false;
        }

        wishlist.delete();
        return true;
    }

    public WishlistResponseDTO getWishlist(Customer customer, int page, int size) {
        Shop shop = Shop.findById(shopContext.getShopId());
        if (shop == null) {
            return new WishlistResponseDTO(List.of(), 0, page, size);
        }

        List<Wishlist> wishlists = Wishlist.find("customer = ?1 and shop = ?2 order by createdAt desc", customer, shop)
                .page(Page.of(page, size))
                .list();

        long totalCount = Wishlist.count("customer = ?1 and shop = ?2", customer, shop);

        List<WishlistDTO> wishlistDTOs = wishlists.stream()
                .map(wishlist -> {
                    ProductItemDTO productDTO = productService.getProductById(wishlist.product.id);
                    ProductVariantDTO variantDTO = convertVariantToDTO(wishlist.variant);
                    return WishlistDTO.fromEntity(wishlist, productDTO, variantDTO);
                })
                .collect(Collectors.toList());

        return new WishlistResponseDTO(wishlistDTOs, totalCount, page, size);
    }

    public boolean isInWishlist(Customer customer, Long variantId) {
        ProductVariant variant = ProductVariant.findById(variantId);
        if (variant == null) {
            return false;
        }
        return Wishlist.existsByCustomerAndVariant(customer, variant);
    }

    // Keep backward compatibility method for product-level check
    public boolean isInWishlistByProduct(Customer customer, Long productId) {
        Product product = Product.findById(productId);
        if (product == null) {
            return false;
        }
        return Wishlist.existsByCustomerAndProduct(customer, product);
    }

    public long getWishlistCount(Customer customer) {
        return Wishlist.countByCustomer(customer);
    }

    public List<Long> getWishlistProductIds(Customer customer) {
        Shop shop = Shop.findById(shopContext.getShopId());
        if (shop == null) {
            return List.of();
        }

        return Wishlist.find("customer = ?1 and shop = ?2", customer, shop)
                .project(Long.class)
                .list();
    }

    public List<Long> getWishlistVariantIds(Customer customer) {
        Shop shop = Shop.findById(shopContext.getShopId());
        if (shop == null) {
            return List.of();
        }

        List<Wishlist> wishlists = Wishlist.find("customer = ?1 and shop = ?2", customer, shop).list();
        return wishlists.stream()
                .map(wishlist -> wishlist.variant.id)
                .collect(Collectors.toList());
    }

    // Helper method to convert ProductVariant entity to ProductVariantDTO
    private ProductVariantDTO convertVariantToDTO(ProductVariant variant) {
        if (variant == null) {
            return null;
        }

        ProductVariantDTO dto = new ProductVariantDTO();
        dto.setId(variant.id);
        dto.setSku(variant.sku);
        dto.setImageId(variant.imageId);

        // Note: For now, we're setting basic fields.
        // In a full implementation, you'd also convert prices, stock, and attributes
        // This can be enhanced later with proper price and stock information

        return dto;
    }
}
