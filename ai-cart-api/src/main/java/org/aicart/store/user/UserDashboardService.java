package org.aicart.store.user;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

@ApplicationScoped
public class UserDashboardService {

    @Inject
    EntityManager entityManager;

    public Map<String, Object> getUserStats(Long userId) {
        String sql = """
            SELECT 
              COUNT(*) AS total_orders,
              SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) AS pending_orders,
              COALESCE(SUM(total_price), 0) AS total_spent,
              0 AS reward_points
            FROM orders
            WHERE customer_id = :userId
        """;

        try {
            Object[] result = (Object[]) entityManager.createNativeQuery(sql)
                    .setParameter("userId", userId)
                    .getSingleResult();

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalOrders", ((Number) result[0]).intValue());
            stats.put("pendingOrder", ((Number) result[1]).intValue());
            stats.put("spend", ((Number) result[2]).longValue());
            stats.put("rewards", ((Number) result[3]).intValue());

            return stats;
        } catch (Exception e) {
            Map<String, Object> defaultStats = new HashMap<>();
            defaultStats.put("totalOrders", 0);
            defaultStats.put("pendingOrder", 0);
            defaultStats.put("spend", 0L);
            defaultStats.put("rewards", 0);
            return defaultStats;
        }
    }

    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getRecentOrders(Long userId) {
        String sql = """
            SELECT 
              id, 
              created_at AS date,
              status,
              total_price AS total
            FROM orders
            WHERE customer_id = :userId
            ORDER BY id DESC
            LIMIT 5
        """;

        List<Object[]> results = entityManager.createNativeQuery(sql)
                .setParameter("userId", userId)
                .getResultList();

        return results.stream().map(row -> {
            Map<String, Object> order = new HashMap<>();
            order.put("id", ((Number) row[0]).longValue());
            order.put("date", row[1]);
            order.put("status", ((Number) row[2]).intValue());
            order.put("total", ((Number) row[3]).longValue());
            return order;
        }).collect(java.util.stream.Collectors.toList());
    }

    public Map<String, Object> getOrderDetail(Long userId, Long orderId) {
        String sql = """
            SELECT 
              o.id AS id, 
              o.created_at AS date,
              o.status,
              o.total_price AS total,
              o.currency,
              
              CASE 
                WHEN ob.order_id IS NOT NULL THEN
                  jsonb_build_object(
                    'city', ob.city,
                    'country', ob.country,
                    'fullName', ob.full_name,
                    'line1', ob.line1,
                    'line2', ob.line2,
                    'postalCode', ob.postal_code,
                    'state', ob.state,
                    'tax_number', ob.tax_number,
                    'vat_number', ob.vat_number
                  )
              END AS billing,
              
              CASE
                WHEN os.order_id IS NOT NULL THEN
                  jsonb_build_object(
                    'city', os.city,
                    'country', os.country,
                    'fullName', os.full_name,
                    'line1', os.line1,
                    'line2', os.line2,
                    'postalCode', os.postal_code,
                    'state', os.state
                  )
              END AS shipping
              
            FROM orders o
            LEFT JOIN order_billing ob ON o.id = ob.order_id
            LEFT JOIN order_shipping os ON o.id = os.order_id
            WHERE o.customer_id = :userId
            AND o.id = :orderId
            LIMIT 1
        """;

        try {
            Object[] result = (Object[]) entityManager.createNativeQuery(sql)
                    .setParameter("userId", userId)
                    .setParameter("orderId", orderId)
                    .getSingleResult();

            Map<String, Object> order = new HashMap<>();
            order.put("id", ((Number) result[0]).longValue());
            order.put("date", result[1]);
            order.put("status", ((Number) result[2]).intValue());
            order.put("total", ((Number) result[3]).longValue());
            order.put("currency", result[4]);
            order.put("billing", result[5]);
            order.put("shipping", result[6]);

            return order;
        } catch (Exception e) {
            return null;
        }
    }

    public Map<String, Object> getUserBilling(Long userId) {
        String sql = """
            SELECT
              ub.id,
              ub.city,
              ub.country,
              ub.created_at,
              ub.email,
              ub.full_name AS fullName,
              ub.line1,
              ub.line2,
              ub.phone,
              ub.postal_code AS postalCode,
              ub.state,
              ub.tax_number AS taxNumber,
              ub.updated_at,
              ub.user_id,
              ub.vat_number AS vatNumber
            FROM user_billing ub 
            WHERE ub.user_id = :userId
            LIMIT 1
        """;

        try {
            Object[] result = (Object[]) entityManager.createNativeQuery(sql)
                    .setParameter("userId", userId)
                    .getSingleResult();

            Map<String, Object> billing = new HashMap<>();
            billing.put("id", ((Number) result[0]).longValue());
            billing.put("city", result[1]);
            billing.put("country", result[2]);
            billing.put("created_at", result[3]);
            billing.put("email", result[4]);
            billing.put("fullName", result[5]);
            billing.put("line1", result[6]);
            billing.put("line2", result[7]);
            billing.put("phone", result[8]);
            billing.put("postalCode", result[9]);
            billing.put("state", result[10]);
            billing.put("taxNumber", result[11]);
            billing.put("updated_at", result[12]);
            billing.put("user_id", ((Number) result[13]).longValue());
            billing.put("vatNumber", result[14]);

            return billing;
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    public Map<String, Object> getUserShipping(Long userId) {
        String sql = """
            SELECT
              us.id,
              us.city,
              us.country,
              us.created_at,
              us.full_name AS fullName,
              us.line1,
              us.line2,
              us.phone,
              us.postal_code AS postalCode,
              us.state,
              us.updated_at,
              us.user_id 
            FROM user_shipping us 
            WHERE us.user_id = :userId 
            LIMIT 1
        """;

        try {
            Object[] result = (Object[]) entityManager.createNativeQuery(sql)
                    .setParameter("userId", userId)
                    .getSingleResult();

            Map<String, Object> shipping = new HashMap<>();
            shipping.put("id", ((Number) result[0]).longValue());
            shipping.put("city", result[1]);
            shipping.put("country", result[2]);
            shipping.put("created_at", result[3]);
            shipping.put("fullName", result[4]);
            shipping.put("line1", result[5]);
            shipping.put("line2", result[6]);
            shipping.put("phone", result[7]);
            shipping.put("postalCode", result[8]);
            shipping.put("state", result[9]);
            shipping.put("updated_at", result[10]);
            shipping.put("user_id", ((Number) result[11]).longValue());

            return shipping;
        } catch (Exception e) {
            return new HashMap<>();
        }
    }

    public Map<String, Object> getUserProfile(Long userId) {
        String sql = """
            SELECT
              u.id,
              u.name,
              u.email,
              u.verified_at AS verifiedAt
            FROM users u 
            WHERE u.id = :userId 
            LIMIT 1
        """;

        try {
            Object[] result = (Object[]) entityManager.createNativeQuery(sql)
                    .setParameter("userId", userId)
                    .getSingleResult();

            Map<String, Object> profile = new HashMap<>();
            profile.put("id", ((Number) result[0]).longValue());
            profile.put("name", result[1]);
            profile.put("email", result[2]);
            profile.put("verifiedAt", result[3]);

            return profile;
        } catch (Exception e) {
            return null;
        }
    }

    public boolean getPasswordTokenValidity(String token) {
        String sql = """
            SELECT p.expired_at
            FROM password_resets p
            WHERE p.expired_at >= :currentTime
            AND p.token = :token
            ORDER BY p.expired_at DESC
            LIMIT 1
        """;

        try {
            long currentTimestamp = System.currentTimeMillis() / 1000; // Unix timestamp
            Object result = entityManager.createNativeQuery(sql)
                    .setParameter("currentTime", currentTimestamp)
                    .setParameter("token", token)
                    .getSingleResult();

            return result != null;
        } catch (Exception e) {
            return false;
        }
    }
}
