package org.aicart.store.shop;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

@ApplicationScoped
public class ShopService {

    @Inject
    EntityManager entityManager;

    @SuppressWarnings("unchecked")
    public Map<String, Object> getShopDataByHost(String host, String countryCode) {
        String sql = """
            SELECT
                s.id,
                s.name,
                s.tag_line,
                sts.favicon_url,
                sts.support_phone,
                sts.support_email,
                sts.top_content_1,
                sts.footer_content,
                sts.footer_widget1,
                sts.footer_widget2,
                sts.footer_widget3,
                sts.footer_widget4,
                sts.footer_widget5,
                sts.social_links,
                sts.favicon_storage_location,
                sts.logo_url,
                sts.logo_storage_location,
                c2.code AS countryCode,
                c.code AS currencyCode,
                c2.id AS countryId
            FROM shops s
                    JOIN shop_country sc ON s.id = sc.shop_id
                    JOIN currencies c ON s.currency_id = c.id
                    JOIN countries c2 ON c2.id = sc.country_id
                    LEFT JOIN (
                SELECT
                    sts.shop_id,
                    fs1.original_url AS favicon_url,
                    fs1.storage_location AS favicon_storage_location,
                    fs2.original_url AS logo_url,
                    fs2.storage_location AS logo_storage_location,
                    sts.support_phone,
                    sts.support_email,
                    sts.top_content_1,
                    sts.footer_content,
                    sts.footer_widget1,
                    sts.footer_widget2,
                    sts.footer_widget3,
                    sts.footer_widget4,
                    sts.footer_widget5,
                    sts.social_links
                FROM shop_theme_settings sts
                        LEFT JOIN file_storage fs1 ON fs1.id = sts.favicon_id
                        LEFT JOIN file_storage fs2 ON fs2.id = sts.logo_id
            ) sts ON sts.shop_id = s.id
            WHERE
                s.host = :host
                AND (c2.code = :countryCode OR c2.id = s.primary_country)
            LIMIT 1
        """;

        try {
            Object[] result = (Object[]) entityManager.createNativeQuery(sql)
                    .setParameter("host", host)
                    .setParameter("countryCode", countryCode)
                    .getSingleResult();

            Map<String, Object> shop = new HashMap<>();
            shop.put("id", ((Number) result[0]).longValue());
            shop.put("name", result[1]);
            shop.put("description", result[2]);
            shop.put("favicon_url", result[3] != null && result[14] != null ? 
                result[14] + "/" + result[3] : null);
            shop.put("support_phone", result[4]);
            shop.put("support_email", result[5]);
            shop.put("top_content_1", result[6]);
            shop.put("footer_content", result[7]);
            shop.put("footer_widget1", result[8]);
            shop.put("footer_widget2", result[9]);
            shop.put("footer_widget3", result[10]);
            shop.put("footer_widget4", result[11]);
            shop.put("footer_widget5", result[12]);
            shop.put("social_links", result[13]);
            shop.put("logo_url", result[15] != null && result[16] != null ? 
                result[16] + "/" + result[15] : null);
            shop.put("countryCode", result[17]);
            shop.put("currencyCode", result[18]);
            shop.put("countryId", ((Number) result[19]).longValue());

            return shop;
        } catch (Exception e) {
            return null;
        }
    }

    public Map<String, Object> getShopThemeData(Long shopId) {
        String sql = """
            SELECT
                s.id,
                sts.header,
                sts.footer,
                sts.home_page,
                sts.sections
            FROM shops s
                    JOIN shop_country sc ON s.id = sc.shop_id
                    JOIN currencies c ON s.currency_id = c.id
                    JOIN countries c2 ON c2.id = sc.country_id
                    LEFT JOIN (
                SELECT
                    sts.shop_id,
                    sts.header,
                    sts.footer,
                    sts.home_page,
                    sts.sections
                FROM shop_theme_settings sts
                        LEFT JOIN file_storage fs1 ON fs1.id = sts.favicon_id
                        LEFT JOIN file_storage fs2 ON fs2.id = sts.logo_id
            ) sts ON sts.shop_id = s.id
            WHERE
                s.id = :shopId
            LIMIT 1
        """;

        try {
            Object[] result = (Object[]) entityManager.createNativeQuery(sql)
                    .setParameter("shopId", shopId)
                    .getSingleResult();

            Map<String, Object> theme = new HashMap<>();
            theme.put("id", ((Number) result[0]).longValue());
            theme.put("header", result[1]);
            theme.put("footer", result[2]);
            theme.put("home_page", result[3]);
            theme.put("sections", result[4]);

            return theme;
        } catch (Exception e) {
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getShopHighlights(Long shopId) {
        String sql = """
            SELECT id,
                icon,
                title,
                description
            FROM shop_highlights
            WHERE is_active = TRUE
            AND shop_id = :shopId
            ORDER BY score ASC, id DESC
            LIMIT 3
        """;

        List<Object[]> results = entityManager.createNativeQuery(sql)
                .setParameter("shopId", shopId)
                .getResultList();

        return results.stream().map(row -> {
            Map<String, Object> highlight = new HashMap<>();
            highlight.put("id", ((Number) row[0]).longValue());
            highlight.put("icon", row[1]);
            highlight.put("title", row[2]);
            highlight.put("description", row[3]);
            return highlight;
        }).collect(java.util.stream.Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getShopBanners(Long shopId) {
        String sql = """
            WITH banner_data AS (
                SELECT
                    b.id,
                    b.background_type,
                    b.button,
                    b.url,
                    b.description,
                    b.end_date,
                    b.tag,
                    b.title,
                    b.background_id,
                    b.poster_id
                FROM banners b
                WHERE
                    b.is_active = TRUE
                AND b.shop_id = :shopId
                ORDER BY b.sort_order DESC, b.id DESC
                LIMIT 10
            ),
            needed_file_ids AS (
                SELECT DISTINCT unnest(array[background_id, poster_id]) AS id
                FROM banner_data
                WHERE background_id IS NOT NULL OR poster_id IS NOT NULL
            ),
            file_data AS (
                SELECT
                    fs.id,
                    jsonb_build_object(
                        'id', fs.id,
                        'storage_location', fs.storage_location,
                        'original_url', fs.original_url,
                        'thumbnail_url', fs.thumbnail_url
                    ) AS file_json
                FROM file_storage fs
                    JOIN needed_file_ids nfi ON fs.id = nfi.id
            )
            SELECT
                bd.*,
                fd_bg.file_json AS background,
                fd_poster.file_json AS poster
            FROM banner_data bd
                LEFT JOIN file_data fd_bg ON fd_bg.id = bd.background_id
                LEFT JOIN file_data fd_poster ON fd_poster.id = bd.poster_id
        """;

        List<Object[]> results = entityManager.createNativeQuery(sql)
                .setParameter("shopId", shopId)
                .getResultList();

        return results.stream().map(row -> {
            Map<String, Object> banner = new HashMap<>();
            banner.put("id", ((Number) row[0]).longValue());
            banner.put("background_type", row[1]);
            banner.put("button", row[2]);
            banner.put("url", row[3]);
            banner.put("description", row[4]);
            banner.put("end_date", row[5]);
            banner.put("tag", row[6]);
            banner.put("title", row[7]);
            banner.put("background", row[10]);
            banner.put("poster", row[11]);
            return banner;
        }).collect(java.util.stream.Collectors.toList());
    }
}
