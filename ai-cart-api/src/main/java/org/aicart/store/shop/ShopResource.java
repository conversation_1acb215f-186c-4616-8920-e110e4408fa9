package org.aicart.store.shop;

import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

import java.util.Map;

@Path("/shop")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ShopResource {

    @Inject
    ShopService shopService;

    @GET
    @Path("/by-host")
    public Response getShopByHost(@QueryParam("host") String host, 
                                  @QueryParam("countryCode") String countryCode) {
        if (host == null || host.isEmpty()) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(Map.of("error", "Host parameter is required"))
                    .build();
        }
        
        Map<String, Object> shop = shopService.getShopDataByHost(host, countryCode);
        if (shop == null) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }
        return Response.ok(shop).build();
    }

    @GET
    @Path("/{shopId}/theme")
    public Response getShopTheme(@PathParam("shopId") Long shopId) {
        Map<String, Object> theme = shopService.getShopThemeData(shopId);
        if (theme == null) {
            return Response.status(Response.Status.NOT_FOUND).build();
        }
        return Response.ok(theme).build();
    }

    @GET
    @Path("/{shopId}/highlights")
    public Response getShopHighlights(@PathParam("shopId") Long shopId) {
        var highlights = shopService.getShopHighlights(shopId);
        return Response.ok(highlights).build();
    }

    @GET
    @Path("/{shopId}/banners")
    public Response getShopBanners(@PathParam("shopId") Long shopId) {
        var banners = shopService.getShopBanners(shopId);
        return Response.ok(banners).build();
    }
}
