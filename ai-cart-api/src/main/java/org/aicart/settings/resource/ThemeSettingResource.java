package org.aicart.settings.resource;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.settings.dto.ThemeRequestDTO;
import org.aicart.settings.entity.ShopThemeSetting;
import org.aicart.settings.service.ThemeSettingService;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.common.response.ApiResponse;

import java.util.Map;

@Path("/theme-settings")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequireShopId
public class ThemeSettingResource {

    @Inject
    ThemeSettingService themeSettingService;

    @POST
    @Path("/")
    public Response createTheme(@Valid ThemeRequestDTO dto) {
        ShopThemeSetting setting = themeSettingService.updateTheme(null, dto);
        return Response.ok(ApiResponse.success(Map.of("setting", setting.sections, "id", setting.id), "Theme created successfully")).build();
    }

    @PUT
    @Path("/{id}")
    public Response updateTheme(@PathParam("id") String id, @Valid ThemeRequestDTO dto) {
        ShopThemeSetting setting = themeSettingService.updateTheme(id, dto);
        return Response.ok(ApiResponse.success(Map.of("setting", setting.sections, "id", setting.id), "Theme updated successfully")).build();
    }
}
