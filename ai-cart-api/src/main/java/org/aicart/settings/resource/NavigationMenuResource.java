package org.aicart.settings.resource;

import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.settings.dto.CreateNavigationMenuRequestDTO;
import org.aicart.settings.dto.NavigationMenuDTO;
import org.aicart.settings.dto.PublicNavigationMenuItemDTO;
import org.aicart.settings.entity.NavigationMenu;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Path("/navigation-menus")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequireShopId
public class NavigationMenuResource {

    @Inject
    org.aicart.settings.service.NavigationMenuService service;

    @GET
    public List<NavigationMenu> listMenus() {
        return service.listAllMenus();
    }

    @GET
    @Path("/{id}")
    public Response getMenu(@PathParam("id") Long id) {
        NavigationMenu menu = service.getMenuById(id);

        if (menu == null) {
            throw BusinessException.notFound("Navigation menu");
        }

        NavigationMenuDTO menuDto = NavigationMenuDTO.fromEntity(menu);

        return Response.ok(ApiResponse.success(menuDto)).build();
    }

    @GET
    @Path("/public/{name}")
    public List<PublicNavigationMenuItemDTO> getMenu(
            @PathParam("name") String name,
            @QueryParam("lang") @DefaultValue("en") String lang
    ) {
        return service.getNavigationMenu(name, lang);
    }

    @POST
    public Response createMenu(CreateNavigationMenuRequestDTO request) {
        if (request.name == null || request.name.isEmpty()) {
            throw BusinessException.badRequest("Menu name is required");
        }

        if (request.value == null || request.value.isEmpty()) {
            throw BusinessException.badRequest("Menu items are required");
        }

        service.createOrUpdateMenu(null, request.name, request.value);
        return Response.status(Response.Status.CREATED)
                .entity(ApiResponse.success(null, "Menu created successfully"))
                .type(MediaType.APPLICATION_JSON).build();
    }

    @PUT
    @Path("/{id}")
    public Response updateMenu(@PathParam("id") Long id, CreateNavigationMenuRequestDTO request) {
        service.createOrUpdateMenu(id, request.name, request.value);
        return Response.status(Response.Status.CREATED)
                .entity(ApiResponse.success(null, "Menu updated successfully"))
                .type(MediaType.APPLICATION_JSON).build();
    }

    @DELETE
    @Path("/{id}")
    public Response deleteMenu(@PathParam("id") Long id) {
        service.deleteMenu(id);
        return Response.noContent().build();
    }
}
