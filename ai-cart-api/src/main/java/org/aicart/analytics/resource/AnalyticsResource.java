package org.aicart.analytics.resource;

import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.analytics.dto.DashboardStatsDTO;
import org.aicart.analytics.service.AnalyticsService;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.common.response.ApiResponse;

@Path("/analytics")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequireShopId
public class AnalyticsResource {

    @Inject
    AnalyticsService analyticsService;

    @GET
    @Path("/dashboard")
    public Response getDashboardStats(@QueryParam("period") @DefaultValue("month") String period) {
        DashboardStatsDTO stats = analyticsService.getDashboardStats(period);
        return Response.ok(ApiResponse.success(stats)).build();
    }

    @GET
    @Path("/revenue")
    public Response getRevenueAnalytics(
            @QueryParam("period") @DefaultValue("month") String period,
            @QueryParam("groupBy") @DefaultValue("day") String groupBy) {
        // This can be expanded for more detailed revenue analytics
        DashboardStatsDTO stats = analyticsService.getDashboardStats(period);
        return Response.ok(ApiResponse.success(stats.getRevenueChart())).build();
    }

    @GET
    @Path("/orders")
    public Response getOrderAnalytics(
            @QueryParam("period") @DefaultValue("month") String period,
            @QueryParam("groupBy") @DefaultValue("day") String groupBy) {
        DashboardStatsDTO stats = analyticsService.getDashboardStats(period);
        return Response.ok(ApiResponse.success(stats.getOrderChart())).build();
    }

    @GET
    @Path("/categories")
    public Response getCategoryAnalytics(@QueryParam("period") @DefaultValue("month") String period) {
        DashboardStatsDTO stats = analyticsService.getDashboardStats(period);
        return Response.ok(ApiResponse.success(stats.getTopCategories())).build();
    }

    @GET
    @Path("/recent-orders")
    public Response getRecentOrders() {
        DashboardStatsDTO stats = analyticsService.getDashboardStats("month");
        return Response.ok(ApiResponse.success(stats.getRecentOrders())).build();
    }
}
