package org.aicart.product.collection.resource;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.product.collection.service.ProductCollectionService;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;
import org.aicart.product.collection.dto.ProductCollectionDTO;
import org.aicart.product.collection.mapper.ProductCollectionMapper;
import org.aicart.product.collection.repository.ProductCollectionRepository;

import java.util.Map;
import java.util.List;
import java.lang.Long;

@Path("/product/collections")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequireShopId
public class ProductCollectionResource {

    // TODO:: make county dynamic

    @Inject
    ProductCollectionService service;

    @Inject
    ProductCollectionRepository collectionRepo;

    @GET
    @Path("/")
    public Response getAll() {
        return Response.ok(ApiResponse.success(service.getAll())).build();
    }


    @GET
    @Path("/test")
    public Response test() {


        @SuppressWarnings("unchecked")
        List<Long> collectionIds = collectionRepo
                .getEntityManager()
                .createNativeQuery("SELECT id FROM product_collections")
                .getResultList();

//            var collections = collectionRepo.list(
//                    "SELECT c.id FROM ProductCollection c"); //  WHERE c.collection_type = true
//
//            System.out.println(collections);

        collectionIds.forEach(collectionRepo::updateSmartCollection);


//            for (ProductCollection collection : collections) {
//                collectionRepo.updateSmartCollection(1L);
//            }

//        if(request == null) {
//            return Response.status(Response.Status.BAD_REQUEST)
//                    .entity(Map.of("message", "Request body is required"))
//                    .build();
//        }

        return Response.ok(ApiResponse.success(null, "Collections updated successfully")).build();
    }


    @POST
    @Path("/")
    public Response create(@Valid ProductCollectionDTO request) {

        if(request == null) {
            throw BusinessException.badRequest("Request body is required");
        }

        return Response.ok(ApiResponse.success(ProductCollectionMapper.toDTO(service.create(request)), "Collection created successfully")).build();
    }

    @PUT
    @Path("/{id}")
    public Response update(@PathParam("id") Long collectionId, @Valid ProductCollectionDTO request) {

        if(request == null) {
            throw BusinessException.badRequest("Request body is required");
        }

        return Response.ok(ApiResponse.success(ProductCollectionMapper.toDTO(service.update(collectionId, request)), "Collection updated successfully")).build();
    }
}
