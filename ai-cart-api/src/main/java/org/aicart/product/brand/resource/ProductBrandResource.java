package org.aicart.product.brand.resource;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.product.brand.dto.ProductBrandDTO;
import org.aicart.product.brand.mapper.ProductBrandMapper;
import org.aicart.product.brand.service.ProductBrandService;
import org.aicart.product.brand.entity.ProductBrand;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.shop.context.ShopContext;
import org.aicart.shop.entity.Shop;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Path("/product/brands")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequireShopId
public class ProductBrandResource {

    @Inject
    ProductBrandService brandService;

    @Inject
    ShopContext shopContext;
    
    @GET
    public Response getBrands(
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("10") int size,
            @QueryParam("sort") @DefaultValue("name") String sortField,
            @QueryParam("order") @DefaultValue("asc") String sortOrder,
            @QueryParam("q") String searchQuery
    ) {
        Shop shop = shopContext.getShop();
        List<ProductBrand> brands = brandService.getBrands(page, size, sortField, 
                "asc".equalsIgnoreCase(sortOrder), searchQuery, shop);
        long totalCount = brandService.countBrands(searchQuery, shop);
        
        List<ProductBrandDTO> dtos = brands.stream()
                .map(ProductBrandMapper::toDto)
                .collect(Collectors.toList());
        
        ApiResponse.PaginationInfo pagination = new ApiResponse.PaginationInfo(page, size, totalCount, (int) Math.ceil((double) totalCount / size));
        return Response.ok(ApiResponse.success(dtos, pagination)).build();
    }
    
    @GET
    @Path("/{id}")
    public Response getBrand(@PathParam("id") Long id) {
        ProductBrand brand = brandService.findById(id, shopContext.getShop());
        if (brand == null) {
            throw BusinessException.notFound("Brand");
        }
        return Response.ok(ApiResponse.success(ProductBrandMapper.toDto(brand))).build();
    }

    @POST
    public Response createBrand(@Valid ProductBrandDTO dto) {
        ProductBrand brand = brandService.createBrand(
            dto.getName(),
            dto.getDescription(),
            dto.getLogo(),
            dto.getWebsite(),
            shopContext.getShop()
        );
        return Response.status(Response.Status.CREATED)
                .entity(ApiResponse.success(ProductBrandMapper.toDto(brand), "Brand created successfully"))
                .build();
    }
    
    @PUT
    @Path("/{id}")
    public Response updateBrand(
            @PathParam("id") Long id, 
            @Valid ProductBrandDTO dto
    ) {
        ProductBrand brand = brandService.updateBrand(
            id,
            dto.getName(),
            dto.getDescription(),
            dto.getLogo(),
            dto.getWebsite(),
            shopContext.getShop()
        );
        return Response.ok(ApiResponse.success(ProductBrandMapper.toDto(brand), "Brand updated successfully")).build();
    }

    @DELETE
    @Path("/{id}")
    public Response deleteBrand(@PathParam("id") Long id) {
        brandService.deleteBrand(id, shopContext.getShop());
        return Response.noContent().build();
    }
}