package org.aicart.product.product.resource;

import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.product.product.request.ProductCreateRequestDTO;
import org.aicart.product.product.service.ProductService;
import org.aicart.product.product.service.ProductStoreService;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.shop.context.ShopContext;
import org.aicart.common.util.QueryParamConverter;
import org.aicart.product.product.dto.ProductItemDTO;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Path("/product")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequireShopId
public class ProductResource {

    @Inject
    EntityManager entityManager;

    @Inject
    ProductService productService;

    @Inject
    ProductStoreService productCreateService;

    @Inject
    ShopContext shopContext;


    @GET
    @Path("/")
    public Response products(
            @QueryParam("page") Integer page,
            @QueryParam("pageSize") Integer pageSize,
            @QueryParam("minPrice") Optional<Long> minPrice,
            @QueryParam("maxPrice") Optional<Long> maxPrice,
            @QueryParam("q") Optional<String> nameFilter,
            @QueryParam("categoryIds") String categoryIds,
            @QueryParam("brandIds") String brandIds,
            @QueryParam("variantIds") String variantIds
    ) {
        Optional<List<Long>> categoryList = Optional.ofNullable(categoryIds).map(QueryParamConverter::toLongList);
        Optional<List<Long>> brandList = Optional.ofNullable(brandIds).map(QueryParamConverter::toLongList);
        Optional<List<Long>> variantList = Optional.ofNullable(variantIds).map(QueryParamConverter::toLongList);

        Integer pageNumber = (page == null) ? 0 : page;
        Integer pageLimit = (pageSize == null) ? 20 : pageSize;

        List<ProductItemDTO> products = productService.getPaginateProducts(pageNumber, pageLimit, minPrice, maxPrice, nameFilter, categoryList, brandList, variantList);

        return Response.ok(ApiResponse.success(products)).build();
    }

    @GET
    @Path("/detail/{slug}")
    public Response productDetail(@PathParam("slug") String slug) {

        ProductItemDTO product = productService.getProductBySlug(slug);
        if(product == null) {
            throw BusinessException.notFound("Product");
        }

        return Response.ok(ApiResponse.success(product)).build();
    }


    @POST
    @Path("/")
    public Response productCreate(@Valid ProductCreateRequestDTO productDTO) {
        return productCreateService.productCreate(productDTO);
    }


    @PUT
    @Path("/update/{productId}")
    public Response productUpdate(@PathParam("productId") BigInteger productId, @Valid ProductCreateRequestDTO productDTO) {
        return productCreateService.productUpdate(productId, productDTO);
    }

    @GET
    @Path("/paginated-with-categories")
    public Response getProductsWithCategories(@QueryParam("page") int page,
                                              @QueryParam("pageSize") int pageSize) {
        List<Map<String, Object>> products = productService.getProductsWithCategories(page, pageSize);
        return Response.ok(ApiResponse.success(products)).build();
    }

    @GET
    @Path("/price-range")
    public Response getPriceRange() {
        Map<String, Object> priceRange = productService.getMinMaxPrice();
        return Response.ok(ApiResponse.success(priceRange)).build();
    }

    @GET
    @Path("/count")
    public Response getProductCount(
            @QueryParam("q") Optional<String> nameFilter,
            @QueryParam("categoryIds") String categoryIds,
            @QueryParam("brandIds") String brandIds,
            @QueryParam("minPrice") Optional<Long> minPrice,
            @QueryParam("maxPrice") Optional<Long> maxPrice
    ) {
        Optional<List<Long>> categoryList = Optional.ofNullable(categoryIds).map(QueryParamConverter::toLongList);
        Optional<List<Long>> brandList = Optional.ofNullable(brandIds).map(QueryParamConverter::toLongList);

        long count = productService.getProductCount(minPrice, maxPrice, nameFilter, categoryList, brandList);
        return Response.ok(ApiResponse.success(Map.of("count", count))).build();
    }

    @GET
    @Path("/filter-categories")
    public Response getFilterCategories() {
        List<Map<String, Object>> categories = productService.getFilterCategories(shopContext.getShop());
        return Response.ok(ApiResponse.success(categories)).build();
    }

    @GET
    @Path("/filter-brands")
    public Response getFilterBrands() {
        List<Map<String, Object>> brands = productService.getFilterBrands(shopContext.getShop());
        return Response.ok(ApiResponse.success(brands)).build();
    }

    @GET
    @Path("/filter-attributes")
    public Response getFilterAttributes() {
        List<Map<String, Object>> attributes = productService.getFilterAttributes(shopContext.getShop());
        return Response.ok(ApiResponse.success(attributes)).build();
    }

}
