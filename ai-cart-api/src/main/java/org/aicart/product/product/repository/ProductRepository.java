package org.aicart.product.product.repository;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import org.aicart.product.product.dto.ProductItemDTO;
import org.aicart.product.product.entity.Product;
import org.aicart.shop.entity.Shop;

import java.util.List;
import java.util.Optional;

@ApplicationScoped
public class ProductRepository {

    @PersistenceContext
    EntityManager entityManager;

    private Query getProductQuery(
            Optional<Long> minPrice,
            Optional<Long> maxPrice,
            Optional<String> nameFilter,
            Optional<List<Long>> categoryIds,
            Optional<List<Long>> brandIds,
            Optional<String> slugFilter,
            Optional<List<Long>> variantIds
    ) {

        int languageId = 1; // TODO Lang
        int countryId = 1; // TODO country Id
        long currentTimestamp = System.currentTimeMillis() / 1000L; // Static Unix timestamp for now

        StringBuilder queryBuilder = new StringBuilder("""
        SELECT
        p.id AS product_id,
        p.name AS product_name,
        p.slug AS slug,
        locale.id AS locale_id,
        locale.name AS locale_name,
        (
            SELECT jsonb_agg(
                           jsonb_build_object(
                                   'id', fs.id,
                                   'relation_id', fsr.id,
                                   'original_url', fs.original_url,
                                   'medium_url', fs.medium_url,
                                   'storage_location', fs.storage_location,
                                   'score', fsr.score
                           )
                   )
            FROM file_storage_relation fsr
                     JOIN file_storage fs
                          ON fsr.file_id = fs.id
            WHERE fsr.associated_id = p.id AND fsr.associated_type = 1
        ) AS images,
        (
            SELECT jsonb_agg(
                           jsonb_build_object(
                                   'id', c.id,
                                   'name', c.name,
                                   'category_id', pc.category_id,
                                   'depth', cc.depth
                           )
                   )
            FROM product_category pc
                     JOIN category_closure cc
                          ON pc.category_id = cc.descendant_id
                     JOIN categories c
                          ON cc.ancestor_id = c.id
            WHERE pc.product_id = p.id
        ) AS categories,
        (
            SELECT jsonb_agg(
                           jsonb_build_object(
                                   'id', pv.id,
                                   'sku', pv.sku,
                                   'stock', (
                                       SELECT SUM(vs.quantity)
                                       FROM variant_stocks vs
                                       WHERE vs.variant_id = pv.id
                                   ),
                                   'price', (
                                       SELECT jsonb_build_object(
                                                      'price', vp.price,
                                                      'compare_price', vp.compare_price,
                                                      'discount', COALESCE(d.amount, 0),
                                                      'discount_end_at', d.end_at,
                                                      'discount_type', d.discount_type,
                                                      'tax_rate', COALESCE(t.tax_rate, 0)
                                              )
                                       FROM variant_prices vp
                                       LEFT JOIN discount_variant_pivot dp ON vp.variant_id = dp.variant_id
                                       LEFT JOIN discounts d ON dp.discount_id = d.id

                                       LEFT JOIN product_tax_rate pt ON pt.product_id = p.id
                                            AND pt.country_id = :countryId
                                            LEFT JOIN taxes t
                                            ON pt.tax_id = t.id
                                       WHERE vp.variant_id = pv.id AND vp.country_id = :countryId
                                       LIMIT 1
                                   ),
                                   'image_id', pv.image_id,
                                   'attributes', (
                                       SELECT jsonb_agg(
                                                      jsonb_build_object(
                                                              'attribute_name', a.name,
                                                              'value', av.value,
                                                              'attribute_id', av.attribute_id,
                                                              'value_id', av.id
                                                      )
                                              )
                                       FROM product_variant_value pvv
                                                JOIN attribute_values av
                                                     ON pvv.attribute_value_id = av.id
                                                JOIN attributes a
                                                     ON av.attribute_id = a.id
                                       WHERE pvv.variant_id = pv.id
                                   )
                           )
                   )
            FROM product_variants pv
            WHERE pv.product_id = p.id
        ) AS variants
        FROM products p
                 LEFT JOIN product_translations locale
                           ON p.id = locale.product_id AND locale.language_id = :languageId
        WHERE 1 = 1
        """);


        if (minPrice.isPresent()) {
            queryBuilder.append("""
                AND EXISTS (
                    SELECT 1
                    FROM variant_prices vp
                             JOIN product_variants pv ON vp.variant_id = pv.id
                    WHERE pv.product_id = p.id
                      AND vp.price >= :minPrice
                )
            """);
        }


        if (maxPrice.isPresent()) {
            queryBuilder.append("""
                AND EXISTS (
                    SELECT 1
                    FROM variant_prices vp
                             JOIN product_variants pv ON vp.variant_id = pv.id
                    WHERE pv.product_id = p.id
                      AND vp.price <= :maxPrice
                )
            """);
        }

        if (nameFilter.isPresent()) {
            queryBuilder.append("""
                AND (p.name ILIKE :nameFilter OR locale.name ILIKE :nameFilter)
            """);
        }

        if (slugFilter.isPresent()) {
            queryBuilder.append("""
                 AND (p.slug = :slugFilter)
            """);
        }

        if (variantIds.isPresent() && !variantIds.get().isEmpty()) {
            queryBuilder.append("""
                AND EXISTS (
                    SELECT 1
                    FROM product_variants pv
                    WHERE pv.product_id = p.id
                      AND pv.id = ANY(:variantIds)
                )
            """);
        }

        if (categoryIds.isPresent()) {
            queryBuilder.append("""
                AND EXISTS (
                    SELECT 1
                    FROM product_category pc
                             JOIN categories c ON pc.category_id = c.id
                    WHERE pc.product_id = p.id
                      AND c.id = ANY(:categoryIds)
                )
            """);
        }


        queryBuilder.append(" ORDER BY p.id");

        // Create query
        Query nativeQuery = entityManager.createNativeQuery(queryBuilder.toString(), ProductItemDTO.class);

        // Set parameters
        nativeQuery.setParameter("languageId", languageId);
        nativeQuery.setParameter("countryId", countryId);
//        nativeQuery.setParameter("currentTimestamp", currentTimestamp);
        minPrice.ifPresent(price -> nativeQuery.setParameter("minPrice", price));
        maxPrice.ifPresent(price -> nativeQuery.setParameter("maxPrice", price));
        nameFilter.ifPresent(filter -> nativeQuery.setParameter("nameFilter", "%" + filter + "%"));
        slugFilter.ifPresent(filter -> nativeQuery.setParameter("slugFilter", slugFilter.get()));

        if(categoryIds.isPresent())
        {
            Long[] categoryArray = categoryIds.get().toArray(new Long[0]);
            nativeQuery.setParameter("categoryIds", categoryArray);
        }

        if(variantIds.isPresent() && !variantIds.get().isEmpty())
        {
            Long[] variantArray = variantIds.get().toArray(new Long[0]);
            nativeQuery.setParameter("variantIds", variantArray);
        }

        return nativeQuery;
    }

    public List<ProductItemDTO> getPaginateProducts(
            Integer page,
            Integer pageSize,
            Optional<Long> minPrice,
            Optional<Long> maxPrice,
            Optional<String> nameFilter,
            Optional<List<Long>> categoryIds,
            Optional<List<Long>> brandIds,
            Optional<List<Long>> variantIds
            ) {

        return getProductQuery(minPrice, maxPrice, nameFilter, categoryIds, brandIds, Optional.empty(), variantIds)
                .setFirstResult(page * pageSize)
                .setMaxResults(pageSize)
                .getResultList();
    }


    public ProductItemDTO getProductBySlug(
            String slugFilter
    ) {
        List<ProductItemDTO> resultList = getProductQuery(Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(), Optional.empty(), Optional.ofNullable(slugFilter), Optional.empty())
                .setMaxResults(1)
                .getResultList();

        return resultList.isEmpty() ? null : resultList.get(0);
    }

    public ProductItemDTO getProductById(Long productId) {
        // Use the existing query but filter by product ID
        String query = """
            SELECT
            p.id AS product_id,
            p.name AS product_name,
            p.slug AS slug,
            locale.id AS locale_id,
            locale.name AS locale_name,
            (
                SELECT jsonb_agg(
                               jsonb_build_object(
                                       'id', fs.id,
                                       'relation_id', fsr.id,
                                       'original_url', fs.original_url,
                                       'medium_url', fs.medium_url,
                                       'storage_location', fs.storage_location,
                                       'score', fsr.score
                               )
                       )
                FROM file_storage_relation fsr
                         JOIN file_storage fs
                              ON fsr.file_id = fs.id
                WHERE fsr.associated_id = p.id AND fsr.associated_type = 1
            ) AS images,
            (
                SELECT jsonb_agg(
                               jsonb_build_object(
                                       'id', c.id,
                                       'name', c.name,
                                       'category_id', pc.category_id,
                                       'depth', cc.depth
                               )
                       )
                FROM product_category pc
                         JOIN category_closure cc
                              ON pc.category_id = cc.descendant_id
                         JOIN categories c
                              ON cc.ancestor_id = c.id
                WHERE pc.product_id = p.id
            ) AS categories,
            (
                SELECT jsonb_agg(
                               jsonb_build_object(
                                       'id', pv.id,
                                       'sku', pv.sku,
                                       'stock', (
                                           SELECT SUM(vs.quantity)
                                           FROM variant_stocks vs
                                           WHERE vs.variant_id = pv.id
                                       ),
                                       'price', (
                                           SELECT jsonb_build_object(
                                                          'price', vp.price,
                                                          'compare_price', vp.compare_price,
                                                          'discount', COALESCE(d.amount, 0),
                                                          'discount_end_at', d.end_at,
                                                          'discount_type', d.discount_type,
                                                          'tax_rate', COALESCE(t.tax_rate, 0)
                                                  )
                                           FROM variant_prices vp
                                           LEFT JOIN discount_variant_pivot dp ON vp.variant_id = dp.variant_id
                                           LEFT JOIN discounts d ON dp.discount_id = d.id
                                           LEFT JOIN product_tax_rate pt ON pt.product_id = p.id
                                                AND pt.country_id = :countryId
                                                LEFT JOIN taxes t
                                                ON pt.tax_id = t.id
                                           WHERE vp.variant_id = pv.id AND vp.country_id = :countryId
                                           LIMIT 1
                                       ),
                                       'image_id', pv.image_id,
                                       'attributes', (
                                           SELECT jsonb_agg(
                                                          jsonb_build_object(
                                                                  'attribute_name', a.name,
                                                                  'value', av.value,
                                                                  'attribute_id', av.attribute_id,
                                                                  'value_id', av.id
                                                          )
                                                  )
                                           FROM product_variant_value pvv
                                                    JOIN attribute_values av
                                                         ON pvv.attribute_value_id = av.id
                                                    JOIN attributes a
                                                         ON av.attribute_id = a.id
                                           WHERE pvv.variant_id = pv.id
                                       )
                               )
                       )
                FROM product_variants pv
                WHERE pv.product_id = p.id
            ) AS variants
            FROM products p
                     LEFT JOIN product_translations locale
                               ON p.id = locale.product_id AND locale.language_id = :languageId
            WHERE p.id = :productId
            """;

        Query nativeQuery = entityManager.createNativeQuery(query, ProductItemDTO.class);
        nativeQuery.setParameter("languageId", 1); // TODO: Make dynamic
        nativeQuery.setParameter("countryId", 1); // TODO: Make dynamic
        nativeQuery.setParameter("productId", productId);

        List<ProductItemDTO> resultList = nativeQuery.getResultList();
        return resultList.isEmpty() ? null : resultList.get(0);
    }





    public List<Product> findPaginatedProducts(int page, int pageSize) {
        return entityManager.createQuery(
                        "SELECT p FROM products p ORDER BY p.id", Product.class)
                .setFirstResult(page * pageSize)
                .setMaxResults(pageSize)
                .getResultList();
    }

    public List<Object[]> findCategoriesByProductIds(List<Long> productIds) {

        String sql = """
            SELECT 
                p.id AS productId,
                c1.category_id AS categoryId,
                a1.id AS ancestorId,
                a1.name AS categoryName,
                cc1.depth AS depth
            FROM 
                products p
            JOIN 
                product_category c1 ON p.id = c1.product_id
            LEFT JOIN 
                category_closure cc1 ON c1.category_id = cc1.descendant_id
            LEFT JOIN 
                categories a1 ON cc1.ancestor_id = a1.id
            WHERE 
                p.id IN (:productIds)
            ORDER BY 
                p.id, a1.id
        """;

        return entityManager.createNativeQuery(sql)
                .setParameter("productIds", productIds) // Pass the product IDs
                .getResultList();
    }

    public java.util.Map<String, Object> getMinMaxPrice() {
        String sql = """
            SELECT MIN(vp.price) AS min_price, MAX(vp.price) AS max_price
            FROM variant_prices vp
            JOIN product_variants pv ON vp.variant_id = pv.id
            JOIN products p ON pv.product_id = p.id
            WHERE vp.country_id = :countryId
        """;

        Object[] result = (Object[]) entityManager.createNativeQuery(sql)
                .setParameter("countryId", 1) // TODO: Make dynamic
                .getSingleResult();

        java.util.Map<String, Object> priceRange = new java.util.HashMap<>();
        priceRange.put("min_price", result[0] != null ? ((Number) result[0]).longValue() : 0L);
        priceRange.put("max_price", result[1] != null ? ((Number) result[1]).longValue() : 0L);
        return priceRange;
    }

    public long getProductCount(Optional<Long> minPrice, Optional<Long> maxPrice,
                               Optional<String> nameFilter, Optional<List<Long>> categoryIds,
                               Optional<List<Long>> brandIds) {
        StringBuilder queryBuilder = new StringBuilder("""
            SELECT COUNT(DISTINCT p.id)
            FROM products p
            WHERE 1 = 1
        """);

        if (minPrice.isPresent()) {
            queryBuilder.append("""
                AND EXISTS (
                    SELECT 1 FROM variant_prices vp
                    JOIN product_variants pv ON vp.variant_id = pv.id
                    WHERE pv.product_id = p.id AND vp.price >= :minPrice
                )
            """);
        }

        if (maxPrice.isPresent()) {
            queryBuilder.append("""
                AND EXISTS (
                    SELECT 1 FROM variant_prices vp
                    JOIN product_variants pv ON vp.variant_id = pv.id
                    WHERE pv.product_id = p.id AND vp.price <= :maxPrice
                )
            """);
        }

        if (nameFilter.isPresent()) {
            queryBuilder.append(" AND p.name ILIKE :nameFilter");
        }

        if (categoryIds.isPresent() && !categoryIds.get().isEmpty()) {
            queryBuilder.append("""
                AND EXISTS (
                    SELECT 1 FROM product_category pc
                    WHERE pc.product_id = p.id AND pc.category_id = ANY(:categoryIds)
                )
            """);
        }

        Query nativeQuery = entityManager.createNativeQuery(queryBuilder.toString());
        minPrice.ifPresent(price -> nativeQuery.setParameter("minPrice", price));
        maxPrice.ifPresent(price -> nativeQuery.setParameter("maxPrice", price));
        nameFilter.ifPresent(filter -> nativeQuery.setParameter("nameFilter", "%" + filter + "%"));

        if (categoryIds.isPresent() && !categoryIds.get().isEmpty()) {
            Long[] categoryArray = categoryIds.get().toArray(new Long[0]);
            nativeQuery.setParameter("categoryIds", categoryArray);
        }

        return ((Number) nativeQuery.getSingleResult()).longValue();
    }

    public List<java.util.Map<String, Object>> getFilterCategories(Shop shop) {
        String sql = """
            SELECT
                c.id,
                c.name,
                c.parent_category_id,
                COUNT(DISTINCT p.id) as product_count
            FROM categories c
            LEFT JOIN product_category pc ON c.id = pc.category_id
            LEFT JOIN products p ON pc.product_id = p.id
            WHERE c.shop_id = :shopId
            GROUP BY c.id, c.name, c.parent_category_id
            ORDER BY c.name
        """;

        List<Object[]> results = entityManager.createNativeQuery(sql)
                .setParameter("shopId", shop.id)
                .getResultList();

        return results.stream().map(row -> {
            java.util.Map<String, Object> category = new java.util.HashMap<>();
            category.put("id", ((Number) row[0]).longValue());
            category.put("name", row[1]);
            category.put("parent_id", row[2] != null ? ((Number) row[2]).longValue() : null);
            category.put("product_count", ((Number) row[3]).longValue());
            return category;
        }).collect(java.util.stream.Collectors.toList());
    }

    public List<java.util.Map<String, Object>> getFilterBrands(Shop shop) {
        String sql = """
            SELECT
                pb.id,
                pb.name,
                COUNT(DISTINCT p.id) as product_count
            FROM product_brands pb
            LEFT JOIN products p ON pb.id = p.product_brand_id
            WHERE pb.shop_id = :shopId
            GROUP BY pb.id, pb.name
            ORDER BY pb.name
        """;

        List<Object[]> results = entityManager.createNativeQuery(sql)
                .setParameter("shopId", shop.id)
                .getResultList();

        return results.stream().map(row -> {
            java.util.Map<String, Object> brand = new java.util.HashMap<>();
            brand.put("id", ((Number) row[0]).longValue());
            brand.put("name", row[1]);
            brand.put("product_count", ((Number) row[2]).longValue());
            return brand;
        }).collect(java.util.stream.Collectors.toList());
    }

    public List<java.util.Map<String, Object>> getFilterAttributes(Shop shop) {
        String sql = """
            SELECT
                a.id as attribute_id,
                a.name as attribute_name,
                av.id as value_id,
                av.value as value_name,
                COUNT(DISTINCT p.id) as product_count
            FROM attributes a
            JOIN attribute_values av ON a.id = av.attribute_id
            LEFT JOIN product_variant_value pvv ON av.id = pvv.attribute_value_id
            LEFT JOIN product_variants pv ON pvv.variant_id = pv.id
            LEFT JOIN products p ON pv.product_id = p.id
            WHERE a.shop_id = :shopId
            GROUP BY a.id, a.name, av.id, av.value
            ORDER BY a.name, av.value
        """;

        List<Object[]> results = entityManager.createNativeQuery(sql)
                .setParameter("shopId", shop.id)
                .getResultList();

        return results.stream().map(row -> {
            java.util.Map<String, Object> attribute = new java.util.HashMap<>();
            attribute.put("attribute_id", ((Number) row[0]).longValue());
            attribute.put("attribute_name", row[1]);
            attribute.put("value_id", ((Number) row[2]).longValue());
            attribute.put("value_name", row[3]);
            attribute.put("product_count", ((Number) row[4]).longValue());
            return attribute;
        }).collect(java.util.stream.Collectors.toList());
    }
}
