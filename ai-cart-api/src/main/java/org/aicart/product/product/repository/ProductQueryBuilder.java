package org.aicart.product.product.repository;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import org.aicart.product.product.dto.ProductItemDTO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Unified query builder for product queries to eliminate redundancy
 * between data and count queries
 */
public class ProductQueryBuilder {
    
    private final EntityManager entityManager;
    private final Map<String, Object> parameters = new HashMap<>();
    private final StringBuilder whereClause = new StringBuilder();
    private final StringBuilder joinClause = new StringBuilder();
    
    // Base query parts
    private static final String BASE_SELECT = """
        SELECT DISTINCT
            p.id,
            p.name,
            p.description,
            p.slug,
            p.status,
            p.created_at,
            p.updated_at,
            p.meta_title,
            p.meta_description,
            COALESCE(MIN(vp.price), 0) as min_price,
            COALESCE(MAX(vp.price), 0) as max_price,
            COALESCE(SUM(vs.quantity), 0) as total_stock,
            fs.storage_location,
            fs.thumbnail_url,
            c.name as category_name,
            pb.name as brand_name
        """;
    
    private static final String BASE_FROM = """
        FROM products p
        LEFT JOIN product_variants pv ON p.id = pv.product_id
        LEFT JOIN variant_prices vp ON pv.id = vp.variant_id AND vp.country_id = :countryId
        LEFT JOIN variant_stocks vs ON pv.id = vs.variant_id
        LEFT JOIN file_storage_relations fsr ON p.id = fsr.associated_id AND fsr.associated_type = 'PRODUCT'
        LEFT JOIN file_storages fs ON fsr.file_id = fs.id AND fsr.score = 0
        LEFT JOIN product_category pc ON p.id = pc.product_id
        LEFT JOIN categories c ON pc.category_id = c.id
        LEFT JOIN product_brands pb ON p.product_brand_id = pb.id
        """;
    
    private static final String COUNT_SELECT = "SELECT COUNT(DISTINCT p.id) ";
    
    public ProductQueryBuilder(EntityManager entityManager) {
        this.entityManager = entityManager;
        // Default parameters
        parameters.put("languageId", 1); // TODO: Make dynamic
        parameters.put("countryId", 1);  // TODO: Make dynamic
        
        // Base where clause
        whereClause.append(" WHERE 1 = 1 ");
    }
    
    public ProductQueryBuilder withPriceRange(Optional<Long> minPrice, Optional<Long> maxPrice) {
        if (minPrice.isPresent()) {
            whereClause.append(" AND EXISTS (")
                      .append("SELECT 1 FROM variant_prices vp2 ")
                      .append("JOIN product_variants pv2 ON vp2.variant_id = pv2.id ")
                      .append("WHERE pv2.product_id = p.id AND vp2.price >= :minPrice")
                      .append(") ");
            parameters.put("minPrice", minPrice.get());
        }
        
        if (maxPrice.isPresent()) {
            whereClause.append(" AND EXISTS (")
                      .append("SELECT 1 FROM variant_prices vp3 ")
                      .append("JOIN product_variants pv3 ON vp3.variant_id = pv3.id ")
                      .append("WHERE pv3.product_id = p.id AND vp3.price <= :maxPrice")
                      .append(") ");
            parameters.put("maxPrice", maxPrice.get());
        }
        
        return this;
    }
    
    public ProductQueryBuilder withNameFilter(Optional<String> nameFilter) {
        if (nameFilter.isPresent()) {
            whereClause.append(" AND (p.name ILIKE :nameFilter OR p.description ILIKE :nameFilter) ");
            parameters.put("nameFilter", "%" + nameFilter.get() + "%");
        }
        return this;
    }
    
    public ProductQueryBuilder withCategories(Optional<List<Long>> categoryIds) {
        if (categoryIds.isPresent() && !categoryIds.get().isEmpty()) {
            whereClause.append(" AND EXISTS (")
                      .append("SELECT 1 FROM product_category pc2 ")
                      .append("WHERE pc2.product_id = p.id AND pc2.category_id = ANY(:categoryIds)")
                      .append(") ");
            parameters.put("categoryIds", categoryIds.get().toArray(new Long[0]));
        }
        return this;
    }
    
    public ProductQueryBuilder withBrands(Optional<List<Long>> brandIds) {
        if (brandIds.isPresent() && !brandIds.get().isEmpty()) {
            whereClause.append(" AND p.product_brand_id = ANY(:brandIds) ");
            parameters.put("brandIds", brandIds.get().toArray(new Long[0]));
        }
        return this;
    }
    
    public ProductQueryBuilder withVariants(Optional<List<Long>> variantIds) {
        if (variantIds.isPresent() && !variantIds.get().isEmpty()) {
            whereClause.append(" AND EXISTS (")
                      .append("SELECT 1 FROM product_variants pv4 ")
                      .append("WHERE pv4.product_id = p.id AND pv4.id = ANY(:variantIds)")
                      .append(") ");
            parameters.put("variantIds", variantIds.get().toArray(new Long[0]));
        }
        return this;
    }
    
    public ProductQueryBuilder withSlug(Optional<String> slug) {
        if (slug.isPresent()) {
            whereClause.append(" AND p.slug = :slug ");
            parameters.put("slug", slug.get());
        }
        return this;
    }
    
    /**
     * Build data query for pagination
     */
    public Query buildDataQuery() {
        String fullQuery = BASE_SELECT + BASE_FROM + whereClause.toString() + 
                          " GROUP BY p.id, p.name, p.description, p.slug, p.status, p.created_at, p.updated_at, " +
                          "p.meta_title, p.meta_description, fs.storage_location, fs.thumbnail_url, c.name, pb.name " +
                          " ORDER BY p.id ";
        
        Query query = entityManager.createNativeQuery(fullQuery, ProductItemDTO.class);
        setParameters(query);
        return query;
    }
    
    /**
     * Build count query for pagination
     */
    public Query buildCountQuery() {
        String countQuery = COUNT_SELECT + BASE_FROM + whereClause.toString();
        
        Query query = entityManager.createNativeQuery(countQuery);
        setParameters(query);
        return query;
    }
    
    /**
     * Set all parameters on the query
     */
    private void setParameters(Query query) {
        parameters.forEach(query::setParameter);
    }
    
    /**
     * Get total count
     */
    public long getCount() {
        return ((Number) buildCountQuery().getSingleResult()).longValue();
    }
    
    /**
     * Get paginated results
     */
    public List<ProductItemDTO> getResults(int page, int pageSize) {
        return buildDataQuery()
                .setFirstResult(page * pageSize)
                .setMaxResults(pageSize)
                .getResultList();
    }
}
