package org.aicart.product.product.resource;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.product.product.request.ValueNameRequest;
import org.aicart.product.product.service.ProductTypeService;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;

import java.util.Map;

@Path("/product/types")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequireShopId
public class ProductTypeResource {

    @Inject
    ProductTypeService service;

    @GET
    @Path("/")
    public Response getAll() {
        return Response.ok(ApiResponse.success(service.getAll())).build();
    }

    @POST
    @Path("/")
    public Response create(@Valid ValueNameRequest request) {
        if(request == null) {
            throw BusinessException.badRequest("Request body is required");
        }
        return Response.ok(ApiResponse.success(service.create(request.getName()), "Product type created successfully")).build();
    }
}
