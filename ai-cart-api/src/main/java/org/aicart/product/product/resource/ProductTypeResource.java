package org.aicart.product.product.resource;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.product.product.request.ValueNameRequest;
import org.aicart.product.product.service.ProductTypeService;
import org.aicart.shop.annotation.RequireShopId;

import java.util.Map;

@Path("/product/types")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequireShopId
public class ProductTypeResource {

    @Inject
    ProductTypeService service;

    @GET
    @Path("/")
    public Response getAll() {
        return Response.ok(service.getAll()).build();
    }

    @POST
    @Path("/")
    public Response create(@Valid ValueNameRequest request) {
        if(request == null) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(Map.of("message", "Request body is required"))
                    .build();
        }
        return Response.ok(service.create(request.getName())).build();
    }
}
