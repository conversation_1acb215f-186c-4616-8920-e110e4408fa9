package org.aicart.product.product.service;


import io.quarkus.logging.Log;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.aicart.product.product.dto.ProductItemDTO;
import org.aicart.product.product.dto.ProductPaginationResponse;
import org.aicart.product.product.dto.ProductWithCategoriesResponse;
import org.aicart.product.product.entity.Product;
import org.aicart.product.product.repository.ProductRepository;
import org.aicart.shop.entity.Shop;
import org.aicart.common.pagination.PaginationResult;
import java.util.*;
import java.util.stream.Collectors;

@ApplicationScoped
public class ProductService {

    @Inject
    ProductRepository productRepository;

    /**
     * Clean, unified pagination method - eliminates redundant queries!
     * Single method handles both data and count with shared query logic
     */
    public PaginationResult<ProductItemDTO> getProductsPaginated(Integer page,
                                                                Integer pageSize,
                                                                Optional<Long> minPrice,
                                                                Optional<Long> maxPrice,
                                                                Optional<String> nameFilter,
                                                                Optional<List<Long>> categoryIds,
                                                                Optional<List<Long>> brandIds,
                                                                Optional<List<Long>> variantIds) {
        return productRepository.findProductsPaginated(page, pageSize, minPrice, maxPrice, nameFilter, categoryIds, brandIds, variantIds);
    }


    public ProductPaginationResponse getPaginateProducts(Integer page,
                                                         Integer pageSize,
                                                         Optional<Long> minPrice,
                                                         Optional<Long> maxPrice,
                                                         Optional<String> nameFilter,
                                                         Optional<List<Long>> categoryIds,
                                                         Optional<List<Long>> brandIds,
                                                         Optional<List<Long>> variantIds) {
        // Get paginated products
        List<ProductItemDTO> products = productRepository.getPaginateProducts(page, pageSize, minPrice, maxPrice, nameFilter, categoryIds, brandIds, variantIds);

        // Get total count for pagination
        long totalElements = productRepository.getProductCount(minPrice, maxPrice, nameFilter, categoryIds, brandIds);

        return new ProductPaginationResponse(products, totalElements, page, pageSize);
    }


    public ProductItemDTO getProductBySlug(String slug) {
        return productRepository.findProductBySlug(slug);
    }

    public ProductItemDTO getProductById(Long id) {
        return productRepository.getProductById(id);
    }

    public ProductWithCategoriesResponse getProductsWithCategories(int page, int pageSize) {
        // Step 1: Fetch paginated products
        List<Product> products = productRepository.findPaginatedProducts(page, pageSize);
        Log.info(products);
        List<Long> productIds = products.stream().map(product -> product.id).collect(Collectors.toList());

        if (productIds.isEmpty()) {
            // Get total count even if current page is empty
            long totalElements = Product.count();
            return new ProductWithCategoriesResponse(Collections.emptyList(), totalElements, page, pageSize);
        }

        // Step 2: Fetch categories for these products
        List<Object[]> categoryRows = productRepository.findCategoriesByProductIds(productIds);

        Log.info(categoryRows.stream().toString());

        // Step 3: Map categories to products
        Map<Long, List<Map<String, Object>>> productCategoryMap = new HashMap<>();
        Map<Long, Map<Long, Map<String, Object>>> categoryHierarchyMap = new HashMap<>();

        for (Object[] row : categoryRows) {
            Long productId = ((Number)row[0]).longValue();
            Long categoryId = ((Number)row[1]).longValue();
            String categoryName = (String) row[3];
            Long parentId = ((Number)row[4]).longValue();

            // Build category data
            Map<String, Object> categoryData = new HashMap<>();
            categoryData.put("id", categoryId);
            categoryData.put("name", categoryName);
            categoryData.put("children", new ArrayList<>());

            productCategoryMap.putIfAbsent(productId, new ArrayList<>());
            categoryHierarchyMap.putIfAbsent(productId, new HashMap<>());

            Map<Long, Map<String, Object>> productHierarchy = categoryHierarchyMap.get(productId);
            productHierarchy.putIfAbsent(categoryId, categoryData);

            if (parentId == null) {
                productCategoryMap.get(productId).add(categoryData);
            } else {
                Map<String, Object> parentCategory = productHierarchy.get(parentId);
                if (parentCategory != null) {
                    ((List<Map<String, Object>>) parentCategory.get("children")).add(categoryData);
                }
            }
        }

        // Step 4: Build the final response
        List<Map<String, Object>> productData = products.stream()
                .map(product -> {
                    Map<String, Object> data = new HashMap<>();
                    data.put("id", product.id);
                    data.put("name", product.name); // Fixed: was using product.id instead of product.name
                    data.put("categories", productCategoryMap.get(product.id));
                    return data;
                })
                .collect(Collectors.toList());

        // Get total count for pagination
        long totalElements = Product.count();

        return new ProductWithCategoriesResponse(productData, totalElements, page, pageSize);
    }

    public Map<String, Object> getMinMaxPrice() {
        return productRepository.getMinMaxPrice();
    }

    public long getProductCount(Optional<Long> minPrice, Optional<Long> maxPrice,
                               Optional<String> nameFilter, Optional<List<Long>> categoryIds,
                               Optional<List<Long>> brandIds) {
        return productRepository.getProductCount(minPrice, maxPrice, nameFilter, categoryIds, brandIds);
    }

    public List<Map<String, Object>> getFilterCategories(Shop shop) {
        return productRepository.getFilterCategories(shop);
    }

    public List<Map<String, Object>> getFilterBrands(Shop shop) {
        return productRepository.getFilterBrands(shop);
    }

    public List<Map<String, Object>> getFilterAttributes(Shop shop) {
        return productRepository.getFilterAttributes(shop);
    }
}

