package org.aicart.product.tag.resource;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.product.product.request.ValueNameRequest;
import org.aicart.product.tag.service.ProductTagService;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;

import java.util.Map;

@Path("/product/tags")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@RequireShopId
public class ProductTagResource {

    @Inject
    ProductTagService service;

    @GET
    @Path("/")
    public Response getAll() {
        return Response.ok(ApiResponse.success(service.getAll())).build();
    }

    @POST
    @Path("/")
    public Response create(@Valid ValueNameRequest request) {
        if(request == null) {
            throw BusinessException.badRequest("Request body is required");
        }
        return Response.ok(ApiResponse.success(service.create(request.getName()))).build();
    }
}
