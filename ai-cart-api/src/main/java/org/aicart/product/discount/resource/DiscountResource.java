package org.aicart.product.discount.resource;

import jakarta.inject.Inject;
import jakarta.ws.rs.core.MediaType;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.Response;
import org.aicart.product.discount.service.DiscountService;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;
import org.aicart.product.discount.dto.DiscountDTO;

@Path("/product/discounts")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequireShopId
public class DiscountResource {

    @Inject
    DiscountService discountService;

    @POST
    public Response createDiscount(@Valid DiscountDTO discountDTO) {
        DiscountDTO discount = discountService.createDiscount(discountDTO);
        return Response.status(Response.Status.CREATED).entity(ApiResponse.success(discount, "Discount created successfully")).build();
    }

    @PUT
    @Path("/update/{slug}")
    public Response updateDiscount(@Valid DiscountDTO discountDTO, @PathParam("slug") String slug) {
        DiscountDTO discount = discountService.updateDiscount(slug, discountDTO);
        return Response.status(Response.Status.ACCEPTED).entity(ApiResponse.success(discount, "Discount updated successfully")).build();
    }
}
