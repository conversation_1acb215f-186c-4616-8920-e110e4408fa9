package org.aicart.shop.resource;

import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.shop.service.ShopService;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;

import java.util.Map;

@Path("/shop")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class ShopResource {

    @Inject
    ShopService shopService;

    @GET
    @Path("/by-host")
    public Response getShopByHost(@QueryParam("host") String host, 
                                  @QueryParam("countryCode") String countryCode) {
        if (host == null || host.isEmpty()) {
            throw BusinessException.badRequest("Host parameter is required");
        }

        Map<String, Object> shop = shopService.getShopDataByHost(host, countryCode);
        if (shop == null) {
            throw BusinessException.notFound("Shop");
        }
        return Response.ok(ApiResponse.success(shop)).build();
    }

    @GET
    @Path("/{shopId}/theme")
    public Response getShopTheme(@PathParam("shopId") Long shopId) {
        Map<String, Object> theme = shopService.getShopThemeData(shopId);
        if (theme == null) {
            throw BusinessException.notFound("Shop theme");
        }
        return Response.ok(ApiResponse.success(theme)).build();
    }

    @GET
    @Path("/{shopId}/highlights")
    public Response getShopHighlights(@PathParam("shopId") Long shopId) {
        var highlights = shopService.getShopHighlights(shopId);
        return Response.ok(ApiResponse.success(highlights)).build();
    }

    @GET
    @Path("/{shopId}/banners")
    public Response getShopBanners(@PathParam("shopId") Long shopId) {
        var banners = shopService.getShopBanners(shopId);
        return Response.ok(ApiResponse.success(banners)).build();
    }
}
