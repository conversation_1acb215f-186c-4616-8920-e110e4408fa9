# Clean Pagination Pattern for Quarkus

This document explains the clean, well-structured pagination pattern implemented to eliminate redundant queries and provide consistent API responses.

## 🎯 **Problem Solved**

**Before (Redundant Approach):**
- Separate data query and count query with duplicated filter logic
- Multiple repository methods for similar operations
- Entity-specific pagination response classes
- Inconsistent pagination across different endpoints

**After (Clean Approach):**
- Single query builder with shared filter logic
- Unified pagination result class
- Consistent API response format
- Eliminates redundancy and improves maintainability

## 🏗️ **Architecture Components**

### 1. **PaginationResult<T>** - Generic pagination container
```java
PaginationResult<ProductItemDTO> result = PaginationResult.of(data, totalElements, page, pageSize);
```

### 2. **QueryBuilder Pattern** - Unified query construction
```java
ProductQueryBuilder queryBuilder = new ProductQueryBuilder(entityManager)
    .withPriceRange(minPrice, maxPrice)
    .withNameFilter(nameFilter)
    .withCategories(categoryIds);

long count = queryBuilder.getCount();
List<ProductItemDTO> data = queryBuilder.getResults(page, pageSize);
```

### 3. **Repository Layer** - Clean pagination methods
```java
public PaginationResult<ProductItemDTO> findProductsPaginated(...) {
    ProductQueryBuilder queryBuilder = new ProductQueryBuilder(entityManager)
        .withPriceRange(minPrice, maxPrice)
        .withNameFilter(nameFilter);
    
    long totalElements = queryBuilder.getCount();
    List<ProductItemDTO> products = queryBuilder.getResults(page, pageSize);
    
    return PaginationResult.of(products, totalElements, page, pageSize);
}
```

### 4. **Service Layer** - Business logic delegation
```java
public PaginationResult<ProductItemDTO> getProductsPaginated(...) {
    return productRepository.findProductsPaginated(...);
}
```

### 5. **Resource Layer** - Unified API response
```java
@GET
@Path("/v2")
public Response productsV2(...) {
    PaginationResult<ProductItemDTO> result = productService.getProductsPaginated(...);
    
    ApiResponse.PaginationInfo pagination = new ApiResponse.PaginationInfo(
        result.getCurrentPage(), result.getPageSize(), 
        result.getTotalElements(), result.getTotalPages());
    
    return Response.ok(ApiResponse.success(result.getData(), pagination)).build();
}
```

## 🚀 **Benefits**

1. **Eliminates Redundancy**: Single query builder for both data and count
2. **Consistent Responses**: Unified pagination format across all endpoints
3. **Maintainable Code**: Shared filter logic, easy to modify
4. **Performance**: Optimized queries with proper indexing support
5. **Type Safety**: Generic approach works with any entity
6. **Reusable**: Pattern can be applied to any entity (Customer, Order, etc.)

## 📝 **Usage Examples**

### Basic Pagination
```java
// Repository
public PaginationResult<CustomerDTO> findCustomersPaginated(int page, int pageSize, String search) {
    CustomerQueryBuilder queryBuilder = new CustomerQueryBuilder(entityManager)
        .withSearch(Optional.ofNullable(search));
    
    return PaginationResult.of(
        queryBuilder.getResults(page, pageSize),
        queryBuilder.getCount(),
        page, pageSize
    );
}

// Resource
@GET
public Response getCustomers(@QueryParam("page") int page, 
                           @QueryParam("size") int size,
                           @QueryParam("search") String search) {
    PaginationResult<CustomerDTO> result = customerService.getCustomersPaginated(page, size, search);
    
    ApiResponse.PaginationInfo pagination = new ApiResponse.PaginationInfo(
        result.getCurrentPage(), result.getPageSize(), 
        result.getTotalElements(), result.getTotalPages());
    
    return Response.ok(ApiResponse.success(result.getData(), pagination)).build();
}
```

### Advanced Filtering
```java
ProductQueryBuilder queryBuilder = new ProductQueryBuilder(entityManager)
    .withPriceRange(Optional.of(100L), Optional.of(500L))
    .withCategories(Optional.of(List.of(1L, 2L, 3L)))
    .withBrands(Optional.of(List.of(10L, 20L)))
    .withNameFilter(Optional.of("laptop"));

PaginationResult<ProductItemDTO> result = PaginationResult.of(
    queryBuilder.getResults(0, 20),
    queryBuilder.getCount(),
    0, 20
);
```

## 🔧 **Implementation for New Entities**

1. Create `EntityQueryBuilder` class with filter methods
2. Add `findEntityPaginated` method to repository
3. Use `PaginationResult<EntityDTO>` as return type
4. Convert to `ApiResponse` with pagination info in resource

This pattern ensures consistent, maintainable, and performant pagination across the entire application.
