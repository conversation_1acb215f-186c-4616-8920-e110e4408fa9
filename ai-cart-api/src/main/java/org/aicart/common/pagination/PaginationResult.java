package org.aicart.common.pagination;

import java.util.List;

/**
 * Generic pagination result that can be used across all entities
 * Eliminates the need for entity-specific pagination response classes
 */
public class PaginationResult<T> {
    
    private final List<T> data;
    private final long totalElements;
    private final int currentPage;
    private final int pageSize;
    private final int totalPages;
    private final boolean hasNext;
    private final boolean hasPrevious;

    public PaginationResult(List<T> data, long totalElements, int currentPage, int pageSize) {
        this.data = data;
        this.totalElements = totalElements;
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        this.totalPages = (int) Math.ceil((double) totalElements / pageSize);
        this.hasNext = currentPage < totalPages - 1;
        this.hasPrevious = currentPage > 0;
    }

    // Static factory methods for cleaner creation
    public static <T> PaginationResult<T> of(List<T> data, long totalElements, int currentPage, int pageSize) {
        return new PaginationResult<>(data, totalElements, currentPage, pageSize);
    }

    public static <T> PaginationResult<T> empty(int currentPage, int pageSize) {
        return new PaginationResult<>(List.of(), 0L, currentPage, pageSize);
    }

    // Getters
    public List<T> getData() {
        return data;
    }

    public long getTotalElements() {
        return totalElements;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public int getPageSize() {
        return pageSize;
    }

    public int getTotalPages() {
        return totalPages;
    }

    public boolean isHasNext() {
        return hasNext;
    }

    public boolean isHasPrevious() {
        return hasPrevious;
    }

    public boolean isEmpty() {
        return data.isEmpty();
    }

    public int getSize() {
        return data.size();
    }
}
