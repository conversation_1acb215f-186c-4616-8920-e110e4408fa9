package org.aicart.common.exception;

import jakarta.ws.rs.core.Response;

/**
 * Business logic exception that should be handled gracefully
 */
public class BusinessException extends RuntimeException {
    
    private final String code;
    private final Response.Status status;

    public BusinessException(String code, String message) {
        super(message);
        this.code = code;
        this.status = Response.Status.BAD_REQUEST;
    }

    public BusinessException(String code, String message, Response.Status status) {
        super(message);
        this.code = code;
        this.status = status;
    }

    public BusinessException(String code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.status = Response.Status.BAD_REQUEST;
    }

    public BusinessException(String code, String message, Response.Status status, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.status = status;
    }

    public String getCode() {
        return code;
    }

    public Response.Status getStatus() {
        return status;
    }

    // Common business exceptions
    public static BusinessException notFound(String resource) {
        return new BusinessException("RESOURCE_NOT_FOUND", resource + " not found", Response.Status.NOT_FOUND);
    }

    public static BusinessException unauthorized(String message) {
        return new BusinessException("UNAUTHORIZED", message, Response.Status.UNAUTHORIZED);
    }

    public static BusinessException forbidden(String message) {
        return new BusinessException("FORBIDDEN", message, Response.Status.FORBIDDEN);
    }

    public static BusinessException badRequest(String message) {
        return new BusinessException("BAD_REQUEST", message, Response.Status.BAD_REQUEST);
    }

    public static BusinessException conflict(String message) {
        return new BusinessException("CONFLICT", message, Response.Status.CONFLICT);
    }
}
