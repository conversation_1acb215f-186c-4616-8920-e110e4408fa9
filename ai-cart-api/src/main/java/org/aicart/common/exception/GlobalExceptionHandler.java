package org.aicart.common.exception;

import org.aicart.common.response.ApiResponse;
import org.jboss.logging.Logger;

import io.smallrye.faulttolerance.api.RateLimitException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.ws.rs.BadRequestException;
import jakarta.ws.rs.ForbiddenException;
import jakarta.ws.rs.NotAuthorizedException;
import jakarta.ws.rs.NotFoundException;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriInfo;
import jakarta.ws.rs.ext.ExceptionMapper;
import jakarta.ws.rs.ext.Provider;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Provider
public class GlobalExceptionHandler implements ExceptionMapper<Throwable> {

    private static final Logger LOG = Logger.getLogger(GlobalExceptionHandler.class);

    @Context
    UriInfo uriInfo;

    @Override
    public Response toResponse(Throwable exception) {
        LOG.error("Exception occurred: " + exception.getMessage(), exception);

        ApiResponse<?> apiResponse;
        Response.Status status;

        if (exception instanceof BusinessException) {
            BusinessException be = (BusinessException) exception;
            apiResponse = ApiResponse.error(be.getCode(), be.getMessage());
            status = be.getStatus();
        } else if (exception instanceof ValidationException) {
            ValidationException ve = (ValidationException) exception;
            apiResponse = ApiResponse.validationError(ve.getValidationErrors());
            status = Response.Status.BAD_REQUEST;
        } else if (exception instanceof ConstraintViolationException) {
            ConstraintViolationException cve = (ConstraintViolationException) exception;
            Map<String, List<String>> validationErrors = new HashMap<>();
            
            Set<ConstraintViolation<?>> violations = cve.getConstraintViolations();
            for (ConstraintViolation<?> violation : violations) {
                String fieldName = violation.getPropertyPath().toString();
                String message = violation.getMessage();
                
                validationErrors.computeIfAbsent(fieldName, k -> new ArrayList<>()).add(message);
            }
            
            apiResponse = ApiResponse.validationError(validationErrors);
            status = Response.Status.BAD_REQUEST;
        } else if (exception instanceof NotAuthorizedException) {
            apiResponse = ApiResponse.unauthorized();
            status = Response.Status.UNAUTHORIZED;
        } else if (exception instanceof ForbiddenException) {
            apiResponse = ApiResponse.forbidden();
            status = Response.Status.FORBIDDEN;
        } else if (exception instanceof NotFoundException) {
            apiResponse = ApiResponse.notFound();
            status = Response.Status.NOT_FOUND;
        } else if (exception instanceof BadRequestException) {
            apiResponse = ApiResponse.error("BAD_REQUEST", "Bad request: " + exception.getMessage());
            status = Response.Status.BAD_REQUEST;
        } else if (exception instanceof WebApplicationException) {
            WebApplicationException wae = (WebApplicationException) exception;
            apiResponse = ApiResponse.error("WEB_APPLICATION_ERROR", wae.getMessage());
            status = Response.Status.fromStatusCode(wae.getResponse().getStatus());
        } else if (exception instanceof ThirdPartyException) {
            // For third-party exceptions like Stripe, we log the original error but return a generic message
            ThirdPartyException tpe = (ThirdPartyException) exception;
            LOG.error("Third-party service error: " + tpe.getOriginalException().getMessage(), tpe.getOriginalException());
            apiResponse = ApiResponse.error(tpe.getCode(), tpe.getMessage());
            status = tpe.getStatus();
        } else if (exception instanceof RateLimitException) {
            RateLimitException tpe = (RateLimitException) exception;
            long retryAfterSeconds = tpe.getRetryAfterMillis() / 1000;
            status = Response.Status.TOO_MANY_REQUESTS;
            apiResponse = ApiResponse.error(tpe.getCode(), tpe.getMessage());
            
        } else {
            // Generic server error for unexpected exceptions
            LOG.error("Unexpected error occurred", exception);
            apiResponse = ApiResponse.serverError();
            status = Response.Status.INTERNAL_SERVER_ERROR;
        }

        // Set the request path for debugging
        if (uriInfo != null) {
            apiResponse.setPath(uriInfo.getPath());
        }

        return Response.status(status)
                .entity(apiResponse)
                .build();
    }
}
