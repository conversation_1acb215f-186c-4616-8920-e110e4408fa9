package org.aicart.common.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> {
    
    @JsonProperty("success")
    private boolean success;
    
    @JsonProperty("data")
    private T data;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("error")
    private ErrorDetails error;
    
    @JsonProperty("pagination")
    private PaginationInfo pagination;
    
    @JsonProperty("timestamp")
    private LocalDateTime timestamp;
    
    @JsonProperty("path")
    private String path;

    // Constructors
    public ApiResponse() {
        this.timestamp = LocalDateTime.now();
    }

    public ApiResponse(boolean success, T data, String message) {
        this();
        this.success = success;
        this.data = data;
        this.message = message;
    }

    // Success responses
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(true, data, "Success");
    }

    public static <T> ApiResponse<T> success(T data, String message) {
        return new ApiResponse<>(true, data, message);
    }

    public static <T> ApiResponse<T> success(T data, PaginationInfo pagination) {
        ApiResponse<T> response = new ApiResponse<>(true, data, "Success");
        response.setPagination(pagination);
        return response;
    }

    public static <T> ApiResponse<T> success(T data, String message, PaginationInfo pagination) {
        ApiResponse<T> response = new ApiResponse<>(true, data, message);
        response.setPagination(pagination);
        return response;
    }

    // Error responses
    public static <T> ApiResponse<T> error(String message) {
        ApiResponse<T> response = new ApiResponse<>(false, null, message);
        response.setError(new ErrorDetails("GENERAL_ERROR", message, null));
        return response;
    }

    public static <T> ApiResponse<T> error(String code, String message) {
        ApiResponse<T> response = new ApiResponse<>(false, null, message);
        response.setError(new ErrorDetails(code, message, null));
        return response;
    }

    public static <T> ApiResponse<T> error(String code, String message, Map<String, List<String>> validationErrors) {
        ApiResponse<T> response = new ApiResponse<>(false, null, message);
        response.setError(new ErrorDetails(code, message, validationErrors));
        return response;
    }

    public static <T> ApiResponse<T> validationError(Map<String, List<String>> validationErrors) {
        ApiResponse<T> response = new ApiResponse<>(false, null, "Validation failed");
        response.setError(new ErrorDetails("VALIDATION_ERROR", "Validation failed", validationErrors));
        return response;
    }

    public static <T> ApiResponse<T> unauthorized() {
        ApiResponse<T> response = new ApiResponse<>(false, null, "Unauthorized");
        response.setError(new ErrorDetails("UNAUTHORIZED", "You are not authorized to perform this action", null));
        return response;
    }

    public static <T> ApiResponse<T> forbidden() {
        ApiResponse<T> response = new ApiResponse<>(false, null, "Forbidden");
        response.setError(new ErrorDetails("FORBIDDEN", "Access denied", null));
        return response;
    }

    public static <T> ApiResponse<T> notFound() {
        ApiResponse<T> response = new ApiResponse<>(false, null, "Resource not found");
        response.setError(new ErrorDetails("NOT_FOUND", "The requested resource was not found", null));
        return response;
    }

    public static <T> ApiResponse<T> serverError() {
        ApiResponse<T> response = new ApiResponse<>(false, null, "Internal server error");
        response.setError(new ErrorDetails("SERVER_ERROR", "An unexpected server error occurred", null));
        return response;
    }

    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public ErrorDetails getError() {
        return error;
    }

    public void setError(ErrorDetails error) {
        this.error = error;
    }

    public PaginationInfo getPagination() {
        return pagination;
    }

    public void setPagination(PaginationInfo pagination) {
        this.pagination = pagination;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    // Inner classes
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ErrorDetails {
        @JsonProperty("code")
        private String code;
        
        @JsonProperty("message")
        private String message;
        
        @JsonProperty("validation_errors")
        private Map<String, List<String>> validationErrors;

        public ErrorDetails() {}

        public ErrorDetails(String code, String message, Map<String, List<String>> validationErrors) {
            this.code = code;
            this.message = message;
            this.validationErrors = validationErrors;
        }

        // Getters and Setters
        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Map<String, List<String>> getValidationErrors() {
            return validationErrors;
        }

        public void setValidationErrors(Map<String, List<String>> validationErrors) {
            this.validationErrors = validationErrors;
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PaginationInfo {
        @JsonProperty("current_page")
        private int currentPage;
        
        @JsonProperty("page_size")
        private int pageSize;
        
        @JsonProperty("total_elements")
        private long totalElements;
        
        @JsonProperty("total_pages")
        private int totalPages;
        
        @JsonProperty("has_next")
        private boolean hasNext;
        
        @JsonProperty("has_previous")
        private boolean hasPrevious;

        public PaginationInfo() {}

        public PaginationInfo(int currentPage, int pageSize, long totalElements, int totalPages) {
            this.currentPage = currentPage;
            this.pageSize = pageSize;
            this.totalElements = totalElements;
            this.totalPages = totalPages;
            this.hasNext = currentPage < totalPages - 1;
            this.hasPrevious = currentPage > 0;
        }

        // Getters and Setters
        public int getCurrentPage() {
            return currentPage;
        }

        public void setCurrentPage(int currentPage) {
            this.currentPage = currentPage;
        }

        public int getPageSize() {
            return pageSize;
        }

        public void setPageSize(int pageSize) {
            this.pageSize = pageSize;
        }

        public long getTotalElements() {
            return totalElements;
        }

        public void setTotalElements(long totalElements) {
            this.totalElements = totalElements;
        }

        public int getTotalPages() {
            return totalPages;
        }

        public void setTotalPages(int totalPages) {
            this.totalPages = totalPages;
        }

        public boolean isHasNext() {
            return hasNext;
        }

        public void setHasNext(boolean hasNext) {
            this.hasNext = hasNext;
        }

        public boolean isHasPrevious() {
            return hasPrevious;
        }

        public void setHasPrevious(boolean hasPrevious) {
            this.hasPrevious = hasPrevious;
        }
    }
}
