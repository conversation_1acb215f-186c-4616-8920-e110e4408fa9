package org.aicart.content.page.repository;

import io.quarkus.hibernate.orm.panache.PanacheRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.TypedQuery;
import org.aicart.content.page.entity.Page;
import org.aicart.shop.entity.Shop;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing Page entities.
 * 
 * This repository provides data access operations for pages,
 * including search, pagination, and shop-specific queries.
 * 
 * <AUTHOR> Cart Team
 * @version 1.0
 * @since 1.0
 */
@ApplicationScoped
public class PageRepository implements PanacheRepository<Page> {

    @PersistenceContext
    EntityManager em;

    /**
     * Finds pages by shop with pagination, sorting, and search.
     * 
     * @param shop The shop to filter by
     * @param page The page number (0-based)
     * @param size The page size
     * @param sortField The field to sort by
     * @param ascending Whether to sort in ascending order
     * @param searchQuery The search query (optional)
     * @return List of pages matching the criteria
     */
    public List<Page> findByShop(Shop shop, int page, int size, String sortField, boolean ascending, String searchQuery) {
        StringBuilder jpql = new StringBuilder("SELECT p FROM Page p WHERE p.shop.id = :shopId");
        
        if (searchQuery != null && !searchQuery.trim().isEmpty()) {
            jpql.append(" AND (LOWER(p.name) LIKE LOWER(:search))");
        }
        
        jpql.append(" ORDER BY p.").append(sortField);
        if (!ascending) {
            jpql.append(" DESC");
        }
        
        TypedQuery<Page> query = em.createQuery(jpql.toString(), Page.class)
                .setParameter("shopId", shop.id)
                .setFirstResult(page * size)
                .setMaxResults(size);
        
        if (searchQuery != null && !searchQuery.trim().isEmpty()) {
            query.setParameter("search", "%" + searchQuery.trim() + "%");
        }
        
        return query.getResultList();
    }

    /**
     * Counts pages by shop with optional search.
     * 
     * @param shop The shop to filter by
     * @param searchQuery The search query (optional)
     * @return The count of pages matching the criteria
     */
    public long countByShop(Shop shop, String searchQuery) {
        StringBuilder jpql = new StringBuilder("SELECT COUNT(p) FROM Page p WHERE p.shop.id = :shopId");
        
        if (searchQuery != null && !searchQuery.trim().isEmpty()) {
            jpql.append(" AND (LOWER(p.name) LIKE LOWER(:search))");
        }
        
        TypedQuery<Long> query = em.createQuery(jpql.toString(), Long.class)
                .setParameter("shopId", shop.id);
        
        if (searchQuery != null && !searchQuery.trim().isEmpty()) {
            query.setParameter("search", "%" + searchQuery.trim() + "%");
        }
        
        return query.getSingleResult();
    }

    /**
     * Finds a page by ID and shop.
     * 
     * @param id The page ID
     * @param shop The shop
     * @return Optional containing the page if found
     */
    public Optional<Page> findByIdAndShop(Long id, Shop shop) {
        return find("id = ?1 AND shop.id = ?2", id, shop.id).firstResultOptional();
    }

    /**
     * Finds a page by slug and shop.
     * 
     * @param slug The page slug
     * @param shop The shop
     * @return Optional containing the page if found
     */
    public Optional<Page> findBySlugAndShop(String slug, Shop shop) {
        return find("slug = ?1 AND shop.id = ?2", slug, shop.id).firstResultOptional();
    }

    /**
     * Checks if a page name exists in a shop.
     * 
     * @param name The page name
     * @param shop The shop
     * @param excludeId The ID to exclude from the check (for updates)
     * @return true if the name exists, false otherwise
     */
    public boolean existsByNameAndShop(String name, Shop shop, Long excludeId) {
        if (excludeId != null) {
            return count("name = ?1 AND shop.id = ?2 AND id != ?3", name, shop.id, excludeId) > 0;
        } else {
            return count("name = ?1 AND shop.id = ?2", name, shop.id) > 0;
        }
    }

    /**
     * Checks if a page slug exists in a shop.
     * 
     * @param slug The page slug
     * @param shop The shop
     * @param excludeId The ID to exclude from the check (for updates)
     * @return true if the slug exists, false otherwise
     */
    public boolean existsBySlugAndShop(String slug, Shop shop, Long excludeId) {
        if (excludeId != null) {
            return count("slug = ?1 AND shop.id = ?2 AND id != ?3", slug, shop.id, excludeId) > 0;
        } else {
            return count("slug = ?1 AND shop.id = ?2", slug, shop.id) > 0;
        }
    }
}
