package org.aicart.content.media.resource;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.content.media.service.MediaService;
import org.aicart.content.media.dto.FileRequestDTO;
import org.aicart.content.media.dto.MediaFileDTO;
import org.aicart.content.media.dto.MediaListResponse;
import org.aicart.content.media.dto.MediaUpdateDTO;
import org.aicart.content.media.entity.FileStorage;
import io.quarkus.security.Authenticated;

import org.aicart.shop.annotation.RequireShopId;
import org.aicart.shop.context.ShopContext;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;
import java.util.Map;

@Path("/media")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequireShopId
public class MediaResource {

    @Inject
    MediaService mediaService;

    @Inject
    ShopContext shopContext;

    @POST
    @Authenticated
    @Path("store")
    public Response store(@Valid FileRequestDTO fileRequestDTO) {

        if (fileRequestDTO == null) {
            throw BusinessException.badRequest("Request body is required");
        }

        try {
            FileStorage file = mediaService.store(shopContext.getShop(), fileRequestDTO);
            return Response.ok(ApiResponse.success(file, "File stored successfully")).build();
        } catch (Exception e) {
            throw BusinessException.badRequest("Failed to store file: " + e.getMessage());
        }
    }

    @GET
    @Authenticated
    public Response list(
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("10") int size,
            @QueryParam("search") String search,
            @QueryParam("fileType") String fileType,
            @QueryParam("sortBy") @DefaultValue("createdAt") String sortBy,
            @QueryParam("order") @DefaultValue("desc") String order) {

        MediaListResponse response = mediaService.findAllWithFilters(shopContext.getShop(), search, fileType, page, size, sortBy, order);
        return Response.ok(ApiResponse.success(response)).build();
    }

    @GET
    @Authenticated
    @Path("/{id}")
    public Response getById(@PathParam("id") Long id) {
        MediaFileDTO file = mediaService.findById(shopContext.getShop(), id);
        if (file == null) {
            throw BusinessException.notFound("Media file");
        }
        return Response.ok(ApiResponse.success(file)).build();
    }

    @PUT
    @Authenticated
    @Path("/{id}")
    public Response update(@PathParam("id") Long id, @Valid MediaUpdateDTO updateDTO) {
        MediaFileDTO updatedFile = mediaService.updateMedia(shopContext.getShop(), id, updateDTO);
        return Response.ok(ApiResponse.success(updatedFile, "Media file updated successfully")).build();
    }

    @DELETE
    @Authenticated
    @Path("/{id}")
    public Response delete(@PathParam("id") Long id) {
        boolean deleted = mediaService.deleteMedia(shopContext.getShop(), id);
        if (!deleted) {
            throw BusinessException.notFound("Media file");
        }
        return Response.ok(ApiResponse.success(null, "Media file deleted successfully")).build();
    }
}
