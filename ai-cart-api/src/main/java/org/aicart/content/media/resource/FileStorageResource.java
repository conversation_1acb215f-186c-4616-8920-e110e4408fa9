package org.aicart.content.media.resource;

import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.content.media.service.FileStorageService;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.shop.context.ShopContext;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Path("/storage")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequireShopId
public class FileStorageResource {

    @Inject
    FileStorageService fileStorageService;

    @Inject
    ShopContext shopContext;

    @GET
    @Path("/files")
    public Response getFilesByIds(@QueryParam("ids") List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(Map.of("error", "File IDs are required"))
                    .build();
        }

        try {
            List<Long> fileIds = ids.stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList());

            Map<String, Object> files = fileStorageService.getFileItemsByIds(fileIds);
            return Response.ok(files).build();
        } catch (NumberFormatException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(Map.of("error", "Invalid file ID format"))
                    .build();
        }
    }

    @GET
    @Path("/media")
    public Response getMediaItems(@HeaderParam("Shop-Id") Long shopId) {
        var mediaItems = fileStorageService.getMediaItems(shopContext.getShopId());
        return Response.ok(mediaItems).build();
    }

    @GET
    @Path("/media/by-ids")
    public Response getMediaItemsByIds(@QueryParam("ids") List<String> ids,
                                       @HeaderParam("Shop-Id") Long shopId) {

        if (ids == null || ids.isEmpty()) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(Map.of("error", "Media IDs are required"))
                    .build();
        }

        try {
            List<Long> mediaIds = ids.stream()
                    .map(Long::valueOf)
                    .collect(Collectors.toList());

            var mediaItems = fileStorageService.getMediaItemsByIds(mediaIds, shopContext.getShopId());
            return Response.ok(mediaItems).build();
        } catch (NumberFormatException e) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity(Map.of("error", "Invalid media ID format"))
                    .build();
        }
    }

    @PUT
    @Path("/media/{mediaId}")
    public Response updateMediaItem(@PathParam("mediaId") Long mediaId,
                                    Map<String, Object> updateData,
                                    @HeaderParam("Shop-Id") Long shopId) {

        Map<String, Object> result = fileStorageService.updateMediaItem(mediaId, updateData, shopContext.getShopId());
        
        if ((Boolean) result.get("success")) {
            return Response.ok(result).build();
        } else {
            return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
        }
    }

    @DELETE
    @Path("/media/{mediaId}")
    public Response deleteMediaItem(@PathParam("mediaId") Long mediaId,
                                    @HeaderParam("Shop-Id") Long shopId) {

        Map<String, Object> result = fileStorageService.deleteMediaItem(mediaId, shopContext.getShopId());
        
        if ((Boolean) result.get("success")) {
            return Response.ok(result).build();
        } else {
            return Response.status(Response.Status.BAD_REQUEST).entity(result).build();
        }
    }
}
