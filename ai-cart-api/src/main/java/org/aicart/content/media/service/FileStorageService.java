package org.aicart.content.media.service;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

@ApplicationScoped
public class FileStorageService {

    @Inject
    EntityManager entityManager;

    @SuppressWarnings("unchecked")
    public Map<String, Object> getFileItemsByIds(List<Long> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return Map.of();
        }

        String sql = """
            SELECT 
                fs.id,
                fs.original_url,
                fs.medium_url,
                fs.storage_location
            FROM file_storage fs
            WHERE fs.id = ANY(:fileIds)
        """;

        List<Object[]> results = entityManager.createNativeQuery(sql)
                .setParameter("fileIds", fileIds.toArray(new Long[0]))
                .getResultList();

        Map<String, Object> fileMap = new HashMap<>();
        for (Object[] row : results) {
            Long id = ((Number) row[0]).longValue();
            String storageLocation = (String) row[3];
            String originalUrl = (String) row[1];
            String mediumUrl = (String) row[2];

            Map<String, Object> fileItem = new HashMap<>();
            fileItem.put("id", id);
            fileItem.put("original_url", storageLocation + "/" + originalUrl);
            fileItem.put("medium_url", storageLocation + "/" + mediumUrl);

            fileMap.put(id.toString(), fileItem);
        }

        return fileMap;
    }

    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getMediaItems(Long shopId) {
        String sql = """
            SELECT
                f.id,
                f.created_at,
                f.file_name,
                f.updated_at,
                f.height,
                f.width,
                f.file_size,
                f.file_type,
                f.mime_type,
                f.alt_text,
                f.medium_url,
                f.original_url,
                f.storage_location,
                f.thumbnail_url
            FROM file_storage f
            WHERE f.shop_id = :shopId
            ORDER BY f.id DESC
        """;

        List<Object[]> results = entityManager.createNativeQuery(sql)
                .setParameter("shopId", shopId)
                .getResultList();

        return results.stream().map(row -> {
            Map<String, Object> media = new HashMap<>();
            media.put("id", ((Number) row[0]).longValue());
            media.put("created_at", row[1]);
            media.put("file_name", row[2]);
            media.put("updated_at", row[3]);
            media.put("height", row[4] != null ? ((Number) row[4]).intValue() : null);
            media.put("width", row[5] != null ? ((Number) row[5]).intValue() : null);
            media.put("file_size", row[6] != null ? ((Number) row[6]).longValue() : null);
            media.put("file_type", row[7]);
            media.put("mime_type", row[8]);
            media.put("alt_text", row[9]);
            media.put("medium_url", row[10]);
            media.put("original_url", row[11]);
            media.put("storage_location", row[12]);
            media.put("thumbnail_url", row[13]);
            return media;
        }).collect(java.util.stream.Collectors.toList());
    }

    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getMediaItemsByIds(List<Long> ids, Long shopId) {
        if (ids == null || ids.isEmpty()) {
            return List.of();
        }

        String sql = """
            SELECT
                f.id,
                f.created_at,
                f.file_name,
                f.updated_at,
                f.height,
                f.width,
                f.file_size,
                f.file_type,
                f.mime_type,
                f.alt_text,
                f.medium_url,
                f.original_url,
                f.storage_location,
                f.thumbnail_url
            FROM file_storage f
            WHERE f.id = ANY(:ids) AND f.shop_id = :shopId
            ORDER BY f.id DESC
        """;

        List<Object[]> results = entityManager.createNativeQuery(sql)
                .setParameter("ids", ids.toArray(new Long[0]))
                .setParameter("shopId", shopId)
                .getResultList();

        return results.stream().map(row -> {
            Map<String, Object> media = new HashMap<>();
            media.put("id", ((Number) row[0]).longValue());
            media.put("created_at", row[1]);
            media.put("file_name", row[2]);
            media.put("updated_at", row[3]);
            media.put("height", row[4] != null ? ((Number) row[4]).intValue() : null);
            media.put("width", row[5] != null ? ((Number) row[5]).intValue() : null);
            media.put("file_size", row[6] != null ? ((Number) row[6]).longValue() : null);
            media.put("file_type", row[7]);
            media.put("mime_type", row[8]);
            media.put("alt_text", row[9]);
            media.put("medium_url", row[10]);
            media.put("original_url", row[11]);
            media.put("storage_location", row[12]);
            media.put("thumbnail_url", row[13]);
            return media;
        }).collect(java.util.stream.Collectors.toList());
    }

    public Map<String, Object> updateMediaItem(Long mediaId, Map<String, Object> updateData, Long shopId) {
        // Build dynamic update query based on provided fields
        StringBuilder sqlBuilder = new StringBuilder("UPDATE file_storage SET ");
        Map<String, Object> parameters = new HashMap<>();
        
        boolean first = true;
        if (updateData.containsKey("alt_text")) {
            if (!first) sqlBuilder.append(", ");
            sqlBuilder.append("alt_text = :altText");
            parameters.put("altText", updateData.get("alt_text"));
            first = false;
        }
        
        if (updateData.containsKey("file_name")) {
            if (!first) sqlBuilder.append(", ");
            sqlBuilder.append("file_name = :fileName");
            parameters.put("fileName", updateData.get("file_name"));
            first = false;
        }

        if (first) {
            return Map.of("success", false, "message", "No fields to update");
        }

        sqlBuilder.append(" WHERE id = :mediaId AND shop_id = :shopId");
        parameters.put("mediaId", mediaId);
        parameters.put("shopId", shopId);

        try {
            var query = entityManager.createNativeQuery(sqlBuilder.toString());
            parameters.forEach(query::setParameter);
            
            int updated = query.executeUpdate();
            
            if (updated > 0) {
                return Map.of("success", true, "message", "Media item updated successfully");
            } else {
                return Map.of("success", false, "message", "Media item not found");
            }
        } catch (Exception e) {
            return Map.of("success", false, "message", "Failed to update media item");
        }
    }

    public Map<String, Object> deleteMediaItem(Long mediaId, Long shopId) {
        String sql = "DELETE FROM file_storage WHERE id = :mediaId AND shop_id = :shopId";

        try {
            int deleted = entityManager.createNativeQuery(sql)
                    .setParameter("mediaId", mediaId)
                    .setParameter("shopId", shopId)
                    .executeUpdate();

            if (deleted > 0) {
                return Map.of("success", true, "message", "Media item deleted successfully");
            } else {
                return Map.of("success", false, "message", "Media item not found");
            }
        } catch (Exception e) {
            return Map.of("success", false, "message", "Failed to delete media item");
        }
    }
}
