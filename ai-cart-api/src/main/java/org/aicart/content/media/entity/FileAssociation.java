package org.aicart.content.media.entity;

/**
 * Enumeration representing different types of file associations.
 *
 * This enum defines the various entities that can be associated with files
 * in the media system, such as products and categories.
 *
 * <AUTHOR> Cart Team
 * @version 1.0
 * @since 1.0
 */
public enum FileAssociation {
    /** File association with product entities */
    PRODUCT(1),

    /** File association with category entities */
    CATEGORY(2);

    private final int value;

    /**
     * Constructor for FileAssociation enum.
     *
     * @param value The numeric value representing the association type
     */
    FileAssociation(int value) {
        this.value = value;
    }

    /**
     * Gets the numeric value of the file association.
     *
     * @return The numeric value
     */
    public int getValue() {
        return value;
    }
}
