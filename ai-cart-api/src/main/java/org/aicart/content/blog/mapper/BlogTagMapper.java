package org.aicart.content.blog.mapper;

import org.aicart.content.blog.dto.BlogTagDTO;
import org.aicart.content.blog.dto.BlogTagTranslationDTO;
import org.aicart.content.blog.entity.BlogTag;
import org.aicart.content.blog.entity.BlogTagTranslation;

import java.util.stream.Collectors;

public class BlogTagMapper {
    
    public static BlogTagDTO toDto(BlogTag entity) {
        if (entity == null) {
            return null;
        }
        
        BlogTagDTO dto = new BlogTagDTO();
        dto.setId(entity.id);
        dto.setName(entity.name);
        dto.setSlug(entity.slug);
        dto.setColor(entity.color);
        dto.setShopId(entity.shop.id);
        dto.setCreatedAt(entity.createdAt);
        dto.setUpdatedAt(entity.updatedAt);
        
        // Map translations
        dto.setTranslations(entity.translations.stream()
                .map(BlogTagMapper::translationToDto)
                .collect(Collectors.toList()));
        
        return dto;
    }
    
    public static BlogTagTranslationDTO translationToDto(BlogTagTranslation entity) {
        if (entity == null) {
            return null;
        }
        
        BlogTagTranslationDTO dto = new BlogTagTranslationDTO();
        dto.setId(entity.id);
        dto.setLanguageId(entity.language.id);
        dto.setLanguageName(entity.language.name);
        dto.setName(entity.name);
        dto.setDescription(entity.description);
        dto.setCreatedAt(entity.createdAt);
        dto.setUpdatedAt(entity.updatedAt);
        
        return dto;
    }
}
