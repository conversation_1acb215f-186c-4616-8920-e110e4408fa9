package org.aicart.content.blog.resource;

import io.quarkus.security.Authenticated;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.content.blog.dto.BlogTagDTO;
import org.aicart.content.blog.service.BlogTagService;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.shop.context.ShopContext;
import org.aicart.shop.entity.Shop;
import org.aicart.common.response.ApiResponse;
import java.util.List;
import java.util.Map;

@Path("/blog-tags")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequireShopId
public class BlogTagResource {
    
    @Inject
    BlogTagService tagService;

    @Inject
    ShopContext shopContext;
    
    @GET
    @Authenticated
    public Response list(
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("10") int size,
            @QueryParam("sort") @DefaultValue("name") String sortField,
            @QueryParam("direction") @DefaultValue("asc") String sortDirection,
            @QueryParam("search") String searchQuery) {

        Shop shop = shopContext.getShop();
        
        List<BlogTagDTO> tags = tagService.findByShop(
                shop, 
                page, 
                size, 
                sortField, 
                "asc".equalsIgnoreCase(sortDirection),
                searchQuery);
        
        long total = tagService.countByShop(shop, searchQuery);
        
        ApiResponse.PaginationInfo pagination = new ApiResponse.PaginationInfo(page, size, total, (int) Math.ceil((double) total / size));
        return Response.ok(ApiResponse.success(tags, pagination)).build();
    }
    
    @GET
    @Path("/{id}")
    @Authenticated
    public Response get(@PathParam("id") Long id) {
        BlogTagDTO tag = tagService.findById(id);
        return Response.ok(ApiResponse.success(tag)).build();
    }
    
    @POST
    @Authenticated
    public Response create(@Valid BlogTagDTO dto) {
        BlogTagDTO created = tagService.create(dto, shopContext.getShop());
        return Response.status(Response.Status.CREATED).entity(ApiResponse.success(created, "Blog tag created successfully")).build();
    }
    
    @PUT
    @Path("/{id}")
    @Authenticated
    public Response update(@PathParam("id") Long id, @Valid BlogTagDTO dto) {
        BlogTagDTO updated = tagService.update(id, dto, shopContext.getShop());
        return Response.ok(ApiResponse.success(updated, "Blog tag updated successfully")).build();
    }
    
    @DELETE
    @Path("/{id}")
    @Authenticated
    public Response delete(@PathParam("id") Long id) {
        tagService.delete(id, shopContext.getShop());
        return Response.ok(ApiResponse.success(null, "Blog tag updated successfully")).build();
    }
    
    @GET
    @Path("/public")
    public Response listPublic(
            @QueryParam("withCounts") @DefaultValue("false") boolean withCounts) {
        List<BlogTagDTO> tags;
        if (withCounts) {
            tags = tagService.findAllByShopWithCounts(shopContext.getShop());
        } else {
            tags = tagService.findAllByShop(shopContext.getShop());
        }

        return Response.ok(ApiResponse.success(tags)).build();
    }
}