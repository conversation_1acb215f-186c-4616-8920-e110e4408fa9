package org.aicart.content.blog.resource;

import io.quarkus.security.Authenticated;
import io.smallrye.common.annotation.Blocking;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.content.blog.dto.BlogDTO;
import org.aicart.content.blog.entity.BlogStatus;
import org.aicart.content.blog.service.BlogService;
import org.aicart.common.entity.Language;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.shop.context.ShopContext;
import org.aicart.shop.entity.Shop;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;

import java.util.List;
import java.util.Map;

@Path("/blogs")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequireShopId
public class BlogResource {
    
    @Inject
    BlogService blogService;

    @Inject
    ShopContext shopContext;
    
    @GET
    @Authenticated
    public Response list(
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("10") int size,
            @QueryParam("sort") @DefaultValue("createdAt") String sortField,
            @QueryParam("direction") @DefaultValue("desc") String sortDirection,
            @QueryParam("search") String searchQuery,
            @QueryParam("languageId") @DefaultValue("1") Long languageId) {

        Shop shop = shopContext.getShop();
        Language language = Language.findById(languageId);
        
        if (language == null) {
            throw BusinessException.notFound("Language");
        }
        
        List<BlogDTO> blogs = blogService.findByShop(
                shop, 
                page, 
                size, 
                sortField, 
                "asc".equalsIgnoreCase(sortDirection),
                searchQuery,
                language);
        
        long total = blogService.countByShop(shop, searchQuery, language);
        
        ApiResponse.PaginationInfo pagination = new ApiResponse.PaginationInfo(page, size, total, (int) Math.ceil((double) total / size));
        return Response.ok(ApiResponse.success(blogs, pagination)).build();
    }
    
    @GET
    @Path("/{id}")
    @Authenticated
    public Response get(@PathParam("id") Long id, 
                        @QueryParam("languageId") @DefaultValue("1") Long languageId) {
        Language language = Language.findById(languageId);
        
        if (language == null) {
            throw BusinessException.notFound("Language");
        }
        
        BlogDTO blog = blogService.findById(id, shopContext.getShop(), language);
        return Response.ok(ApiResponse.success(blog)).build();
    }
    
    @POST
    @Authenticated
    public Response create(@Valid BlogDTO dto) {
        BlogDTO created = blogService.create(dto, shopContext.getShop());
        return Response.status(Response.Status.CREATED).entity(created).build();
    }
    
    @PUT
    @Path("/{id}")
    @Authenticated
    public Response update(@PathParam("id") Long id, @Valid BlogDTO dto) {
        BlogDTO updated = blogService.update(id, dto, shopContext.getShop());
        return Response.ok(ApiResponse.success(updated, "Blog updated successfully")).build();
    }
    
    @DELETE
    @Path("/{id}")
    @Authenticated
    public Response delete(@PathParam("id") Long id) {
        blogService.delete(id, shopContext.getShop());
        return Response.ok(ApiResponse.success(null, "Blog deleted successfully")).build();
    }
    
    @PUT
    @Path("/{id}/status")
    @Authenticated
    public Response updateStatus(
            @PathParam("id") Long id,
            @QueryParam("status") @DefaultValue("DRAFT") BlogStatus status) {
        BlogDTO updated = blogService.updateStatus(id, status, shopContext.getShop());
        return Response.ok(ApiResponse.success(updated, "Blog status updated successfully")).build();
    }
    
    @GET
    @Path("/public")
    @Blocking
    public Response listPublic(
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("10") int size,
            @QueryParam("languageId") @DefaultValue("1") Long languageId,
            @QueryParam("search") String search,
            @QueryParam("categoryId") Long categoryId,
            @QueryParam("tagId") Long tagId,
            @QueryParam("year") Integer year,
            @QueryParam("month") Integer month,
            @QueryParam("day") Integer day) {
        
        Language language = Language.findById(languageId);
        if (language == null) {
            throw BusinessException.notFound("Language");
        }

        List<BlogDTO> blogs = blogService.findPublishedByShopWithFilters(
                shopContext.getShop(), page, size, language, search, categoryId, tagId, year, month, day);

        return Response.ok(ApiResponse.success(blogs)).build();
    }
    
    @GET
    @Path("/public/{slug}")
    @Blocking
    public Response getPublicBySlug(
            @PathParam("slug") String slug,
            @QueryParam("languageId") @DefaultValue("1") Long languageId) {

        Language language = Language.findById(languageId);
        if (language == null) {
            throw BusinessException.notFound("Language");
        }

        BlogDTO blog = blogService.findBySlug(slug, shopContext.getShop(), language);

        // Increment view count asynchronously
        new Thread(() -> blogService.incrementViewCount(blog.getId())).start();

        return Response.ok(ApiResponse.success(blog)).build();
    }
    
    @GET
    @Path("/public/category/{categoryId}")
    @Blocking
    public Response getByCategory(
            @PathParam("categoryId") Long categoryId,
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("10") int size,
            @QueryParam("languageId") @DefaultValue("1") Long languageId) {
        
        Language language = Language.findById(languageId);
        if (language == null) {
            throw BusinessException.notFound("Language");
        }
        
        List<BlogDTO> blogs = blogService.findByCategory(categoryId, shopContext.getShop(), page, size, language);
        
        return Response.ok(ApiResponse.success(Map.of(
                "data", blogs,
                "page", page,
                "size", size
        ))).build();
    }
    
    @GET
    @Path("/public/tag/{tagId}")
    @Blocking
    public Response getByTag(
            @PathParam("tagId") Long tagId,
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("10") int size,
            @QueryParam("languageId") @DefaultValue("1") Long languageId) {
        
        Language language = Language.findById(languageId);
        if (language == null) {
            throw BusinessException.notFound("Language");
        }
        
        List<BlogDTO> blogs = blogService.findByTag(tagId, shopContext.getShop(), page, size, language);
        
        return Response.ok(ApiResponse.success(Map.of(
                "data", blogs,
                "page", page,
                "size", size
        ))).build();
    }

    @GET
    @Path("/public/archives")
    public Response getArchives() {
        List<Map<String, Object>> archives = blogService.getBlogArchives(shopContext.getShop());
        return Response.ok(ApiResponse.success(archives)).build();
    }
}