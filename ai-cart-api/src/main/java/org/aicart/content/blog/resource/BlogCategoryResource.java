package org.aicart.content.blog.resource;

import io.quarkus.security.Authenticated;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.content.blog.dto.BlogCategoryDTO;
import org.aicart.content.blog.service.BlogCategoryService;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.shop.context.ShopContext;
import org.aicart.shop.entity.Shop;
import org.aicart.common.response.ApiResponse;

import java.util.List;

@Path("/blog-categories")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequireShopId
public class BlogCategoryResource {
    
    @Inject
    BlogCategoryService categoryService;

    @Inject
    ShopContext shopContext;
    
    @GET
    @Authenticated
    public Response list(
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("10") int size,
            @QueryParam("sort") @DefaultValue("name") String sortField,
            @QueryParam("direction") @DefaultValue("asc") String sortDirection,
            @QueryParam("search") String searchQuery) {

        Shop shop = shopContext.getShop();
        
        List<BlogCategoryDTO> categories = categoryService.findByShop(
                shop, 
                page, 
                size, 
                sortField, 
                "asc".equalsIgnoreCase(sortDirection),
                searchQuery);
        
        long total = categoryService.countByShop(shop, searchQuery);
        
        ApiResponse.PaginationInfo pagination = new ApiResponse.PaginationInfo(page, size, total, (int) Math.ceil((double) total / size));
        return Response.ok(ApiResponse.success(categories, pagination)).build();
    }
    
    @GET
    @Path("/{id}")
    @Authenticated
    public Response get(@PathParam("id") Long id) {
        BlogCategoryDTO category = categoryService.findById(shopContext.getShop(), id);
        return Response.ok(ApiResponse.success(category)).build();
    }
    
    @GET
    @Path("/root")
    @Authenticated
    public Response getRootCategories() {
        List<BlogCategoryDTO> categories = categoryService.findRootCategories(shopContext.getShop());
        return Response.ok(ApiResponse.success(categories)).build();
    }
    
    @GET
    @Path("/{parentId}/children")
    @Authenticated
    public Response getChildren(@PathParam("parentId") Long parentId) {
        List<BlogCategoryDTO> children = categoryService.findChildren(shopContext.getShop(), parentId);
        return Response.ok(ApiResponse.success(children)).build();
    }
    
    @POST
    @Authenticated
    public Response create(@Valid BlogCategoryDTO dto) {
        BlogCategoryDTO created = categoryService.create(dto, shopContext.getShop());
        return Response.status(Response.Status.CREATED).entity(ApiResponse.success(created, "Blog category created successfully")).build();
    }
    
    @PUT
    @Path("/{id}")
    @Authenticated
    public Response update(@PathParam("id") Long id, @Valid BlogCategoryDTO dto) {
        BlogCategoryDTO updated = categoryService.update(id, dto, shopContext.getShop());
        return Response.ok(ApiResponse.success(updated, "Blog category updated successfully")).build();
    }
    
    @DELETE
    @Path("/{id}")
    @Authenticated
    public Response delete(@PathParam("id") Long id) {
        categoryService.delete(id, shopContext.getShop());
        return Response.ok(ApiResponse.success(null, "Blog category updated successfully")).build();
    }
    
    @GET
    @Path("/public")
    public Response listPublic(
            @QueryParam("withCounts") @DefaultValue("false") boolean withCounts,
            @QueryParam("includeChildren") @DefaultValue("false") boolean includeChildren) {

        Shop shop = shopContext.getShop();

        List<BlogCategoryDTO> categories;
        if (includeChildren) {
            if (withCounts) {
                categories = categoryService.findAllCategoriesWithCounts(shop);
            } else {
                categories = categoryService.findAllCategories(shop);
            }
        } else {
            if (withCounts) {
                categories = categoryService.findRootCategoriesWithCounts(shop);
            } else {
                categories = categoryService.findRootCategories(shop);
            }
        }

        return Response.ok(ApiResponse.success(categories)).build();
    }
}
