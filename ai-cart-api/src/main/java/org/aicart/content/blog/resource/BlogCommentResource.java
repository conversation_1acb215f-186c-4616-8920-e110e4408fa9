package org.aicart.content.blog.resource;

import io.quarkus.security.Authenticated;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import org.aicart.content.blog.dto.BlogCommentDTO;
import org.aicart.content.blog.dto.BlogCommentReplyDTO;
import org.aicart.content.blog.service.BlogCommentService;
import org.aicart.shop.annotation.RequireShopId;
import org.aicart.shop.context.ShopContext;
import org.aicart.shop.entity.Shop;
import org.aicart.common.exception.BusinessException;
import org.aicart.common.response.ApiResponse;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.util.List;

@Path("/blog-comments")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequireShopId
public class BlogCommentResource {
    
    @Inject
    BlogCommentService commentService;
    
    @Inject
    JsonWebToken jwt;

    @Inject
    ShopContext shopContext;
    
    @GET
    @Authenticated
    public Response list(
            @QueryParam("blogId") Long blogId,
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("10") int size,
            @QueryParam("sort") @DefaultValue("createdAt") String sortField,
            @QueryParam("direction") @DefaultValue("desc") String sortDirection) {

        Shop shop = shopContext.getShop();
        
        List<BlogCommentDTO> comments = commentService.findByBlogAndShop(
                blogId,
                shop, 
                page, 
                size, 
                sortField, 
                "asc".equalsIgnoreCase(sortDirection));
        
        long total = commentService.countByBlogAndShop(blogId, shop);
        
        ApiResponse.PaginationInfo pagination = new ApiResponse.PaginationInfo(page, size, total, (int) Math.ceil((double) total / size));
        return Response.ok(ApiResponse.success(comments, pagination)).build();
    }
    
    @GET
    @Path("/{id}")
    @Authenticated
    public Response get(@PathParam("id") Long id) {
        BlogCommentDTO comment = commentService.findById(id, shopContext.getShop());
        return Response.ok(ApiResponse.success(comment)).build();
    }
    
    @POST
    public Response create(@Valid BlogCommentDTO dto) {
        Long userId = null;
        try {
            if (jwt != null && jwt.getSubject() != null) {
                userId = Long.parseLong(jwt.getSubject());
            }
        } catch (Exception e) {
            // Ignore if not authenticated
        }
        
        BlogCommentDTO created = commentService.create(dto, userId, shopContext.getShop());
        return Response.status(Response.Status.CREATED).entity(ApiResponse.success(created, "Comment created successfully")).build();
    }
    
    @PUT
    @Path("/{id}/approve")
    @Authenticated
    public Response approve(@PathParam("id") Long id) {
        BlogCommentDTO updated = commentService.approve(id, shopContext.getShop());
        return Response.ok(ApiResponse.success(updated, "Comment approved successfully")).build();
    }
    
    @DELETE
    @Path("/{id}")
    @Authenticated
    public Response delete(@PathParam("id") Long id) {
        commentService.delete(id, shopContext.getShop());
        return Response.ok(ApiResponse.success(null, "Comment updated successfully")).build();
    }
    
    @POST
    @Path("/{commentId}/replies")
    public Response addReply(@PathParam("commentId") Long commentId, @Valid BlogCommentReplyDTO dto) {
        Long userId = null;
        try {
            if (jwt != null && jwt.getSubject() != null) {
                userId = Long.parseLong(jwt.getSubject());
            }
        } catch (Exception e) {
            // Ignore if not authenticated
        }
        
        BlogCommentReplyDTO created = commentService.addReply(commentId, dto, userId);
        return Response.status(Response.Status.CREATED).entity(ApiResponse.success(created, "Reply created successfully")).build();
    }
    
    @PUT
    @Path("/replies/{id}/approve")
    @Authenticated
    public Response approveReply(@PathParam("id") Long id) {
        BlogCommentReplyDTO updated = commentService.approveReply(id, shopContext.getShop());
        return Response.ok(ApiResponse.success(updated, "Reply approved successfully")).build();
    }
    
    @DELETE
    @Path("/replies/{id}")
    @Authenticated
    public Response deleteReply(@PathParam("id") Long id) {
        commentService.deleteReply(id, shopContext.getShop());
        return Response.ok(ApiResponse.success(null, "Comment deleted successfully")).build();
    }
    
    @GET
    @Path("/public")
    public Response listPublic(
            @QueryParam("blogId") Long blogId,
            @QueryParam("page") @DefaultValue("0") int page,
            @QueryParam("size") @DefaultValue("10") int size) {
        
        List<BlogCommentDTO> comments = commentService.findApprovedByBlog(
                blogId, 
                page, 
                size);
        
        long total = commentService.countApprovedByBlog(blogId);
        
        ApiResponse.PaginationInfo pagination = new ApiResponse.PaginationInfo(page, size, total, (int) Math.ceil((double) total / size));
        return Response.ok(ApiResponse.success(comments, pagination)).build();
    }
}