-- Migration to create product_compares table for product comparison functionality

-- Create product_compares table
CREATE TABLE product_compares (
    id BIGSERIAL PRIMARY KEY,
    customer_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    variant_id BIGINT NOT NULL,
    shop_id BIGINT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_product_compares_customer_id 
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    CONSTRAINT fk_product_compares_product_id 
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    CONSTRAINT fk_product_compares_variant_id 
        FOREIGN KEY (variant_id) REFERENCES product_variants(id) ON DELETE CASCADE,
    CONSTRAINT fk_product_compares_shop_id 
        FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate entries
    CONSTRAINT product_compares_customer_id_variant_id_key 
        UNIQUE (customer_id, variant_id)
);

-- Create indexes for better performance
CREATE INDEX idx_product_compares_customer_id ON product_compares(customer_id);
CREATE INDEX idx_product_compares_variant_id ON product_compares(variant_id);
CREATE INDEX idx_product_compares_product_id ON product_compares(product_id);
CREATE INDEX idx_product_compares_shop_id ON product_compares(shop_id);
CREATE INDEX idx_product_compares_customer_shop ON product_compares(customer_id, shop_id);
CREATE INDEX idx_product_compares_created_at ON product_compares(created_at);

-- Add comments for documentation
COMMENT ON TABLE product_compares IS 'Stores product variants that customers want to compare';
COMMENT ON COLUMN product_compares.customer_id IS 'Reference to the customer who added the item to compare';
COMMENT ON COLUMN product_compares.product_id IS 'Reference to the product being compared';
COMMENT ON COLUMN product_compares.variant_id IS 'Reference to the specific product variant being compared';
COMMENT ON COLUMN product_compares.shop_id IS 'Reference to the shop that owns the product';
COMMENT ON COLUMN product_compares.created_at IS 'Timestamp when the item was added to compare list';
COMMENT ON COLUMN product_compares.updated_at IS 'Timestamp when the record was last updated';
