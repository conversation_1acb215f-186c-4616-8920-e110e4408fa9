-- Migration to add variant_id to wishlists table
-- This migration adds variant_id column and updates the unique constraint

-- Step 1: Add variant_id column (nullable initially)
ALTER TABLE wishlists ADD COLUMN variant_id BIGINT;

-- Step 2: Add foreign key constraint for variant_id
ALTER TABLE wishlists ADD CONSTRAINT fk_wishlists_variant_id 
    FOREIGN KEY (variant_id) REFERENCES product_variants(id) ON DELETE CASCADE;

-- Step 3: Update existing wishlist records to use the first variant of each product
UPDATE wishlists 
SET variant_id = (
    SELECT pv.id 
    FROM product_variants pv 
    WHERE pv.product_id = wishlists.product_id 
    ORDER BY pv.id 
    LIMIT 1
)
WHERE variant_id IS NULL;

-- Step 4: Make variant_id NOT NULL after populating data
ALTER TABLE wishlists ALTER COLUMN variant_id SET NOT NULL;

-- Step 5: Drop the old unique constraint
ALTER TABLE wishlists DROP CONSTRAINT IF EXISTS wishlists_customer_id_product_id_key;

-- Step 6: Add new unique constraint on customer_id and variant_id
ALTER TABLE wishlists ADD CONSTRAINT wishlists_customer_id_variant_id_key 
    UNIQUE (customer_id, variant_id);

-- Step 7: Create index for better performance
CREATE INDEX IF NOT EXISTS idx_wishlists_variant_id ON wishlists(variant_id);
CREATE INDEX IF NOT EXISTS idx_wishlists_customer_variant ON wishlists(customer_id, variant_id);
