quarkus.datasource.metrics.enabled=false
# Enable SQL logging
quarkus.hibernate-orm.log.sql=true

quarkus.hibernate-orm.database.generation=drop-and-create

#quarkus.hibernate-orm.log.sql=true
#quarkus.hibernate-orm.log.format-sql=true
#quarkus.log.category."org.hibernate".level=DEBUG

#quarkus.datasource.username=root
#quarkus.datasource.password=secret
#quarkus.datasource.jdbc.url=********************************************

#quarkus.oidc.auth-server-url=http://localhost:8081/realms/aicart
#quarkus.oidc.client-id=aicart
#quarkus.oidc.credentials.secret=n08PvrpL5fLIQG3ACCSy2AlEQP45At7T

#quarkus.datasource.devservices.port=56173
#quarkus.datasource.devservices.password=quarkus


minio.bucket-name=ai-cart
quarkus.minio.host=https://storage.aicart.store
quarkus.minio.secure=true
quarkus.minio.access-key=ENrgz4hNQ1GSCiZxrq8A
quarkus.minio.secret-key=qdIHv6fiObchcd2nAk8WrRa5Ou6fn6IFLB6j9AjG

quarkus.devservices.enabled=false

# JWT Auth configuration
mp.jwt.verify.publickey.location=publicKey.pem
smallrye.jwt.sign.key.location=privateKey.pem
mp.jwt.verify.issuer=https://aicart.store
quarkus.smallrye-jwt.enabled=true

quarkus.datasource.username=postgres
quarkus.datasource.password=postgres
quarkus.datasource.jdbc.url=****************************************

quarkus.datasource.jdbc.max-size=20
quarkus.datasource.db-kind=postgresql

sslcommerz.validationUrl=https://sandbox.sslcommerz.com/validator/api/validationserverAPI.php
sslcommerz.storeId=ll674e06430e6ce
sslcommerz.storePassword=ll674e06430e6ce@ssl

stripe.webhookSecret=whsec_95ce0281a7c58b3635b9b75ce69dfe90c99a5c8cfc8e6f9501704fdc3085cfaf

quarkus.mailer.from=<EMAIL>
quarkus.mailer.host=sandbox.smtp.mailtrap.io
quarkus.mailer.port=2525
quarkus.mailer.username=412d1c94c928db
quarkus.mailer.password=45581b43ece0c4
quarkus.mailer.tls=false
quarkus.mailer.start-tls=OPTIONAL

# In dev mode, prevent from using the mock SMTP server
quarkus.mailer.mock=false

quarkus.smallrye-openapi.info-title=AICart API
%dev.quarkus.smallrye-openapi.info-title=AICart API (development)
%test.quarkus.smallrye-openapi.info-title=AICart API (test)
quarkus.smallrye-openapi.info-version=1.0.0
quarkus.smallrye-openapi.info-description=AICart Ecommerce API service
quarkus.smallrye-openapi.info-contact-email=<EMAIL>
quarkus.smallrye-openapi.info-license-name=Apache 2.0
quarkus.smallrye-openapi.info-license-url=https://www.apache.org/licenses/LICENSE-2.0.html
