
"use server";


// Database client removed - now using backend APIs via server actions
import { serverApiClient } from "@/lib/api/server-client"
// Removed unused imports
import { CartPropRaw, CartProductRaw } from "@/types/db/cart-query";
import { cookies } from "next/headers";

export async function getCartDetail(): Promise<CartPropRaw|null> {
    try {
        // Import the getCommonHeader function from cart actions
        const { getCommonHeader } = await import("@/lib/actions/cart");
        const headers = await getCommonHeader(false); // Don't set cookies from this context
        const cart = await serverApiClient.getCart(headers);
        return cart as CartPropRaw;
    } catch (error) {
        console.error('Failed to fetch cart detail:', error);
        return null;
    }
}


  export async function getCartItems(_cartId: number): Promise<CartProductRaw[]>
  {
    // This function is now handled by the getCartDetail function above
    // which gets cart items from the backend API
    return [];
  }

  