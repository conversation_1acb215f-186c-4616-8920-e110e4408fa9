// Database client removed - now using backend APIs via server actions
import { serverApiClient } from "@/lib/api/server-client"
import type { ProductFilterParams } from '@/lib/actions/product';
import { getCurrentLangId, getCurrentLocationId } from "@/lib/utils";
import { getHostShop } from "@/lib/db/store/shop-query";

export async function minMaxPriceQuery() {
  try {
    const priceRange = await serverApiClient.getPriceRange();
    return {
      rows: [{ min_price: priceRange.min, max_price: priceRange.max }],
      rowCount: 1
    };
  } catch (error) {
    console.error('Failed to fetch price range:', error);
    return {
      rows: [{ min_price: 0, max_price: 1000 }],
      rowCount: 1
    };
  }
}

export async function getProductCountQuery(filters: ProductFilterParams) {
    try {
        const count = await serverApiClient.getProductCount({
            q: filters.q,
            categories: filters.categories,
            brands: filters.brands,
            minPrice: filters.minPrice,
            maxPrice: filters.maxPrice,
            attributes: filters.attributes,
            page: 1,
            pageSize: 1
        });
        return {
            rows: [{ count }],
            rowCount: 1
        };
    } catch (error) {
        console.error('Failed to fetch product count:', error);
        return {
            rows: [{ count: 0 }],
            rowCount: 1
        };
    }
}


export async function getPaginatedProducts(filters: ProductFilterParams) {
    try {
        const products = await serverApiClient.getProducts({
            q: filters.q,
            categories: filters.categories,
            brands: filters.brands,
            minPrice: filters.minPrice,
            maxPrice: filters.maxPrice,
            attributes: filters.attributes,
            sort: filters.sort,
            page: Number(filters.page) || 1,
            pageSize: Number(filters.pageSize) || 20
        });
        return {
            rows: products,
            rowCount: products.length
        };
    } catch (error) {
        console.error('Failed to fetch paginated products:', error);
        return {
            rows: [],
            rowCount: 0
        };
    }
}

export async function getSearchList({ query }: { query: string }) {
    try {
        const products = await serverApiClient.getProducts({
            q: query,
            page: 1,
            pageSize: 20
        });

        const searchResults = products.map((product: any) => ({
            product_id: product.id,
            product_name: product.name,
            slug: product.slug,
            locale_id: product.locale_id,
            locale_name: product.locale_name
        }));

        return {
            rows: searchResults,
            rowCount: searchResults.length
        };
    } catch (error) {
        console.error('Failed to fetch search list:', error);
        return {
            rows: [],
            rowCount: 0
        };
    }
}