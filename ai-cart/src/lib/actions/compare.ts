"use server";

import { unstable_noStore as noStore } from "next/cache";
import { env } from "@/env";
import { auth } from "~/auth";
import { getHostShop } from "@/lib/db/store/shop-query";

async function getAuthHeaders() {
  const session = await auth();
  const accessToken = session?.accessToken;

  if (!accessToken) {
    throw new Error("Authentication required");
  }

  const shop = await getHostShop()

  return {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': `Bearer ${accessToken}`,
    'Shop-Id': (shop?.id || '') as string,
  };
}

export interface CompareItem {
  id: number;
  product: {
    id: number;
    name: string;
    slug: string;
    images?: Array<{
      id: number;
      original_url: string;
      medium_url: string;
      score: number;
    }>;
    variants?: Array<{
      id: number;
      sku: string;
      price?: {
        price: number;
        sell_price: number;
        compare_price?: number;
        discount?: number;
        tax_rate?: number;
        currency_id?: number;
        discount_type?: string;
        discount_end_at?: number;
      };
      stock: number;
    }>;
  };
  variant: {
    id: number;
    sku: string;
    image_id?: number;
    price?: {
      price: number;
      sell_price: number;
      compare_price?: number;
      discount?: number;
      tax_rate?: number;
      currency_id?: number;
      discount_type?: string;
      discount_end_at?: number;
    };
    stock?: number;
    attributes?: Array<{
      id: number;
      name: string;
      value: string;
    }>;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CompareResponse {
  items: CompareItem[];
  totalCount: number;
  page: number;
  size: number;
  hasMore: boolean;
}

export async function addToCompare(variantId: number) {
  noStore();

  try {
    const headers = await getAuthHeaders();
    
    const res = await fetch(`${env.API_BASE_URL}/customers/compare`, {
      method: 'POST',
      headers,
      body: JSON.stringify({ variantId }),
    });

    if (!res.ok) {
      const errorData = await res.json();
      return { 
        success: false, 
        error: errorData.message || 'Failed to add to compare list' 
      };
    }

    return { success: true };
  } catch (error) {
    console.error('Error adding to compare:', error);
    return { 
      success: false, 
      error: 'Something went wrong' 
    };
  }
}

export async function removeFromCompare(variantId: number) {
  noStore();

  try {
    const headers = await getAuthHeaders();
    
    const res = await fetch(`${env.API_BASE_URL}/customers/compare/${variantId}`, {
      method: 'DELETE',
      headers,
    });

    if (!res.ok) {
      const errorData = await res.json();
      return { 
        success: false, 
        error: errorData.message || 'Failed to remove from compare list' 
      };
    }

    return { success: true };
  } catch (error) {
    console.error('Error removing from compare:', error);
    return { 
      success: false, 
      error: 'Something went wrong' 
    };
  }
}

export async function getCompareList(page: number = 0, size: number = 20): Promise<CompareResponse> {
  noStore();

  try {
    const headers = await getAuthHeaders();
    
    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
    });

    const res = await fetch(`${env.API_BASE_URL}/customers/compare?${params}`, {
      headers,
      cache: "no-store"
    });

    if (!res.ok) {
      throw new Error(`Failed to fetch compare list: ${res.statusText}`);
    }

    return await res.json();
  } catch (err) {
    console.error('Error fetching compare list:', err);
    return {
      items: [],
      totalCount: 0,
      page,
      size,
      hasMore: false,
    };
  }
}

export async function getCompareCount(): Promise<number> {
  noStore();

  try {
    const headers = await getAuthHeaders();
    
    const res = await fetch(`${env.API_BASE_URL}/customers/compare/count`, {
      headers,
      cache: "no-store"
    });

    if (!res.ok) {
      return 0;
    }

    const data = await res.json();
    return data.count || 0;
  } catch (err) {
    console.error('Error fetching compare count:', err);
    return 0;
  }
}

export async function clearCompareList() {
  noStore();

  try {
    const headers = await getAuthHeaders();
    
    const res = await fetch(`${env.API_BASE_URL}/customers/compare/clear`, {
      method: 'DELETE',
      headers,
    });

    if (!res.ok) {
      const errorData = await res.json();
      return { 
        success: false, 
        error: errorData.message || 'Failed to clear compare list' 
      };
    }

    return { success: true };
  } catch (error) {
    console.error('Error clearing compare list:', error);
    return { 
      success: false, 
      error: 'Something went wrong' 
    };
  }
}

export async function checkCompareStatus(variantId: number): Promise<boolean> {
  noStore();

  try {
    const headers = await getAuthHeaders();
    
    const res = await fetch(`${env.API_BASE_URL}/customers/compare/check/${variantId}`, {
      headers,
      cache: "no-store"
    });

    if (!res.ok) {
      return false;
    }

    const data = await res.json();
    return data.inCompare || false;
  } catch (err) {
    console.error('Error checking compare status:', err);
    return false;
  }
}

export async function getCompareVariantIds(): Promise<number[]> {
  noStore();

  try {
    const headers = await getAuthHeaders();

    const res = await fetch(`${env.API_BASE_URL}/customers/compare/variant-ids`, {
      headers,
      cache: "no-store"
    });

    if (!res.ok) {
      return [];
    }

    const data = await res.json();
    return data.variantIds || [];
  } catch (err) {
    console.error('Error fetching compare variant IDs:', err);
    return [];
  }
}

// Fetch products by variant IDs (for guest users on compare page)
export async function getProductsByVariantIds(variantIds: number[]): Promise<CompareItem[]> {
  noStore();

  if (variantIds.length === 0) {
    return [];
  }

  try {
    const headers = await getAuthHeaders();

    // Create query string with variant IDs
    const queryParams = variantIds.map(id => `variantIds=${id}`).join('&');

    const res = await fetch(`${env.API_BASE_URL}/products/variants/compare?${queryParams}`, {
      method: 'GET',
      headers,
    });

    if (!res.ok) {
      console.error('Failed to fetch products by variant IDs');
      return [];
    }

    const products = await res.json();

    // Convert to CompareItem format
    return products.map((product: any) => {
      const variant = product.variants.find((v: any) => variantIds.includes(v.id));
      return {
        id: variant?.id || product.id,
        variant_id: variant?.id || product.variants[0]?.id,
        product: product
      };
    });
  } catch (error) {
    console.error('Error fetching products by variant IDs:', error);
    return [];
  }
}
