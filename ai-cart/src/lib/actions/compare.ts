"use server";

import { unstable_noStore as noStore } from "next/cache";
import { env } from "@/env";
import { getHostShop } from "@/lib/db/store/shop-query";

// Simple function to get headers for API calls
async function getHeaders() {
  const shop = await getHostShop()

  return {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Shop-Id': (shop?.id || '') as string,
  };
}

export interface CompareItem {
  id: number;
  variant_id: number;
  variant: {
    id: number;
    sku: string;
    price: {
      price: number;
      sell_price?: number;
      compare_price?: number;
    };
    stock?: number;
  };
  product: {
    id: number;
    name: string;
    slug: string;
    description?: string;
    images: Array<{
      id: number;
      original_url: string;
      medium_url?: string;
    }>;
  };
}

// Fetch products by variant IDs using backend filter query
export async function getProductsByVariantIds(variantIds: number[]): Promise<CompareItem[]> {
  noStore();

  if (variantIds.length === 0) {
    return [];
  }

  try {
    const headers = await getHeaders();

    // Use filter query to get only products with specific variant IDs
    const variantIdsParam = variantIds.join(',');
    const res = await fetch(`${env.API_BASE_URL}/product/?variantIds=${variantIdsParam}`, {
      method: 'GET',
      headers,
    });

    if (!res.ok) {
      console.error('Failed to fetch products by variant IDs');
      return [];
    }

    const products = await res.json();

    // Convert to CompareItem format
    const compareItems: CompareItem[] = [];

    for (const product of products) {
      if (product.variants && Array.isArray(product.variants)) {
        // Find the specific variant that was requested
        const requestedVariant = product.variants.find((v: any) => variantIds.includes(v.id));
        if (requestedVariant) {
          compareItems.push({
            id: requestedVariant.id,
            variant_id: requestedVariant.id,
            variant: {
              id: requestedVariant.id,
              sku: requestedVariant.sku || '',
              price: requestedVariant.price || { price: 0 },
              stock: requestedVariant.stock
            },
            product: {
              id: product.id,
              name: product.name,
              slug: product.slug,
              description: product.description,
              images: product.images || []
            }
          });
        }
      }
    }

    return compareItems;
  } catch (error) {
    console.error('Error fetching products by variant IDs:', error);
    return [];
  }
}


