"use server";

import { unstable_noStore as noStore } from "next/cache";
import { env } from "@/env";
import { getHostShop } from "@/lib/db/store/shop-query";

// Simple function to get headers for API calls
async function getHeaders() {
  const shop = await getHostShop()

  return {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Shop-Id': (shop?.id || '') as string,
  };
}

export interface CompareItem {
  id: number;
  variant_id: number;
  variant: {
    id: number;
    sku: string;
    price: {
      price: number;
      sell_price?: number;
      compare_price?: number;
    };
    stock?: number;
  };
  product: {
    id: number;
    name: string;
    slug: string;
    description?: string;
    images: Array<{
      id: number;
      original_url: string;
      medium_url?: string;
    }>;
  };
}

// Fetch products by variant IDs (for loading compare data)
export async function getProductsByVariantIds(variantIds: number[]): Promise<CompareItem[]> {
  noStore();

  if (variantIds.length === 0) {
    return [];
  }

  try {
    const headers = await getHeaders();

    // Create query string with variant IDs
    const queryParams = variantIds.map(id => `variantIds=${id}`).join('&');

    const res = await fetch(`${env.API_BASE_URL}/products/variants/compare?${queryParams}`, {
      method: 'GET',
      headers,
    });

    if (!res.ok) {
      console.error('Failed to fetch products by variant IDs');
      return [];
    }

    const products = await res.json();

    // Convert to CompareItem format
    return products.map((product: any) => {
      const variant = product.variants.find((v: any) => variantIds.includes(v.id));
      return {
        id: variant?.id || product.id,
        variant_id: variant?.id || product.variants[0]?.id,
        variant: {
          id: variant?.id || product.variants[0]?.id,
          sku: variant?.sku || product.variants[0]?.sku || '',
          price: variant?.price || product.variants[0]?.price || { price: 0 },
          stock: variant?.stock || product.variants[0]?.stock
        },
        product: {
          id: product.id,
          name: product.name,
          slug: product.slug,
          description: product.description,
          images: product.images || []
        }
      };
    });
  } catch (error) {
    console.error('Error fetching products by variant IDs:', error);
    return [];
  }
}


