"use server"

import { unstable_noStore as noStore } from "next/cache"
import { revalidatePath } from "next/cache"
import { auth } from "~/auth"
import { env } from "@/env"
import { getErrorMessage } from "@/lib/handle-error"
import { getHostShop } from "@/lib/db/store/shop-query"

async function getAuthHeaders() {
  const session = await auth();
  const accessToken = session?.accessToken;
  
  if (!accessToken) {
    throw new Error("Authentication required");
  }

  const shop = await getHostShop()

  return {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': `Bearer ${accessToken}`,
    'Shop-Id': (shop?.id || '') as string,
  };
}

export interface WishlistItem {
  id: number;
  product: {
    id: number;
    name: string;
    slug: string;
    images?: Array<{
      id: number;
      original_url: string;
      medium_url: string;
      score: number;
    }>;
    variants?: Array<{
      id: number;
      sku: string;
      price?: {
        price: number;
        sell_price: number;
        compare_price?: number;
        discount?: number;
        tax_rate?: number;
        currency_id?: number;
        discount_type?: string;
        discount_end_at?: number;
      };
      stock: number;
    }>;
  };
  variant: {
    id: number;
    sku: string;
    image_id?: number;
    price?: {
      price: number;
      sell_price: number;
      compare_price?: number;
      discount?: number;
      tax_rate?: number;
      currency_id?: number;
      discount_type?: string;
      discount_end_at?: number;
    };
    stock?: number;
    attributes?: Array<{
      id: number;
      name: string;
      value: string;
    }>;
  };
  createdAt: string;
  updatedAt: string;
}

export interface WishlistResponse {
  items: WishlistItem[];
  totalCount: number;
  page: number;
  size: number;
  hasMore: boolean;
}

export async function addToWishlist(variantId: number) {
  noStore();

  try {
    const headers = await getAuthHeaders();

    const res = await fetch(`${env.API_BASE_URL}/customers/wishlist`, {
      method: 'POST',
      headers,
      body: JSON.stringify({ variantId }),
    });

    if (!res.ok) {
      const error = await res.json().catch(() => ({ message: 'Failed to add to wishlist' }));
      return {
        data: null,
        error: error.message || 'Failed to add to wishlist',
      };
    }

    const data = await res.json();

    // Revalidate paths to trigger updates
    revalidatePath("/");

    return {
      data,
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}

export async function removeFromWishlist(variantId: number) {
  noStore();

  try {
    const headers = await getAuthHeaders();

    const res = await fetch(`${env.API_BASE_URL}/customers/wishlist/${variantId}`, {
      method: 'DELETE',
      headers,
    });

    if (!res.ok) {
      const error = await res.json().catch(() => ({ message: 'Failed to remove from wishlist' }));
      return {
        data: null,
        error: error.message || 'Failed to remove from wishlist',
      };
    }

    const data = await res.json();

    // Revalidate paths to trigger updates
    revalidatePath("/");

    return {
      data,
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}

export async function getWishlist(page: number = 0, size: number = 20): Promise<WishlistResponse> {
  noStore();

  try {
    const headers = await getAuthHeaders();
    
    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
    });

    const res = await fetch(`${env.API_BASE_URL}/customers/wishlist?${params}`, {
      headers,
      cache: "no-store"
    });

    if (!res.ok) {
      throw new Error(`Failed to fetch wishlist: ${res.statusText}`);
    }

    return await res.json();
  } catch (err) {
    console.error('Error fetching wishlist:', err);
    return {
      items: [],
      totalCount: 0,
      page,
      size,
      hasMore: false,
    };
  }
}

export async function checkWishlistStatus(productId: number): Promise<boolean> {
  noStore();

  try {
    const headers = await getAuthHeaders();
    
    const res = await fetch(`${env.API_BASE_URL}/customers/wishlist/check/${productId}`, {
      headers,
      cache: "no-store"
    });

    if (!res.ok) {
      return false;
    }

    const data = await res.json();
    return data.inWishlist || false;
  } catch (err) {
    console.error('Error checking wishlist status:', err);
    return false;
  }
}

export async function getWishlistCount(): Promise<number> {
  noStore();

  try {
    const headers = await getAuthHeaders();
    
    const res = await fetch(`${env.API_BASE_URL}/customers/wishlist/count`, {
      headers,
      cache: "no-store"
    });

    if (!res.ok) {
      return 0;
    }

    const data = await res.json();
    return data.count || 0;
  } catch (err) {
    console.error('Error fetching wishlist count:', err);
    return 0;
  }
}

export async function getWishlistProductIds(): Promise<number[]> {
  noStore();

  try {
    const headers = await getAuthHeaders();

    const res = await fetch(`${env.API_BASE_URL}/customers/wishlist/product-ids`, {
      headers,
      cache: "no-store"
    });

    if (!res.ok) {
      return [];
    }

    const data = await res.json();
    return data.productIds || [];
  } catch (err) {
    console.error('Error fetching wishlist product IDs:', err);
    return [];
  }
}

export async function getWishlistVariantIds(): Promise<number[]> {
  noStore();

  try {
    const headers = await getAuthHeaders();

    const res = await fetch(`${env.API_BASE_URL}/customers/wishlist/variant-ids`, {
      headers,
      cache: "no-store"
    });

    if (!res.ok) {
      return [];
    }

    const data = await res.json();
    return data.variantIds || [];
  } catch (err) {
    console.error('Error fetching wishlist variant IDs:', err);
    return [];
  }
}
