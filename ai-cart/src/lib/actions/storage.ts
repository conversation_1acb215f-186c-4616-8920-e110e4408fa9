"use server";

import { unstable_noStore as noStore } from "next/cache";
import { serverApiClient } from "@/lib/api/server-client";

export interface FileItem {
  id: number;
  original_url: string;
  medium_url: string;
}

export interface MediaItem {
  id: number;
  created_at: string;
  file_name: string;
  updated_at: string;
  height?: number;
  width?: number;
  file_size?: number;
  file_type: string;
  mime_type: string;
  alt_text?: string;
  medium_url: string;
  original_url: string;
  storage_location: string;
  thumbnail_url: string;
}

export async function fetchFileItemsByIds(fileIds: number[]): Promise<Record<number, FileItem>> {
  noStore();
  
  if (!fileIds || fileIds.length === 0) {
    return {};
  }

  try {
    const files = await serverApiClient.getFilesByIds(fileIds);
    return files as Record<number, FileItem>;
  } catch (error) {
    console.error('Failed to fetch file items:', error);
    return {};
  }
}

export async function fetchMediaItems(): Promise<MediaItem[]> {
  noStore();
  
  try {
    const mediaItems = await serverApiClient.getMediaItems();
    return mediaItems as MediaItem[];
  } catch (error) {
    console.error('Failed to fetch media items:', error);
    return [];
  }
}

export async function fetchMediaItemsByIds(ids: number[]): Promise<MediaItem[]> {
  noStore();
  
  if (!ids || ids.length === 0) {
    return [];
  }

  try {
    const mediaItems = await serverApiClient.getMediaItemsByIds(ids);
    return mediaItems as MediaItem[];
  } catch (error) {
    console.error('Failed to fetch media items by IDs:', error);
    return [];
  }
}

export async function updateMediaItem(mediaId: number, updateData: any): Promise<{ success: boolean; message: string }> {
  noStore();
  
  try {
    const result = await serverApiClient.updateMediaItem(mediaId, updateData);
    return result as { success: boolean; message: string };
  } catch (error) {
    console.error('Failed to update media item:', error);
    return { success: false, message: 'Failed to update media item' };
  }
}

export async function deleteMediaItem(mediaId: number): Promise<{ success: boolean; message: string }> {
  noStore();
  
  try {
    const result = await serverApiClient.deleteMediaItem(mediaId);
    return result as { success: boolean; message: string };
  } catch (error) {
    console.error('Failed to delete media item:', error);
    return { success: false, message: 'Failed to delete media item' };
  }
}
