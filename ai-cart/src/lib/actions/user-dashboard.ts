"use server";

import { unstable_noStore as noStore } from "next/cache";
import { serverApiClient } from "@/lib/api/server-client";

export interface UserStats {
  totalOrders: number;
  pendingOrder: number;
  spend: number;
  rewards: number;
}

export interface RecentOrder {
  id: number;
  date: string;
  status: number;
  total: number;
}

export interface OrderDetail {
  id: number;
  date: string;
  status: number;
  total: number;
  currency: string;
  billing?: any;
  shipping?: any;
}

export interface UserBilling {
  id: number;
  city: string;
  country: string;
  created_at: string;
  email: string;
  fullName: string;
  line1: string;
  line2?: string;
  phone: string;
  postalCode: string;
  state: string;
  taxNumber?: string;
  updated_at: string;
  user_id: number;
  vatNumber?: string;
}

export interface UserShipping {
  id: number;
  city: string;
  country: string;
  created_at: string;
  fullName: string;
  line1: string;
  line2?: string;
  phone: string;
  postalCode: string;
  state: string;
  updated_at: string;
  user_id: number;
}

export interface UserProfile {
  id: number;
  name: string;
  email: string;
  verifiedAt?: string;
}

export async function fetchUserStats(userId: number): Promise<UserStats> {
  noStore();
  
  try {
    const stats = await serverApiClient.getUserStats(userId);
    return stats as UserStats;
  } catch (error) {
    console.error('Failed to fetch user stats:', error);
    return {
      totalOrders: 0,
      pendingOrder: 0,
      spend: 0,
      rewards: 0
    };
  }
}

export async function fetchUserRecentOrders(userId: number): Promise<RecentOrder[]> {
  noStore();
  
  try {
    const orders = await serverApiClient.getUserRecentOrders(userId);
    return orders as RecentOrder[];
  } catch (error) {
    console.error('Failed to fetch user recent orders:', error);
    return [];
  }
}

export async function fetchUserOrderDetail(userId: number, orderId: number): Promise<OrderDetail | null> {
  noStore();
  
  try {
    const order = await serverApiClient.getUserOrderDetail(userId, orderId);
    return order as OrderDetail;
  } catch (error) {
    console.error('Failed to fetch user order detail:', error);
    return null;
  }
}

export async function fetchUserBilling(userId: number): Promise<UserBilling | null> {
  noStore();
  
  try {
    const billing = await serverApiClient.getUserBilling(userId);
    return billing as UserBilling;
  } catch (error) {
    console.error('Failed to fetch user billing:', error);
    return null;
  }
}

export async function fetchUserShipping(userId: number): Promise<UserShipping | null> {
  noStore();
  
  try {
    const shipping = await serverApiClient.getUserShipping(userId);
    return shipping as UserShipping;
  } catch (error) {
    console.error('Failed to fetch user shipping:', error);
    return null;
  }
}

export async function fetchUserProfile(userId: number): Promise<UserProfile | null> {
  noStore();
  
  try {
    const profile = await serverApiClient.getUserProfile(userId);
    return profile as UserProfile;
  } catch (error) {
    console.error('Failed to fetch user profile:', error);
    return null;
  }
}

export async function fetchPasswordTokenValidity(token: string): Promise<boolean> {
  noStore();
  
  try {
    const result = await serverApiClient.getPasswordTokenValidity(token);
    return result.valid as boolean;
  } catch (error) {
    console.error('Failed to fetch password token validity:', error);
    return false;
  }
}
