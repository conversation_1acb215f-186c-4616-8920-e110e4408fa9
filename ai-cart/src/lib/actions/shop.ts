"use server";

import { unstable_noStore as noStore } from "next/cache";
import { serverApiClient } from "@/lib/api/server-client";
import { cache } from "react";

export interface HostShopData {
  id: number;
  countryCode: string;
  currencyCode: string;
  countryId: number;
  name: string;
  description: string;
  favicon_url?: string;
  logo_url?: string;
  support_phone?: string;
  support_email?: string;
  top_content_1?: string;
  footer_content?: string;
  footer_widget1?: string;
  footer_widget2?: string;
  footer_widget3?: string;
  footer_widget4?: string;
  footer_widget5?: string;
  social_links?: string;
}

export interface HostShopThemeData {
  id: number;
  header: unknown;
  footer: unknown;
  home_page: unknown;
  sections: unknown;
}

export interface ShopHighlight {
  id: number;
  icon: string;
  title: string;
  description: string;
}

export interface ShopBanner {
  id: number;
  background_type: 'image' | 'video';
  button: any;
  description?: string;
  end_date?: Date;
  tag: string;
  title: string;
  background?: any;
  poster?: any;
  url?: string;
}

function getSubdomain(host: string) {
  const baseDomain = process.env.AICART_DOMAIN || '';
  
  // Remove port number
  host = host.split(':')[0] || '';

  if (!host.endsWith(baseDomain)) return host;

  // Remove base domain and trailing dot
  const sub = host.slice(0, -baseDomain.length).replace(/\.$/, '');

  // If there's a subdomain, return it
  return sub || '';
}

export const fetchHostShopData = cache(async (host: string, countryCode: string): Promise<HostShopData | null> => {
  noStore();
  
  try {
    const shop = await serverApiClient.getShopByHost(host, countryCode);
    return shop as HostShopData;
  } catch (error) {
    console.error('Failed to fetch host shop data:', error);
    return null;
  }
});

export const fetchHostShopThemeData = cache(async (shopId: number): Promise<HostShopThemeData | null> => {
  noStore();
  
  try {
    const theme = await serverApiClient.getShopTheme(shopId);
    return theme as HostShopThemeData;
  } catch (error) {
    console.error('Failed to fetch host shop theme data:', error);
    return null;
  }
});

export const getHostShopByIdentity = async (host: string, countryCode: string): Promise<HostShopData | null> => {
  return fetchHostShopData(getSubdomain(host || ''), countryCode || '');
};

export const fetchShopHighlights = async (shopId: number): Promise<ShopHighlight[]> => {
  noStore();
  
  try {
    const highlights = await serverApiClient.getShopHighlights(shopId);
    return highlights as ShopHighlight[];
  } catch (error) {
    console.error('Failed to fetch shop highlights:', error);
    return [];
  }
};

export const fetchShopBanners = async (shopId: number): Promise<ShopBanner[]> => {
  noStore();
  
  try {
    const banners = await serverApiClient.getShopBanners(shopId);
    return banners as ShopBanner[];
  } catch (error) {
    console.error('Failed to fetch shop banners:', error);
    return [];
  }
};
