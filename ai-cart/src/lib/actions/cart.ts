"use server"

import { unstable_noStore as noStore } from "next/cache"
import { cookies } from 'next/headers'
import { revalidatePath } from "next/cache";
import { env } from "@/env";
// import { db } from "@/db"
// import { carts, categories, products, stores, subcategories } from "@/db/schema"
// import { and, asc, desc, eq, inArray, sql } from "drizzle-orm"
import { type z } from "zod"

import type { FormValues } from "@/lib/schema/checkout-address"

import { getErrorMessage } from "@/lib/handle-error"
import {
  type deleteCartItemsSchema,
} from "@/lib/validations/cart"
import { CartDeliveryInfo, CartProp } from "@/types/product";

import { serverApiClient } from "@/lib/api/server-client"
import { auth } from '~/auth'
import { getHostShop } from "@/lib/db/store/shop-query";



const getBaseUrl = (): URL => {
  const API_BASE_URL = env.API_BASE_URL;
  return new URL(`${API_BASE_URL}/carts`)
}

// For server actions - can set cookies
export const getCommonHeaderForActions = async (): Promise<Record<string, string>> => {
  const cookieStore = await cookies()
  let session = cookieStore.get("cart-session");

  // Generate a new session ID if one doesn't exist (similar to backend logic)
  if (!session?.value) {
    const newSessionId = `cart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    cookieStore.set("cart-session", newSessionId, {
      path: "/",
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
    });
    session = { value: newSessionId } as any;
  }

  const authSession = await auth();
  const shop = await getHostShop();

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Shop-Id': (shop?.id || '') as string,
  };

  if(authSession?.accessToken) {
    headers['Authorization'] = `Bearer ${authSession.accessToken}`;
  }

  if(session?.value) {
    headers['cookie'] = `cart-session=${session.value}`;
  }

  return headers;
}

// For server components - cannot set cookies, only read
export const getCommonHeaderForComponents = async (): Promise<Record<string, string>> => {
  const cookieStore = await cookies()
  const session = cookieStore.get("cart-session");

  const authSession = await auth();
  const shop = await getHostShop();

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Shop-Id': (shop?.id || '') as string,
  };

  if(authSession?.accessToken) {
    headers['Authorization'] = `Bearer ${authSession.accessToken}`;
  }

  if(session?.value) {
    headers['cookie'] = `cart-session=${session.value}`;
  }

  return headers;
}

// Legacy function for backward compatibility - defaults to action behavior
export const getCommonHeader = getCommonHeaderForActions;

export async function getCart(input?: {
  storeId: string
}): Promise<CartProp> {

  noStore()

  console.log(input);

  const defaultData: CartProp = {
    cartId: 0, currency: '', cartItems: [],
    step: 0
  };

  try {
    const headers = await getCommonHeaderForComponents();
    const rawCart = await serverApiClient.getCart(headers);

    if (!rawCart) {
      return defaultData;
    }

    // Use the existing cart transformer to properly transform the data
    const { cartTransform } = await import("@/lib/db/store/cart-transformer");
    const transformedCart = cartTransform(rawCart);
    return transformedCart;
  } catch (err) {
    console.error('Server API request error:', err);
    return defaultData;
  }







  // console.log("cart", cart);


  // (await cookies()).set({
  //   name: "cart-session",
  //   value: cart.sessionId,
  //   path: "/",
  //   httpOnly: true,         // Prevent access from client-side JavaScript
  //   secure: process.env.NODE_ENV === "production", // Use only over HTTPS in production
  //   sameSite: "lax",
  // })

  // cookieStore.set("cartId", cart.sessionId, {
  //   secure: true
  // })




      // revalidatePath("/")

  // console.log("carts", cart);

  // const cartId = null; //cookies().get("cartId")?.value

  // if (!cartId) return [];

  // console.log(input);

  // return [];

//   try {
//     const cart = await db.query.carts.findFirst({
//       columns: {
//         items: true,
//       },
//       where: eq(carts.id, cartId),
//     })

//     const productIds = cart?.items?.map((item) => item.productId) ?? []

//     if (productIds.length === 0) return []

//     const uniqueProductIds = [...new Set(productIds)]

//     const cartLineItems = await db
//       .select({
//         id: products.id,
//         name: products.name,
//         images: products.images,
//         category: categories.name,
//         subcategory: subcategories.name,
//         price: products.price,
//         inventory: products.inventory,
//         storeId: products.storeId,
//         storeName: stores.name,
//         storeStripeAccountId: stores.stripeAccountId,
//       })
//       .from(products)
//       .leftJoin(stores, eq(stores.id, products.storeId))
//       .leftJoin(categories, eq(categories.id, products.categoryId))
//       .leftJoin(subcategories, eq(subcategories.id, products.subcategoryId))
//       .where(
//         and(
//           inArray(products.id, uniqueProductIds),
//           input?.storeId ? eq(products.storeId, input.storeId) : undefined
//         )
//       )
//       .groupBy(products.id)
//       .orderBy(desc(stores.stripeAccountId), asc(products.createdAt))
//       .execute()
//       .then((items) => {
//         return items.map((item) => {
//           const quantity = cart?.items?.find(
//             (cartItem) => cartItem.productId === item.id
//           )?.quantity

//           return {
//             ...item,
//             quantity: quantity ?? 0,
//           }
//         })
//       })

//     return cartLineItems
//   } catch (err) {
//     return []
//   }
}

export async function getUniqueStoreIds() {
  noStore()

  const cartId = null; // cookies().get("cartId")?.value

  if (!cartId) return []

  return []

//   try {
//     const cart = await db
//       .selectDistinct({ storeId: products.storeId })
//       .from(carts)
//       .leftJoin(
//         products,
//         sql`JSON_CONTAINS(carts.items, JSON_OBJECT('productId', products.id))`
//       )
//       .groupBy(products.storeId)
//       .where(eq(carts.id, cartId))

//     const storeIds = cart.map((item) => item.storeId).filter((id) => id)

//     return storeIds
//   } catch (err) {
//     return []
//   }
}

export async function getCartItems(input: { cartId?: string }) {
  noStore()

  if (!input.cartId) return []

  return []

//   try {
//     const cart = await db.query.carts.findFirst({
//       where: eq(carts.id, input.cartId),
//     })

//     return cart?.items
//   } catch (err) {
//     return []
//   }
}

export async function addToCart(productId: number, variantId: number, quantity: number) {
  noStore()

  try {
    const headers = await getCommonHeader();
    const result = await serverApiClient.addToCart(productId, variantId, quantity, headers);

    revalidatePath("/");

    return {
      data: result,
      error: null,
    };
  } catch (error) {
    console.error('Add to cart error:', error);
    return {
      data: null,
      error: getErrorMessage("Failed to add product to cart"),
    };
  }

  // return {
  //   data: null,
  //   error: getErrorMessage("No data found"),
  // }

  /*

  try {
    const input = cartItemSchema.parse(rawInput)

    // Checking if product is in stock via server API
    try {
      const product = await serverApiClient.getProduct(input.productId);
      if (!product) {
        throw new Error("Product not found, please try again.");
      }

      if (product.inventory < input.quantity) {
        throw new Error("Product is out of stock, please try again later.");
      }
    } catch (error) {
      throw new Error("Failed to check product availability, please try again.");
    }

    const cookieStore = cookies()
    const cartId = cookieStore.get("cartId")?.value

    if (!cartId) {
      const cart = await db
        .insert(carts)
        .values({
          items: [input],
        })
        .returning({ insertedId: carts.id })

      // Note: .set() is only available in a Server Action or Route Handler
      cookieStore.set("cartId", String(cart[0]?.insertedId), {
        path: "/",
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
      })

      revalidatePath("/")
      return {
        data: [input],
        error: null,
      }
    }

    // Cart validation is now handled by the server API

    // Cart handling is now done by the server API

    if (cartItem) {
      cartItem.quantity += input.quantity
    } else {
      cart.items?.push(input)
    }

    await db
      .update(carts)
      .set({
        items: cart.items,
      })
      .where(eq(carts.id, cartId))

    revalidatePath("/")

    return {
      data: cart.items,
      error: null,
    }
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    }
  }

  */
}

export async function updateCartItem(cartItemId: number, quantity: number) {
  noStore()

  try {
    const result = await serverApiClient.updateCartItemQuantity(cartItemId, quantity);

    revalidatePath("/");

    return {
      data: result,
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }

  /*

  try {
    const input = cartItemSchema.parse(rawInput)

    const cartId = cookies().get("cartId")?.value

    if (!cartId) {
      throw new Error("cartId not found, please try again.")
    }

    // Cart validation is now handled by the server API

    if (input.quantity === 0) {
      cart.items =
        cart.items?.filter((item) => item.productId !== input.productId) ?? []
    } else {
      cartItem.quantity = input.quantity
    }

    await db
      .update(carts)
      .set({
        items: cart.items,
      })
      .where(eq(carts.id, cartId))

    revalidatePath("/")

    return {
      data: cart.items,
      error: null,
    }
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    }
  }

  */
}




export async function mergeGuestCart(accessToken: string) {
  noStore()

  const apiUrl = getBaseUrl();

  const headers = await getCommonHeader();
  headers['Authorization'] = `Bearer ${accessToken}`;

  if(headers?.cookie) return;


  console.log('headers: ', headers)

  fetch(`${apiUrl}/merge-guest`, {
      method: 'POST',
      cache: "no-store", // Disable caching for fresh data on each request
      credentials: "include",
      headers,
  });
}



export async function deleteCart() {
  noStore()

  return {
    data: null,
    error: null,
  }

  /*

  try {
    const cartId = cookies().get("cartId")?.value

    if (!cartId) {
      throw new Error("cartId not found, please try again.")
    }

    // Cart deletion is now handled by the server API

    revalidatePath("/")

    return {
      data: null,
      error: null,
    }
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    }
  }

  */
}

export async function deleteCartItem(cartItemId: number) {
  noStore()

  try {
    const headers = await getCommonHeader();
    const result = await serverApiClient.deleteCartItem(cartItemId, headers);

    revalidatePath("/");

    return {
      data: result,
      error: null,
    };
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    };
  }
}

export async function deleteCartItems(
  input: z.infer<typeof deleteCartItemsSchema>
) {
  noStore()


  console.log("input", input);

  try {
    const cartId = null; //cookies().get("cartId")?.value

    if (!cartId) {
      throw new Error("cartId not found, please try again.")
    }

    return {
        data: null,
        error: null,
      }


    /*

    const cart = await db.query.carts.findFirst({
      where: eq(carts.id, cartId),
    })

    if (!cart) return

    cart.items =
      cart.items?.filter(
        (item) => !input.productIds.includes(item.productId)
      ) ?? []

    await db
      .update(carts)
      .set({
        items: cart.items,
      })
      .where(eq(carts.id, cartId))

    revalidatePath("/")

    return {
      data: cart.items,
      error: null,
    }

    */
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    }
  }
}



export async function cartAddressUpdate(cartId: number, cartAddress: FormValues) {
  noStore()

  try {
    const apiUrl = getBaseUrl();

    const headers = await getCommonHeader();
  
    const res = await fetch(`${apiUrl}/update-cart-address/${cartId}`, {
        method: 'PUT',
        cache: "no-store", // Disable caching for fresh data on each request
        credentials: "include",
        headers,
        body: JSON.stringify(cartAddress),
    });
  
    if (!res.ok) {
      return {
        data: null,
        error: getErrorMessage("Failed to add product on cart"),
      }
    }
  
    const data = await res.json();

    revalidatePath("/")

    return {
      data: data,
      error: null,
    }

  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    }
  }

  /*

  try {
    const input = cartItemSchema.parse(rawInput)

    const cartId = cookies().get("cartId")?.value

    if (!cartId) {
      throw new Error("cartId not found, please try again.")
    }

    // Cart update is now handled by the server API

    revalidatePath("/")

    return {
      data: cart.items,
      error: null,
    }
  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    }
  }

  */
}



export async function cartDeliveryInfoUpdate(cartId: number, payload: CartDeliveryInfo) {
  noStore()

  try {
    const apiUrl = getBaseUrl();

    const headers = await getCommonHeader();
  
    const res = await fetch(`${apiUrl}/update-delivery-info/${cartId}`, {
        method: 'PUT',
        cache: "no-store", // Disable caching for fresh data on each request
        credentials: "include",
        headers,
        body: JSON.stringify(payload),
    });
  
    if (!res.ok) {
      return {
        data: null,
        error: getErrorMessage("Failed to add product on cart"),
      }
    }
  
    const data = await res.json();

    revalidatePath("/")

    return {
      data: data,
      error: null,
    }

  } catch (err) {
    return {
      data: null,
      error: getErrorMessage(err),
    }
  }
}



export async function verifyCoupon(cartId: number, couponCode: string) {

  noStore()

  try {

  const apiUrl = getBaseUrl();
  const headers = await getCommonHeader();

  const res = await fetch(`${apiUrl}/verify-coupon/${cartId}?couponCode=${couponCode}`, {
      cache: "no-store", // Disable caching for fresh data on each request
      credentials: "include",
      headers,
  });

  if (!res.ok) {
    return null;
  }

    const cart = await res.json();
    return cart

} catch (err) {
  console.log(err);
}
}


