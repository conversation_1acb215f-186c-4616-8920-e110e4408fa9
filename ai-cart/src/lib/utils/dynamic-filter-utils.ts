import { Product } from '@/types/product';
import { TreeCategory, BrandAttribute, ProductAttribute } from '@/lib/actions/attribute-action';

export interface DynamicFilterCounts {
  categories: TreeCategory[];
  brands: BrandAttribute[];
  attributes: ProductAttribute[];
}

export interface FilterParams {
  categories?: string;
  brands?: string;
  attributes?: string;
  minPrice?: string;
  maxPrice?: string;
  q?: string;
}

/**
 * Calculate dynamic filter counts based on current products and filters
 * This provides real-time filter counts that update based on current selections
 */
export function calculateDynamicFilterCounts(
  allProducts: Product[],
  currentFilters: FilterParams,
  staticFilters: {
    categories: TreeCategory[];
    brands: BrandAttribute[];
    attributes: ProductAttribute[];
  }
): DynamicFilterCounts {
  // Get products that match current filters (excluding the filter type we're calculating)
  const getFilteredProducts = (excludeFilterType?: 'categories' | 'brands' | 'attributes') => {
    return allProducts.filter(product => {
      // Apply price filter
      if (currentFilters.minPrice || currentFilters.maxPrice) {
        const minPrice = currentFilters.minPrice ? parseFloat(currentFilters.minPrice) : 0;
        const maxPrice = currentFilters.maxPrice ? parseFloat(currentFilters.maxPrice) : Infinity;
        const productPrice = product.price || 0;
        if (productPrice < minPrice || productPrice > maxPrice) {
          return false;
        }
      }

      // Apply search filter
      if (currentFilters.q) {
        const searchTerm = currentFilters.q.toLowerCase();
        const productName = product.name?.toLowerCase() || '';
        const productDescription = product.description?.toLowerCase() || '';
        if (!productName.includes(searchTerm) && !productDescription.includes(searchTerm)) {
          return false;
        }
      }

      // Apply category filter (unless we're calculating category counts)
      if (excludeFilterType !== 'categories' && currentFilters.categories) {
        const selectedCategories = currentFilters.categories.split(',').filter(Boolean);
        const productCategories = product.categories?.map(cat => cat.id.toString()) || [];
        if (!selectedCategories.some(catId => productCategories.includes(catId))) {
          return false;
        }
      }

      // Apply brand filter (unless we're calculating brand counts)
      if (excludeFilterType !== 'brands' && currentFilters.brands) {
        const selectedBrands = currentFilters.brands.split(',').filter(Boolean);
        const productBrandId = product.brand?.id?.toString();
        if (!productBrandId || !selectedBrands.includes(productBrandId)) {
          return false;
        }
      }

      // Apply attribute filter (unless we're calculating attribute counts)
      if (excludeFilterType !== 'attributes' && currentFilters.attributes) {
        const selectedAttributes = currentFilters.attributes.split(',').map(id => parseInt(id)).filter(id => !isNaN(id));
        const productAttributeIds = product.variants?.flatMap(variant => 
          variant.attributeValues?.map(av => av.id) || []
        ) || [];
        if (!selectedAttributes.some(attrId => productAttributeIds.includes(attrId))) {
          return false;
        }
      }

      return true;
    });
  };

  // Calculate category counts
  const calculateCategoryCounts = (categories: TreeCategory[]): TreeCategory[] => {
    const filteredProducts = getFilteredProducts('categories');
    
    return categories.map(category => {
      // Count products in this category and its children
      const countProductsInCategory = (catId: number): number => {
        return filteredProducts.filter(product => 
          product.categories?.some(cat => cat.id === catId)
        ).length;
      };

      const productCount = countProductsInCategory(category.id);
      
      // Recursively calculate counts for children
      const children = category.children ? calculateCategoryCounts(category.children) : undefined;
      
      return {
        ...category,
        product_count: productCount,
        children
      };
    }).filter(category => 
      // Hide categories with 0 products (unless they have children with products)
      category.product_count > 0 || (category.children && category.children.length > 0)
    );
  };

  // Calculate brand counts
  const calculateBrandCounts = (): BrandAttribute[] => {
    const filteredProducts = getFilteredProducts('brands');
    
    return staticFilters.brands.map(brand => {
      const productCount = filteredProducts.filter(product => 
        product.brand?.id === brand.id
      ).length;
      
      return {
        ...brand,
        product_count: productCount
      };
    }).filter(brand => brand.product_count > 0); // Hide brands with 0 products
  };

  // Calculate attribute counts
  const calculateAttributeCounts = (): ProductAttribute[] => {
    const filteredProducts = getFilteredProducts('attributes');
    
    return staticFilters.attributes.map(attribute => {
      const updatedValues = attribute.values.map(value => {
        const productCount = filteredProducts.filter(product =>
          product.variants?.some(variant =>
            variant.attributeValues?.some(av => av.id === value.attribute_value_id)
          )
        ).length;
        
        return {
          ...value,
          product_count: productCount
        };
      }).filter(value => value.product_count > 0); // Hide values with 0 products
      
      return {
        ...attribute,
        values: updatedValues
      };
    }).filter(attribute => attribute.values.length > 0); // Hide attributes with no values
  };

  return {
    categories: calculateCategoryCounts(staticFilters.categories),
    brands: calculateBrandCounts(),
    attributes: calculateAttributeCounts()
  };
}

/**
 * Check if a filter should be hidden based on product count
 */
export function shouldHideFilter(productCount: number): boolean {
  return productCount === 0;
}

/**
 * Format product count for display
 */
export function formatProductCount(count: number): string {
  if (count === 0) return '';
  if (count === 1) return '(1)';
  return `(${count})`;
}
