import { env } from '@/env';

/**
 * Server-side API client for making requests to the backend API
 * This should ONLY be used in server actions, never on the client side
 */
class ServerApiClient {
  private baseUrl: string;
  private defaultHeaders: HeadersInit;

  constructor() {
    this.baseUrl = env.API_BASE_URL || 'http://localhost:8080';
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      'Shop-Id': '1', // TODO: Make dynamic based on current shop
    };
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        console.error(`API request failed: ${response.status} ${response.statusText} for ${url}`);
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      return response.json();
    } catch (error) {
      console.error('Server API request error:', error);
      throw error;
    }
  }

  // Product API methods
  async getProducts(params: {
    page?: number;
    pageSize?: number;
    minPrice?: number;
    maxPrice?: number;
    q?: string;
    categoryIds?: string;
    brandIds?: string;
  }) {
    const searchParams = new URLSearchParams();

    if (params.page !== undefined) searchParams.set('page', params.page.toString());
    if (params.pageSize !== undefined) searchParams.set('pageSize', params.pageSize.toString());
    if (params.minPrice !== undefined) searchParams.set('minPrice', params.minPrice.toString());
    if (params.maxPrice !== undefined) searchParams.set('maxPrice', params.maxPrice.toString());
    if (params.q) searchParams.set('q', params.q);
    if (params.categoryIds) searchParams.set('categoryIds', params.categoryIds);
    if (params.brandIds) searchParams.set('brandIds', params.brandIds);

    const url = `/product/?${searchParams.toString()}`;

    try {
      const result = await this.request(url);
      return result;
    } catch (error) {
      console.error('Failed to fetch products:', error);
      throw error;
    }
  }

  async getProductBySlug(slug: string) {
    return this.request(`/product/detail/${slug}`);
  }

  async getProductCount(params: {
    q?: string;
    categoryIds?: string;
    brandIds?: string;
    minPrice?: number;
    maxPrice?: number;
  }) {
    const searchParams = new URLSearchParams();
    
    if (params.q) searchParams.set('q', params.q);
    if (params.categoryIds) searchParams.set('categoryIds', params.categoryIds);
    if (params.brandIds) searchParams.set('brandIds', params.brandIds);
    if (params.minPrice !== undefined) searchParams.set('minPrice', params.minPrice.toString());
    if (params.maxPrice !== undefined) searchParams.set('maxPrice', params.maxPrice.toString());

    return this.request<{ count: number }>(`/product/count?${searchParams.toString()}`);
  }

  async getPriceRange() {
    return this.request<{ min_price: number; max_price: number }>('/product/price-range');
  }

  async getFilterCategories() {
    return this.request('/product/filter-categories');
  }

  async getFilterBrands() {
    return this.request('/product/filter-brands');
  }

  async getFilterAttributes() {
    return this.request('/product/filter-attributes');
  }

  // User Dashboard API methods (server-side only)
  async getUserStats(userId: number) {
    return this.request(`/users/${userId}/stats`);
  }

  async getUserRecentOrders(userId: number) {
    return this.request(`/users/${userId}/recent-orders`);
  }

  async getUserOrderDetail(userId: number, orderId: number) {
    return this.request(`/users/${userId}/orders/${orderId}`);
  }

  async getUserBilling(userId: number) {
    return this.request(`/users/${userId}/billing`);
  }

  async getUserShipping(userId: number) {
    return this.request(`/users/${userId}/shipping`);
  }

  async getUserProfile(userId: number) {
    return this.request(`/users/${userId}/profile`);
  }

  async getPasswordTokenValidity(token: string) {
    return this.request(`/users/password-token/${token}/validity`);
  }

  // Shop API methods (server-side only)
  async getShopByHost(host: string, countryCode?: string) {
    const params = new URLSearchParams({ host });
    if (countryCode) params.append('countryCode', countryCode);
    return this.request(`/shop/by-host?${params.toString()}`);
  }

  async getShopTheme(shopId: number) {
    return this.request(`/shop/${shopId}/theme`);
  }

  async getShopHighlights(shopId: number) {
    return this.request(`/shop/${shopId}/highlights`);
  }

  async getShopBanners(shopId: number) {
    return this.request(`/shop/${shopId}/banners`);
  }

  // Cart API methods (server-side only) - Using original /carts API
  async getCart(headers: Record<string, string>) {
    const url = `${this.baseUrl}/carts`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          ...this.defaultHeaders,
          ...headers,
        },
      });

      // 204 No Content is expected when cart doesn't exist yet - return null instead of throwing
      if (response.status === 204) {
        return null;
      }

      if (!response.ok) {
        console.error(`API request failed: ${response.status} ${response.statusText} for ${url}`);
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      return response.json();
    } catch (error: any) {
      // If it's a network error or other non-204 error, re-throw it
      if (!error.message?.includes('204')) {
        console.error(`Network error for ${url}:`, error);
        throw error;
      }
      return null;
    }
  }

  async addToCart(productId: number, variantId: number | null, quantity: number, headers: Record<string, string>) {
    const url = `${this.baseUrl}/carts`;

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          ...this.defaultHeaders,
          ...headers,
        },
        body: JSON.stringify({
          productId,
          variantId,
          quantity
        })
      });

      if (!response.ok) {
        console.error(`API request failed: ${response.status} ${response.statusText} for ${url}`);
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      return response.json();
    } catch (error: any) {
      console.error(`Network error for ${url}:`, error);
      throw error;
    }
  }

  async updateCartItemQuantity(cartItemId: number, quantity: number, headers: Record<string, string>) {
    return this.request(`/carts/update-quantity/${cartItemId}`, {
      method: 'PUT',
      headers,
      body: JSON.stringify({ quantity })
    });
  }

  async deleteCartItem(cartItemId: number, headers: Record<string, string>) {
    return this.request(`/carts/delete/${cartItemId}`, {
      method: 'DELETE',
      headers
    });
  }

  // File Storage API methods (server-side only)
  async getFilesByIds(ids: number[]) {
    const params = new URLSearchParams();
    ids.forEach(id => params.append('ids', id.toString()));
    return this.request(`/storage/files?${params.toString()}`);
  }

  async getMediaItems() {
    return this.request('/storage/media');
  }

  async getMediaItemsByIds(ids: number[]) {
    const params = new URLSearchParams();
    ids.forEach(id => params.append('ids', id.toString()));
    return this.request(`/storage/media/by-ids?${params.toString()}`);
  }

  async updateMediaItem(mediaId: number, updateData: any) {
    return this.request(`/storage/media/${mediaId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData)
    });
  }

  async deleteMediaItem(mediaId: number) {
    return this.request(`/storage/media/${mediaId}`, {
      method: 'DELETE'
    });
  }

  // Product Reviews API methods (server-side only)
  async getProductReviews(params: {
    productId: number;
    page?: number;
    pageSize?: number;
    sort?: string;
  }) {
    const searchParams = new URLSearchParams();
    // Backend uses 0-based pagination, so convert from 1-based to 0-based
    if (params.page) searchParams.set('page', (params.page - 1).toString());
    if (params.pageSize) searchParams.set('size', params.pageSize.toString()); // Backend uses 'size' not 'pageSize'
    if (params.sort) searchParams.set('sort', params.sort);

    // Backend API structure: /product/{productId}/reviews
    return this.request(`/product/${params.productId}/reviews?${searchParams.toString()}`);
  }

  async getProductReviewSummary(productId: number) {
    // Since there's no dedicated review summary endpoint in the backend yet,
    // we'll return a default empty summary for now
    // TODO: Implement proper review summary endpoint in backend
    return {
      total_reviews: 0,
      average_rating: 0,
      one_count: 0,
      two_count: 0,
      three_count: 0,
      four_count: 0,
      five_count: 0
    };
  }

  // Collections API methods (server-side only)
  async getCollectionBySlug(slug: string) {
    return this.request(`/collections/${slug}`);
  }

  // Additional Shop API methods
  async getHostShopData(host: string, countryCode: string) {
    return this.getShopByHost(host, countryCode);
  }

  async getHostShopThemeData(shopId: number) {
    return this.getShopTheme(shopId);
  }

  async getHostShopByIdentity(host: string, countryCode: string) {
    return this.getShopByHost(host, countryCode);
  }

  // Navigation API methods (server-side only)
  async getNavigationMenu(name: string = 'main', lang: string = 'en') {
    return this.request(`/navigation-menus/public/${name}?lang=${lang}`);
  }

  async getNavigationMenuById(id: number) {
    return this.request(`/navigation-menus/${id}`);
  }

  async getAllNavigationMenus() {
    return this.request('/navigation-menus');
  }

  // Categories API methods (server-side only)
  async getCategoriesWithCount(params: {
    shopId: number;
    limit?: number;
    offset?: number;
  }) {
    const searchParams = new URLSearchParams();
    searchParams.set('shopId', params.shopId.toString());
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());

    return this.request(`/categories/with-count?${searchParams.toString()}`);
  }

  async getChildCategoriesWithCount(params: {
    parentCategoryId: number;
    shopId: number;
    limit?: number;
    offset?: number;
  }) {
    const searchParams = new URLSearchParams();
    searchParams.set('parentCategoryId', params.parentCategoryId.toString());
    searchParams.set('shopId', params.shopId.toString());
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());

    return this.request(`/categories/child-with-count?${searchParams.toString()}`);
  }

  async getRootCategoriesWithCount(params: {
    shopId: number;
    limit?: number;
    offset?: number;
  }) {
    const searchParams = new URLSearchParams();
    searchParams.set('shopId', params.shopId.toString());
    if (params.limit) searchParams.set('limit', params.limit.toString());
    if (params.offset) searchParams.set('offset', params.offset.toString());

    return this.request(`/categories/root-with-count?${searchParams.toString()}`);
  }

  async getAttributeQuery(shopId: number, langId: number) {
    return this.request(`/attributes?shopId=${shopId}&langId=${langId}`);
  }

  async getBrandAttributeQuery(shopId: number) {
    return this.request(`/brands?shopId=${shopId}`);
  }

  async getCategoryAttributeQuery(shopId: number, languageId: number) {
    return this.request(`/categories/attributes?shopId=${shopId}&languageId=${languageId}`);
  }
}

// Export a singleton instance for server-side use only
export const serverApiClient = new ServerApiClient();
