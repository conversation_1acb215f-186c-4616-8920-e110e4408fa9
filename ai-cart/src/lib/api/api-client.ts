"use server";

import { auth } from '~/auth';
import { getHostShop } from '@/lib/db/store/shop-query';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { ERROR_MESSAGES, getApiUrl } from './api-routes';

/**
 * Unified API response structure matching backend
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    code: string;
    message: string;
    validation_errors?: Record<string, string[]>;
  };
  pagination?: {
    current_page: number;
    page_size: number;
    total_elements: number;
    total_pages: number;
    has_next: boolean;
    has_previous: boolean;
  };
  timestamp?: string;
  path?: string;
}

/**
 * API Error class for structured error handling
 */
export class ApiError extends Error {
  constructor(
    public code: string,
    message: string,
    public status: number,
    public validationErrors?: Record<string, string[]>
  ) {
    super(message);
    this.name = 'ApiError';
  }

  static fromResponse(response: Response, data?: any): ApiError {
    const status = response.status;
    let code = 'UNKNOWN_ERROR';
    let message = ERROR_MESSAGES.UNKNOWN_ERROR;
    let validationErrors: Record<string, string[]> | undefined;

    if (data?.error) {
      code = data.error.code || code;
      message = data.error.message || message;
      validationErrors = data.error.validation_errors;
    } else {
      // Map HTTP status codes to error messages
      switch (status) {
        case 400:
          code = 'BAD_REQUEST';
          message = ERROR_MESSAGES.BAD_REQUEST;
          break;
        case 401:
          code = 'UNAUTHORIZED';
          message = ERROR_MESSAGES.UNAUTHORIZED;
          break;
        case 403:
          code = 'FORBIDDEN';
          message = ERROR_MESSAGES.FORBIDDEN;
          break;
        case 404:
          code = 'NOT_FOUND';
          message = ERROR_MESSAGES.NOT_FOUND;
          break;
        case 409:
          code = 'CONFLICT';
          message = ERROR_MESSAGES.CONFLICT;
          break;
        case 429:
          code = 'RATE_LIMIT_EXCEEDED';
          message = ERROR_MESSAGES.RATE_LIMIT_EXCEEDED;
          break;
        case 500:
          code = 'SERVER_ERROR';
          message = ERROR_MESSAGES.SERVER_ERROR;
          break;
        default:
          code = 'UNKNOWN_ERROR';
          message = ERROR_MESSAGES.UNKNOWN_ERROR;
      }
    }

    return new ApiError(code, message, status, validationErrors);
  }
}

/**
 * Request configuration interface
 */
export interface RequestConfig extends RequestInit {
  requireAuth?: boolean;
  includeCartSession?: boolean;
  timeout?: number;
}

/**
 * Centralized API client for server actions
 * Handles authentication, error handling, and response formatting
 */
export class ApiClient {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.API_BASE_URL || 'http://localhost:8080';
  }

  /**
   * Get authentication headers
   */
  private async getAuthHeaders(): Promise<Record<string, string>> {
    const session = await auth();
    const accessToken = session?.accessToken;
    
    if (!accessToken) {
      throw new ApiError('UNAUTHORIZED', ERROR_MESSAGES.UNAUTHORIZED, 401);
    }

    return {
      'Authorization': `Bearer ${accessToken}`,
    };
  }

  /**
   * Get cart session headers
   */
  private async getCartHeaders(): Promise<Record<string, string>> {
    const cookieStore = await cookies();
    const session = cookieStore.get("cart-session");
    
    if (session?.value) {
      return {
        'cookie': `cart-session=${session.value}`,
      };
    }
    
    return {};
  }

  /**
   * Get common headers including shop ID
   */
  private async getCommonHeaders(config: RequestConfig = {}): Promise<Record<string, string>> {
    const shop = await getHostShop();
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Shop-Id': (shop?.id || '') as string,
    };

    // Add authentication headers if required
    if (config.requireAuth) {
      const authHeaders = await this.getAuthHeaders();
      Object.assign(headers, authHeaders);
    }

    // Add cart session headers if required
    if (config.includeCartSession) {
      const cartHeaders = await this.getCartHeaders();
      Object.assign(headers, cartHeaders);
    }

    return headers;
  }

  /**
   * Handle API response and errors
   */
  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    let data: any;
    
    try {
      data = await response.json();
    } catch (error) {
      // If response is not JSON, create a generic error
      if (!response.ok) {
        throw ApiError.fromResponse(response);
      }
      data = null;
    }

    if (!response.ok) {
      const apiError = ApiError.fromResponse(response, data);
      
      // Handle specific error cases
      if (response.status === 401) {
        // Redirect to login on unauthorized
        redirect('/api/auth/signin');
      }
      
      throw apiError;
    }

    // Return the response as-is if it's already in our ApiResponse format
    if (data && typeof data === 'object' && 'success' in data) {
      return data as ApiResponse<T>;
    }

    // Wrap non-ApiResponse data in our format
    return {
      success: true,
      data: data as T,
      message: 'Success',
    };
  }

  /**
   * Make HTTP request with error handling
   */
  private async request<T>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<ApiResponse<T>> {
    const { requireAuth, includeCartSession, timeout = 30000, ...fetchConfig } = config;
    
    const url = getApiUrl(endpoint);
    const headers = await this.getCommonHeaders({ requireAuth, includeCartSession });

    // Merge headers
    const finalConfig: RequestInit = {
      ...fetchConfig,
      headers: {
        ...headers,
        ...fetchConfig.headers,
      },
    };

    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        ...finalConfig,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return await this.handleResponse<T>(response);
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new ApiError('TIMEOUT_ERROR', ERROR_MESSAGES.TIMEOUT_ERROR, 408);
        }
        
        // Network or other errors
        throw new ApiError('NETWORK_ERROR', ERROR_MESSAGES.NETWORK_ERROR, 0);
      }
      
      throw new ApiError('UNKNOWN_ERROR', ERROR_MESSAGES.UNKNOWN_ERROR, 0);
    }
  }

  /**
   * HTTP Methods
   */
  async get<T>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async put<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async patch<T>(endpoint: string, data?: any, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...config,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  async delete<T>(endpoint: string, config?: RequestConfig): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...config, method: 'DELETE' });
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
