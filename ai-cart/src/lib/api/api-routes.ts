/**
 * Centralized API endpoints configuration
 * All API routes should be defined here to avoid hardcoded URLs throughout the application
 */

export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
    REGISTER: '/auth/register',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    VERIFY_EMAIL: '/auth/verify-email',
  },

  // Users
  USERS: {
    LIST: '/users',
    CREATE: '/users',
    GET: (id: string | number) => `/users/${id}`,
    UPDATE: (id: string | number) => `/users/${id}`,
    DELETE: (id: string | number) => `/users/${id}`,
    STATS: (id: string | number) => `/users/${id}/stats`,
    RECENT_ORDERS: (id: string | number) => `/users/${id}/recent-orders`,
    ORDERS: (userId: string | number, orderId: string | number) => `/users/${userId}/orders/${orderId}`,
    BILLING: (id: string | number) => `/users/${id}/billing`,
    SHIPPING: (id: string | number) => `/users/${id}/shipping`,
    PASSWORD_TOKEN_VALIDITY: (token: string) => `/users/password-token/${token}/validity`,
  },

  // Products
  PRODUCTS: {
    LIST: '/product',
    CREATE: '/product',
    GET: (id: string | number) => `/product/${id}`,
    UPDATE: (id: string | number) => `/product/${id}`,
    DELETE: (id: string | number) => `/product/${id}`,
    SEARCH: '/product/search',
    PRICE_RANGE: '/product/price-range',
    FILTER_CATEGORIES: '/product/filter-categories',
    FILTER_BRANDS: '/product/filter-brands',
    FILTER_ATTRIBUTES: '/product/filter-attributes',
    BY_VARIANT_IDS: '/product/by-variant-ids',
  },

  // Categories
  CATEGORIES: {
    LIST: '/categories',
    CREATE: '/categories',
    GET: (id: string | number) => `/categories/${id}`,
    UPDATE: (id: string | number) => `/categories/${id}`,
    DELETE: (id: string | number) => `/categories/${id}`,
    TREE: '/categories/tree',
    PUBLIC: '/categories/public',
  },

  // Brands
  BRANDS: {
    LIST: '/brands',
    CREATE: '/brands',
    GET: (id: string | number) => `/brands/${id}`,
    UPDATE: (id: string | number) => `/brands/${id}`,
    DELETE: (id: string | number) => `/brands/${id}`,
    PUBLIC: '/brands/public',
  },

  // Cart
  CART: {
    GET: '/carts',
    ADD_ITEM: '/carts',
    UPDATE_ITEM: (itemId: string | number) => `/carts/items/${itemId}`,
    DELETE_ITEM: (itemId: string | number) => `/carts/items/${itemId}`,
    CLEAR: '/carts/clear',
    MERGE_GUEST: '/carts/merge-guest',
    UPDATE_ADDRESS: (cartId: string | number) => `/carts/${cartId}/address`,
    UPDATE_DELIVERY_INFO: (cartId: string | number) => `/carts/${cartId}/delivery-info`,
    VERIFY_COUPON: (cartId: string | number) => `/carts/${cartId}/verify-coupon`,
  },

  // Orders
  ORDERS: {
    LIST: '/orders',
    CREATE: '/orders',
    GET: (id: string | number) => `/orders/${id}`,
    UPDATE: (id: string | number) => `/orders/${id}`,
    DELETE: (id: string | number) => `/orders/${id}`,
    CANCEL: (id: string | number) => `/orders/${id}/cancel`,
    TRACK: (id: string | number) => `/orders/${id}/track`,
    REFUND: (id: string | number) => `/orders/${id}/refund`,
  },

  // Wishlist
  WISHLIST: {
    LIST: '/wishlist',
    ADD: '/wishlist',
    REMOVE: (variantId: string | number) => `/wishlist/${variantId}`,
    CLEAR: '/wishlist/clear',
    CHECK: (variantId: string | number) => `/wishlist/check/${variantId}`,
    VARIANT_IDS: '/wishlist/variant-ids',
  },

  // Reviews
  REVIEWS: {
    LIST: '/reviews',
    CREATE: '/reviews',
    GET: (id: string | number) => `/reviews/${id}`,
    UPDATE: (id: string | number) => `/reviews/${id}`,
    DELETE: (id: string | number) => `/reviews/${id}`,
    BY_PRODUCT: (productId: string | number) => `/reviews/product/${productId}`,
  },

  // Blog
  BLOG: {
    LIST: '/blog',
    CREATE: '/blog',
    GET: (id: string | number) => `/blog/${id}`,
    UPDATE: (id: string | number) => `/blog/${id}`,
    DELETE: (id: string | number) => `/blog/${id}`,
    BY_SLUG: (slug: string) => `/blog/slug/${slug}`,
    PUBLIC: '/blog/public',
    CATEGORIES: '/blog/categories',
    TAGS: '/blog/tags',
    COMMENTS: {
      LIST: (blogId: string | number) => `/blog/${blogId}/comments`,
      CREATE: (blogId: string | number) => `/blog/${blogId}/comments`,
      GET: (blogId: string | number, commentId: string | number) => `/blog/${blogId}/comments/${commentId}`,
      UPDATE: (blogId: string | number, commentId: string | number) => `/blog/${blogId}/comments/${commentId}`,
      DELETE: (blogId: string | number, commentId: string | number) => `/blog/${blogId}/comments/${commentId}`,
      REPLIES: {
        CREATE: (blogId: string | number, commentId: string | number) => `/blog/${blogId}/comments/${commentId}/replies`,
        UPDATE: (blogId: string | number, commentId: string | number, replyId: string | number) => `/blog/${blogId}/comments/${commentId}/replies/${replyId}`,
        DELETE: (blogId: string | number, commentId: string | number, replyId: string | number) => `/blog/${blogId}/comments/${commentId}/replies/${replyId}`,
      },
    },
  },

  // Pages
  PAGES: {
    LIST: '/pages',
    CREATE: '/pages',
    GET: (id: string | number) => `/pages/${id}`,
    UPDATE: (id: string | number) => `/pages/${id}`,
    DELETE: (id: string | number) => `/pages/${id}`,
    BY_SLUG: (slug: string) => `/pages/slug/${slug}`,
    PUBLIC: '/pages/public',
  },

  // Media/Storage
  MEDIA: {
    LIST: '/storage',
    UPLOAD: '/storage/upload',
    GET: (id: string | number) => `/storage/${id}`,
    UPDATE: (id: string | number) => `/storage/${id}`,
    DELETE: (id: string | number) => `/storage/${id}`,
    BULK_DELETE: '/storage/bulk-delete',
  },

  // Shop
  SHOP: {
    GET: (id: string | number) => `/shop/${id}`,
    BY_HOST: '/shop/by-host',
    THEME: (id: string | number) => `/shop/${id}/theme`,
    SETTINGS: (id: string | number) => `/shop/${id}/settings`,
  },

  // Navigation
  NAVIGATION: {
    MENU: '/navigation/menu',
    CATEGORIES: '/navigation/categories',
  },

  // Payments
  PAYMENTS: {
    STRIPE: {
      WEBHOOK: '/stripe/webhook',
      CREATE_INTENT: '/stripe/create-payment-intent',
      CONFIRM: '/stripe/confirm-payment',
    },
  },

  // Analytics
  ANALYTICS: {
    DASHBOARD: '/analytics/dashboard',
    SALES: '/analytics/sales',
    PRODUCTS: '/analytics/products',
    CUSTOMERS: '/analytics/customers',
  },

  // Inventory
  INVENTORY: {
    LIST: '/inventory',
    GET: (id: string | number) => `/inventory/${id}`,
    UPDATE: (id: string | number) => `/inventory/${id}`,
    BULK_UPDATE: '/inventory/bulk-update',
    LOW_STOCK: '/inventory/low-stock',
  },

  // Customers (Dashboard)
  CUSTOMERS: {
    LIST: '/customers',
    CREATE: '/customers',
    GET: (id: string | number) => `/customers/${id}`,
    UPDATE: (id: string | number) => `/customers/${id}`,
    DELETE: (id: string | number) => `/customers/${id}`,
    SEARCH: '/customers/search',
    TAGS: '/customers/tags',
    ADDRESSES: (id: string | number) => `/customers/${id}/addresses`,
    ORDERS: (id: string | number) => `/customers/${id}/orders`,
  },
} as const;

export const ERROR_MESSAGES = {
  // Network errors
  NETWORK_ERROR: 'Network error occurred. Please check your connection.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
  
  // Authentication errors
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied. You do not have permission to access this resource.',
  TOKEN_EXPIRED: 'Your session has expired. Please log in again.',
  INVALID_CREDENTIALS: 'Invalid email or password.',
  
  // Resource errors
  NOT_FOUND: 'The requested resource was not found.',
  RESOURCE_NOT_FOUND: 'Resource not found.',
  
  // Validation errors
  VALIDATION_ERROR: 'Please check your input and try again.',
  REQUIRED_FIELD: 'This field is required.',
  INVALID_EMAIL: 'Please enter a valid email address.',
  INVALID_PHONE: 'Please enter a valid phone number.',
  PASSWORD_TOO_SHORT: 'Password must be at least 8 characters long.',
  
  // Server errors
  SERVER_ERROR: 'An unexpected server error occurred. Please try again later.',
  BAD_REQUEST: 'Invalid request. Please check your input.',
  CONFLICT: 'A conflict occurred. The resource may already exist.',
  RATE_LIMIT_EXCEEDED: 'Rate limit exceeded. Please try again later.',
  MISSING_SHOP_ID: 'Shop-Id header is required for this request.',
  
  // Business logic errors
  INSUFFICIENT_STOCK: 'Insufficient stock available.',
  PRODUCT_NOT_AVAILABLE: 'This product is currently not available.',
  CART_EMPTY: 'Your cart is empty.',
  PAYMENT_FAILED: 'Payment processing failed. Please try again.',
  ORDER_NOT_FOUND: 'Order not found.',
  
  // Third-party service errors
  PAYMENT_GATEWAY_ERROR: 'Payment service is currently unavailable. Please try again later.',
  STRIPE_ERROR: 'Payment processing failed. Please try again.',
  EXTERNAL_SERVICE_ERROR: 'External service is currently unavailable. Please try again later.',
  
  // Generic fallback
  UNKNOWN_ERROR: 'An unknown error occurred. Please try again.',
} as const;

// Helper function to get full API URL
export const getApiUrl = (endpoint: string): string => {
  const baseUrl = process.env.API_BASE_URL || 'http://localhost:8080';
  return `${baseUrl}${endpoint}`;
};

// Helper function to build query parameters
export const buildQueryParams = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(v => searchParams.append(key, v.toString()));
      } else {
        searchParams.set(key, value.toString());
      }
    }
  });
  
  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : '';
};
