"use client"

import { Product } from '@/types/product'

export interface GuestCompareItem {
  variantId: number
  productId: number
  productName: string
  productSlug: string
  variantSku?: string
  price: number
  comparePrice?: number
  image?: string
  addedAt: number // timestamp
}

export interface GuestCompareData {
  items: GuestCompareItem[]
  maxItems: number
  lastUpdated: number
}

const STORAGE_KEY = 'guest_compare_items'
const MAX_COMPARE_ITEMS = 4
const STORAGE_VERSION = '1.0'

class GuestCompareStorage {
  private storageKey: string
  private maxItems: number

  constructor(storageKey = STORAGE_KEY, maxItems = MAX_COMPARE_ITEMS) {
    this.storageKey = storageKey
    this.maxItems = maxItems
  }

  // Check if localStorage is available
  private isStorageAvailable(): boolean {
    try {
      if (typeof window === 'undefined') return false
      const test = '__storage_test__'
      localStorage.setItem(test, test)
      localStorage.removeItem(test)
      return true
    } catch {
      return false
    }
  }

  // Get current compare data from localStorage
  private getStorageData(): GuestCompareData {
    if (!this.isStorageAvailable()) {
      return { items: [], maxItems: this.maxItems, lastUpdated: Date.now() }
    }

    try {
      const stored = localStorage.getItem(this.storageKey)
      if (!stored) {
        return { items: [], maxItems: this.maxItems, lastUpdated: Date.now() }
      }

      const parsed = JSON.parse(stored)
      
      // Validate data structure
      if (!parsed.items || !Array.isArray(parsed.items)) {
        return { items: [], maxItems: this.maxItems, lastUpdated: Date.now() }
      }

      // Ensure maxItems limit
      const items = parsed.items.slice(0, this.maxItems)
      
      return {
        items,
        maxItems: this.maxItems,
        lastUpdated: parsed.lastUpdated || Date.now()
      }
    } catch (error) {
      console.error('Error reading guest compare data:', error)
      return { items: [], maxItems: this.maxItems, lastUpdated: Date.now() }
    }
  }

  // Save compare data to localStorage
  private saveStorageData(data: GuestCompareData): void {
    if (!this.isStorageAvailable()) return

    try {
      const dataToSave = {
        ...data,
        version: STORAGE_VERSION,
        lastUpdated: Date.now()
      }
      localStorage.setItem(this.storageKey, JSON.stringify(dataToSave))
    } catch (error) {
      console.error('Error saving guest compare data:', error)
    }
  }

  // Get all compare items
  getItems(): GuestCompareItem[] {
    return this.getStorageData().items
  }

  // Get compare count
  getCount(): number {
    return this.getStorageData().items.length
  }

  // Check if item is in compare
  hasItem(variantId: number): boolean {
    const data = this.getStorageData()
    return data.items.some(item => item.variantId === variantId)
  }

  // Add item to compare
  addItem(product: Product, variantId?: number): { success: boolean; error?: string; item?: GuestCompareItem } {
    const data = this.getStorageData()
    
    // Use first variant if no specific variant provided
    const variant = variantId 
      ? product.variants.find(v => v.id === variantId)
      : product.variants[0]
    
    if (!variant) {
      return { success: false, error: 'Product variant not found' }
    }

    // Check if already exists
    if (data.items.some(item => item.variantId === variant.id)) {
      return { success: false, error: 'Item already in compare list' }
    }

    // Check max items limit
    if (data.items.length >= this.maxItems) {
      return { 
        success: false, 
        error: `Maximum ${this.maxItems} items allowed for comparison` 
      }
    }

    // Create compare item
    const compareItem: GuestCompareItem = {
      variantId: variant.id,
      productId: product.id,
      productName: product.name,
      productSlug: product.slug,
      variantSku: variant.sku,
      price: variant.price?.sell_price || variant.price?.price || 0,
      comparePrice: variant.price?.compare_price,
      image: product.images?.[0]?.medium_url || product.images?.[0]?.original_url,
      addedAt: Date.now()
    }

    // Add to items
    data.items.push(compareItem)
    this.saveStorageData(data)

    return { success: true, item: compareItem }
  }

  // Remove item from compare
  removeItem(variantId: number): { success: boolean; error?: string } {
    const data = this.getStorageData()
    const initialLength = data.items.length
    
    data.items = data.items.filter(item => item.variantId !== variantId)
    
    if (data.items.length === initialLength) {
      return { success: false, error: 'Item not found in compare list' }
    }

    this.saveStorageData(data)
    return { success: true }
  }

  // Clear all items
  clearAll(): { success: boolean } {
    const data = this.getStorageData()
    data.items = []
    this.saveStorageData(data)
    return { success: true }
  }

  // Get variant IDs for easy checking
  getVariantIds(): number[] {
    return this.getStorageData().items.map(item => item.variantId)
  }

  // Get items with full product data (for display)
  getItemsWithDetails(): GuestCompareItem[] {
    return this.getStorageData().items.sort((a, b) => b.addedAt - a.addedAt)
  }

  // Export data for sync with backend
  exportData(): GuestCompareData {
    return this.getStorageData()
  }

  // Import data (useful for syncing from backend)
  importData(data: GuestCompareItem[]): { success: boolean; imported: number } {
    const currentData = this.getStorageData()
    
    // Merge with existing data, avoiding duplicates
    const existingVariantIds = new Set(currentData.items.map(item => item.variantId))
    const newItems = data.filter(item => !existingVariantIds.has(item.variantId))
    
    // Respect max items limit
    const totalItems = [...currentData.items, ...newItems].slice(0, this.maxItems)
    
    const updatedData: GuestCompareData = {
      items: totalItems,
      maxItems: this.maxItems,
      lastUpdated: Date.now()
    }
    
    this.saveStorageData(updatedData)
    
    return { 
      success: true, 
      imported: newItems.length 
    }
  }

  // Clean up old items (optional, for maintenance)
  cleanupOldItems(maxAgeMs = 7 * 24 * 60 * 60 * 1000): { success: boolean; removed: number } {
    const data = this.getStorageData()
    const cutoffTime = Date.now() - maxAgeMs
    const initialLength = data.items.length
    
    data.items = data.items.filter(item => item.addedAt > cutoffTime)
    
    if (data.items.length < initialLength) {
      this.saveStorageData(data)
    }
    
    return { 
      success: true, 
      removed: initialLength - data.items.length 
    }
  }

  // Get storage info
  getStorageInfo(): {
    isAvailable: boolean
    itemCount: number
    maxItems: number
    lastUpdated: number
    storageSize: number
  } {
    const data = this.getStorageData()
    const storageSize = this.isStorageAvailable() 
      ? (localStorage.getItem(this.storageKey)?.length || 0)
      : 0

    return {
      isAvailable: this.isStorageAvailable(),
      itemCount: data.items.length,
      maxItems: this.maxItems,
      lastUpdated: data.lastUpdated,
      storageSize
    }
  }
}

// Export singleton instance
export const guestCompareStorage = new GuestCompareStorage()

// Export class for testing or custom instances
export { GuestCompareStorage }
