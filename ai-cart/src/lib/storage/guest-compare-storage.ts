"use client"

export interface CompareData {
  variantIds: number[]
  maxItems: number
  lastUpdated: number
}

const STORAGE_KEY = 'compare_items'
const MAX_COMPARE_ITEMS = 4
const STORAGE_VERSION = '1.0'

class CompareStorage {
  private storageKey: string
  private maxItems: number

  constructor(storageKey = STORAGE_KEY, maxItems = MAX_COMPARE_ITEMS) {
    this.storageKey = storageKey
    this.maxItems = maxItems
  }

  // Check if localStorage is available
  private isStorageAvailable(): boolean {
    try {
      if (typeof window === 'undefined') return false
      const test = '__storage_test__'
      localStorage.setItem(test, test)
      localStorage.removeItem(test)
      return true
    } catch {
      return false
    }
  }

  // Get current compare data from localStorage
  private getStorageData(): CompareData {
    if (!this.isStorageAvailable()) {
      return { variantIds: [], maxItems: this.maxItems, lastUpdated: Date.now() }
    }

    try {
      const stored = localStorage.getItem(this.storageKey)
      if (!stored) {
        return { variantIds: [], maxItems: this.maxItems, lastUpdated: Date.now() }
      }

      const parsed = JSON.parse(stored)

      // Validate data structure - support migration from old formats
      let variantIds: number[] = []
      if (parsed.variantIds && Array.isArray(parsed.variantIds)) {
        variantIds = parsed.variantIds
      } else if (parsed.items && Array.isArray(parsed.items)) {
        // Migrate from old format
        variantIds = parsed.items.map((item: any) => item.variantId).filter(Boolean)
      }

      // Ensure maxItems limit and remove duplicates
      variantIds = [...new Set(variantIds)].slice(0, this.maxItems)

      return {
        variantIds,
        maxItems: this.maxItems,
        lastUpdated: parsed.lastUpdated || Date.now()
      }
    } catch (error) {
      console.error('Error reading compare data:', error)
      return { variantIds: [], maxItems: this.maxItems, lastUpdated: Date.now() }
    }
  }

  // Save compare data to localStorage
  private saveStorageData(data: CompareData): void {
    if (!this.isStorageAvailable()) return

    try {
      const dataToSave = {
        ...data,
        version: STORAGE_VERSION,
        lastUpdated: Date.now()
      }
      localStorage.setItem(this.storageKey, JSON.stringify(dataToSave))
    } catch (error) {
      console.error('Error saving guest compare data:', error)
    }
  }

  // Get all variant IDs
  getVariantIds(): number[] {
    return this.getStorageData().variantIds
  }

  // Get compare count
  getCount(): number {
    return this.getStorageData().variantIds.length
  }

  // Check if item is in compare
  hasItem(variantId: number): boolean {
    const data = this.getStorageData()
    return data.variantIds.includes(variantId)
  }

  // Add variant to compare
  addItem(variantId: number): { success: boolean; error?: string } {
    const data = this.getStorageData()

    // Check if already exists
    if (data.variantIds.includes(variantId)) {
      return { success: false, error: 'Item already in compare list' }
    }

    // Check max items limit
    if (data.variantIds.length >= this.maxItems) {
      return {
        success: false,
        error: `Maximum ${this.maxItems} items allowed for comparison`
      }
    }

    // Add variant ID
    data.variantIds.push(variantId)
    this.saveStorageData(data)

    return { success: true }
  }

  // Remove item from compare
  removeItem(variantId: number): { success: boolean; error?: string } {
    const data = this.getStorageData()
    const initialLength = data.variantIds.length

    data.variantIds = data.variantIds.filter(id => id !== variantId)

    if (data.variantIds.length === initialLength) {
      return { success: false, error: 'Item not found in compare list' }
    }

    this.saveStorageData(data)
    return { success: true }
  }

  // Clear all items
  clearAll(): { success: boolean } {
    const data = this.getStorageData()
    data.variantIds = []
    this.saveStorageData(data)
    return { success: true }
  }

  // Export data
  exportData(): CompareData {
    return this.getStorageData()
  }

  // Import data
  importData(variantIds: number[]): { success: boolean; imported: number } {
    const currentData = this.getStorageData()

    // Merge with existing data, avoiding duplicates
    const existingIds = new Set(currentData.variantIds)
    const newIds = variantIds.filter(id => !existingIds.has(id))

    // Respect max items limit
    const totalIds = [...currentData.variantIds, ...newIds].slice(0, this.maxItems)

    const updatedData: CompareData = {
      variantIds: totalIds,
      maxItems: this.maxItems,
      lastUpdated: Date.now()
    }

    this.saveStorageData(updatedData)

    return {
      success: true,
      imported: newIds.length
    }
  }

  // Clean up old items (optional, for maintenance)
  cleanupOldItems(maxAgeMs = 7 * 24 * 60 * 60 * 1000): { success: boolean; removed: number } {
    const data = this.getStorageData()
    const cutoffTime = Date.now() - maxAgeMs

    // For simplified storage, we can't track individual item ages
    // So we'll clear all items if the storage is older than maxAge
    if (data.lastUpdated < cutoffTime) {
      const removedCount = data.variantIds.length
      data.variantIds = []
      this.saveStorageData(data)
      return { success: true, removed: removedCount }
    }

    return { success: true, removed: 0 }
  }

  // Get storage info
  getStorageInfo(): {
    isAvailable: boolean
    itemCount: number
    maxItems: number
    lastUpdated: number
    storageSize: number
  } {
    const data = this.getStorageData()
    const storageSize = this.isStorageAvailable()
      ? (localStorage.getItem(this.storageKey)?.length || 0)
      : 0

    return {
      isAvailable: this.isStorageAvailable(),
      itemCount: data.variantIds.length,
      maxItems: this.maxItems,
      lastUpdated: data.lastUpdated,
      storageSize
    }
  }
}

// Export singleton instance
export const compareStorage = new CompareStorage()

// Export class for testing or custom instances
export { CompareStorage }
