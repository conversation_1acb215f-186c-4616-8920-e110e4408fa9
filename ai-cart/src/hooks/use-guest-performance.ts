"use client"

import { useEffect, useRef, useCallback } from 'react'
import { guestCompareStorage } from '@/lib/storage/guest-compare-storage'

interface PerformanceMetrics {
  storageOperationTime: number[]
  renderTime: number[]
  memoryUsage: number[]
  errorCount: number
  totalOperations: number
}

/**
 * Hook for monitoring and optimizing guest user performance
 */
export function useGuestPerformance() {
  const metricsRef = useRef<PerformanceMetrics>({
    storageOperationTime: [],
    renderTime: [],
    memoryUsage: [],
    errorCount: 0,
    totalOperations: 0
  })

  // Measure storage operation performance
  const measureStorageOperation = useCallback(async <T>(
    operationName: string,
    operation: () => T
  ): Promise<T> => {
    const startTime = performance.now()
    
    try {
      const result = operation()
      const endTime = performance.now()
      const duration = endTime - startTime
      
      // Store metrics
      metricsRef.current.storageOperationTime.push(duration)
      metricsRef.current.totalOperations++
      
      // Keep only last 50 measurements
      if (metricsRef.current.storageOperationTime.length > 50) {
        metricsRef.current.storageOperationTime.shift()
      }
      
      // Log slow operations in development
      if (process.env.NODE_ENV === 'development' && duration > 10) {
        console.warn(`Slow guest storage operation "${operationName}": ${duration.toFixed(2)}ms`)
      }
      
      return result
    } catch (error) {
      metricsRef.current.errorCount++
      
      // Log errors in development
      if (process.env.NODE_ENV === 'development') {
        console.error(`Guest storage operation "${operationName}" failed:`, error)
      }
      
      throw error
    }
  }, [])

  // Measure render performance
  const measureRender = useCallback((componentName: string) => {
    const startTime = performance.now()
    
    return () => {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      metricsRef.current.renderTime.push(duration)
      
      // Keep only last 20 measurements
      if (metricsRef.current.renderTime.length > 20) {
        metricsRef.current.renderTime.shift()
      }
      
      // Log slow renders in development
      if (process.env.NODE_ENV === 'development' && duration > 16) {
        console.warn(`Slow render "${componentName}": ${duration.toFixed(2)}ms`)
      }
    }
  }, [])

  // Monitor memory usage
  useEffect(() => {
    const monitorMemory = () => {
      if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
        const memoryUsage = (window.performance as any).memory.usedJSHeapSize / 1024 / 1024 // MB
        metricsRef.current.memoryUsage.push(memoryUsage)
        
        // Keep only last 10 measurements
        if (metricsRef.current.memoryUsage.length > 10) {
          metricsRef.current.memoryUsage.shift()
        }
      }
    }

    // Monitor every 30 seconds
    const interval = setInterval(monitorMemory, 30000)
    monitorMemory() // Initial measurement
    
    return () => clearInterval(interval)
  }, [])

  // Get performance metrics
  const getMetrics = useCallback(() => {
    const { storageOperationTime, renderTime, memoryUsage, errorCount, totalOperations } = metricsRef.current
    
    const avgStorageTime = storageOperationTime.length > 0 
      ? storageOperationTime.reduce((sum, time) => sum + time, 0) / storageOperationTime.length 
      : 0
      
    const avgRenderTime = renderTime.length > 0
      ? renderTime.reduce((sum, time) => sum + time, 0) / renderTime.length
      : 0
      
    const avgMemoryUsage = memoryUsage.length > 0
      ? memoryUsage.reduce((sum, usage) => sum + usage, 0) / memoryUsage.length
      : 0
    
    return {
      averageStorageTime: Math.round(avgStorageTime * 100) / 100,
      averageRenderTime: Math.round(avgRenderTime * 100) / 100,
      averageMemoryUsage: Math.round(avgMemoryUsage * 100) / 100,
      errorRate: totalOperations > 0 ? (errorCount / totalOperations) * 100 : 0,
      totalOperations,
      errorCount
    }
  }, [])

  // Optimize storage operations
  const optimizeStorage = useCallback(() => {
    try {
      // Clean up old items (older than 7 days)
      const result = guestCompareStorage.cleanupOldItems()
      
      if (result.removed > 0) {
        console.log(`Cleaned up ${result.removed} old compare items`)
      }
      
      return result
    } catch (error) {
      console.error('Storage optimization failed:', error)
      return { success: false, removed: 0 }
    }
  }, [])

  // Check if performance is degraded
  const isPerformanceDegraded = useCallback(() => {
    const metrics = getMetrics()
    
    return (
      metrics.averageStorageTime > 50 || // Storage operations taking > 50ms
      metrics.averageRenderTime > 100 || // Renders taking > 100ms
      metrics.errorRate > 5 || // Error rate > 5%
      metrics.averageMemoryUsage > 100 // Memory usage > 100MB
    )
  }, [getMetrics])

  return {
    measureStorageOperation,
    measureRender,
    getMetrics,
    optimizeStorage,
    isPerformanceDegraded
  }
}

/**
 * Hook for optimized guest compare operations
 */
export function useOptimizedGuestCompare() {
  const { measureStorageOperation } = useGuestPerformance()
  
  const addItem = useCallback(async (product: any, variantId?: number) => {
    return measureStorageOperation('addItem', () => 
      guestCompareStorage.addItem(product, variantId)
    )
  }, [measureStorageOperation])
  
  const removeItem = useCallback(async (variantId: number) => {
    return measureStorageOperation('removeItem', () => 
      guestCompareStorage.removeItem(variantId)
    )
  }, [measureStorageOperation])
  
  const getItems = useCallback(() => {
    return measureStorageOperation('getItems', () => 
      guestCompareStorage.getItems()
    )
  }, [measureStorageOperation])
  
  const getCount = useCallback(() => {
    return measureStorageOperation('getCount', () => 
      guestCompareStorage.getCount()
    )
  }, [measureStorageOperation])
  
  const clearAll = useCallback(() => {
    return measureStorageOperation('clearAll', () => 
      guestCompareStorage.clearAll()
    )
  }, [measureStorageOperation])
  
  const hasItem = useCallback((variantId: number) => {
    return measureStorageOperation('hasItem', () => 
      guestCompareStorage.hasItem(variantId)
    )
  }, [measureStorageOperation])
  
  return {
    addItem,
    removeItem,
    getItems,
    getCount,
    clearAll,
    hasItem
  }
}

/**
 * Performance monitoring component for development
 */
export function GuestPerformanceMonitor() {
  const { getMetrics, isPerformanceDegraded, optimizeStorage } = useGuestPerformance()
  
  useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return
    
    const logMetrics = () => {
      const metrics = getMetrics()
      const isDegraded = isPerformanceDegraded()
      
      console.group('Guest Performance Metrics')
      console.log('Average Storage Time:', `${metrics.averageStorageTime}ms`)
      console.log('Average Render Time:', `${metrics.averageRenderTime}ms`)
      console.log('Average Memory Usage:', `${metrics.averageMemoryUsage}MB`)
      console.log('Error Rate:', `${metrics.errorRate.toFixed(2)}%`)
      console.log('Total Operations:', metrics.totalOperations)
      console.log('Performance Degraded:', isDegraded)
      console.groupEnd()
      
      if (isDegraded) {
        console.warn('Guest performance is degraded, running optimization...')
        optimizeStorage()
      }
    }
    
    // Log metrics every 60 seconds
    const interval = setInterval(logMetrics, 60000)
    
    return () => clearInterval(interval)
  }, [getMetrics, isPerformanceDegraded, optimizeStorage])
  
  return null // This is a monitoring component
}
