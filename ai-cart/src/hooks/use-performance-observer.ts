'use client'

import { useEffect } from 'react'

interface PerformanceObserverOptions {
  onLCP?: (value: number) => void
  onFID?: (value: number) => void
  onCLS?: (value: number) => void
  onFCP?: (value: number) => void
  onTTFB?: (value: number) => void
}

export function usePerformanceObserver(options: PerformanceObserverOptions = {}) {
  useEffect(() => {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return
    }

    // Largest Contentful Paint (LCP)
    if (options.onLCP) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as PerformanceEntry & { renderTime?: number; loadTime?: number }
        const value = lastEntry.renderTime || lastEntry.loadTime || 0
        options.onLCP?.(value)
      })

      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
      } catch (e) {
        // Ignore if not supported
      }
    }

    // First Input Delay (FID)
    if (options.onFID) {
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          const fidEntry = entry as PerformanceEntry & { processingStart?: number; startTime?: number }
          const value = (fidEntry.processingStart || 0) - (fidEntry.startTime || 0)
          options.onFID?.(value)
        })
      })

      try {
        fidObserver.observe({ entryTypes: ['first-input'] })
      } catch (e) {
        // Ignore if not supported
      }
    }

    // Cumulative Layout Shift (CLS)
    if (options.onCLS) {
      let clsValue = 0
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          const layoutShiftEntry = entry as PerformanceEntry & { value?: number; hadRecentInput?: boolean }
          if (!layoutShiftEntry.hadRecentInput) {
            clsValue += layoutShiftEntry.value || 0
          }
        })
        options.onCLS?.(clsValue)
      })

      try {
        clsObserver.observe({ entryTypes: ['layout-shift'] })
      } catch (e) {
        // Ignore if not supported
      }
    }

    // First Contentful Paint (FCP)
    if (options.onFCP) {
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          if (entry.name === 'first-contentful-paint') {
            options.onFCP?.(entry.startTime)
          }
        })
      })

      try {
        fcpObserver.observe({ entryTypes: ['paint'] })
      } catch (e) {
        // Ignore if not supported
      }
    }

    // Time to First Byte (TTFB)
    if (options.onTTFB) {
      const navigationEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[]
      if (navigationEntries.length > 0) {
        const ttfb = navigationEntries[0].responseStart - navigationEntries[0].requestStart
        options.onTTFB?.(ttfb)
      }
    }

  }, [options])
}

// Hook for monitoring infinite scroll performance
export function useInfiniteScrollPerformance() {
  usePerformanceObserver({
    onLCP: (value) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('LCP (Largest Contentful Paint):', value, 'ms')
        if (value > 2500) {
          console.warn('LCP is slower than recommended (2.5s)')
        }
      }
    },
    onCLS: (value) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('CLS (Cumulative Layout Shift):', value)
        if (value > 0.1) {
          console.warn('CLS is higher than recommended (0.1)')
        }
      }
    },
    onFCP: (value) => {
      if (process.env.NODE_ENV === 'development') {
        console.log('FCP (First Contentful Paint):', value, 'ms')
        if (value > 1800) {
          console.warn('FCP is slower than recommended (1.8s)')
        }
      }
    }
  })
}
