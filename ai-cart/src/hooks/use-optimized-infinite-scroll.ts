"use client"

import { useState, useEffect, useCallback, useRef } from 'react'
import { useInView } from 'react-intersection-observer'

interface UseOptimizedInfiniteScrollOptions {
  initialData: any[]
  fetchMore: (page: number) => Promise<{ data: any[]; hasMore: boolean; totalCount?: number }>
  pageSize?: number
  threshold?: number
  rootMargin?: string
  enabled?: boolean
  debounceMs?: number
}

interface UseOptimizedInfiniteScrollReturn {
  data: any[]
  loading: boolean
  error: string | null
  hasMore: boolean
  loadMore: () => void
  refresh: () => void
  ref: (node?: Element | null) => void
  totalCount: number
}

export function useOptimizedInfiniteScroll({
  initialData,
  fetchMore,
  pageSize = 20,
  threshold = 0.1,
  rootMargin = '100px',
  enabled = true,
  debounceMs = 300
}: UseOptimizedInfiniteScrollOptions): UseOptimizedInfiniteScrollReturn {
  const [data, setData] = useState(initialData)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [totalCount, setTotalCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  
  // Refs for performance optimization
  const loadingRef = useRef(false)
  const debounceRef = useRef<NodeJS.Timeout>()
  const abortControllerRef = useRef<AbortController>()

  // Intersection observer for infinite scroll
  const { ref, inView } = useInView({
    threshold,
    rootMargin,
    triggerOnce: false,
  })

  // Debounced load more function
  const debouncedLoadMore = useCallback(() => {
    if (debounceRef.current) {
      clearTimeout(debounceRef.current)
    }
    
    debounceRef.current = setTimeout(() => {
      if (!loadingRef.current && hasMore && enabled) {
        loadMore()
      }
    }, debounceMs)
  }, [hasMore, enabled, debounceMs])

  // Load more data
  const loadMore = useCallback(async () => {
    if (loadingRef.current || !hasMore || !enabled) return

    // Cancel previous request if still pending
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    loadingRef.current = true
    setLoading(true)
    setError(null)

    // Create new abort controller for this request
    abortControllerRef.current = new AbortController()

    try {
      const result = await fetchMore(currentPage)
      
      // Check if request was aborted
      if (abortControllerRef.current.signal.aborted) {
        return
      }

      setData(prevData => {
        // Prevent duplicate items
        const existingIds = new Set(prevData.map(item => item.id))
        const newItems = result.data.filter(item => !existingIds.has(item.id))
        return [...prevData, ...newItems]
      })
      
      setHasMore(result.hasMore)
      setCurrentPage(prev => prev + 1)
      
      if (result.totalCount !== undefined) {
        setTotalCount(result.totalCount)
      }
    } catch (err) {
      if (err instanceof Error && err.name !== 'AbortError') {
        setError(err.message || 'Failed to load more data')
        console.error('Infinite scroll error:', err)
      }
    } finally {
      loadingRef.current = false
      setLoading(false)
    }
  }, [currentPage, hasMore, enabled, fetchMore])

  // Refresh data
  const refresh = useCallback(async () => {
    // Cancel any pending requests
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    setData(initialData)
    setCurrentPage(1)
    setHasMore(true)
    setError(null)
    setTotalCount(0)
    loadingRef.current = false
  }, [initialData])

  // Effect for intersection observer
  useEffect(() => {
    if (inView && enabled) {
      debouncedLoadMore()
    }
  }, [inView, enabled, debouncedLoadMore])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current)
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  // Update data when initialData changes
  useEffect(() => {
    setData(initialData)
    setCurrentPage(1)
    setHasMore(initialData.length >= pageSize)
    setError(null)
    loadingRef.current = false
  }, [initialData, pageSize])

  return {
    data,
    loading,
    error,
    hasMore,
    loadMore,
    refresh,
    ref,
    totalCount
  }
}

// Performance monitoring hook
export function useInfiniteScrollPerformance() {
  const metricsRef = useRef({
    loadTimes: [] as number[],
    errorCount: 0,
    totalRequests: 0
  })

  const recordLoadTime = useCallback((startTime: number) => {
    const loadTime = performance.now() - startTime
    metricsRef.current.loadTimes.push(loadTime)
    metricsRef.current.totalRequests++
    
    // Keep only last 10 measurements
    if (metricsRef.current.loadTimes.length > 10) {
      metricsRef.current.loadTimes.shift()
    }
  }, [])

  const recordError = useCallback(() => {
    metricsRef.current.errorCount++
  }, [])

  const getMetrics = useCallback(() => {
    const { loadTimes, errorCount, totalRequests } = metricsRef.current
    const avgLoadTime = loadTimes.length > 0 
      ? loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length 
      : 0
    
    return {
      averageLoadTime: Math.round(avgLoadTime),
      errorRate: totalRequests > 0 ? (errorCount / totalRequests) * 100 : 0,
      totalRequests,
      errorCount
    }
  }, [])

  return {
    recordLoadTime,
    recordError,
    getMetrics
  }
}
