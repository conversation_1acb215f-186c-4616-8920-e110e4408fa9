import { Suspense } from 'react'
import { Metada<PERSON> } from 'next'
import { notFound } from 'next/navigation'
import { auth } from '~/auth'
import { getCompareList } from '@/lib/actions/compare'
import { ComparePageContent } from '@/components/compare/compare-page-content'
import { ComparePageSkeleton } from '@/components/compare/compare-page-skeleton'

// Generate dynamic metadata for SEO
export async function generateMetadata(): Promise<Metadata> {
  const session = await auth()

  let itemCount = 0
  try {
    if (session?.user) {
      const compareData = await getCompareList(0, 4)
      itemCount = compareData.items.length
    }
  } catch (error) {
    console.error('Error getting compare count for metadata:', error)
  }

  const title = itemCount > 0
    ? `Compare Products (${itemCount} items)`
    : 'Compare Products'

  const description = itemCount > 0
    ? `Compare ${itemCount} products side by side. Find the perfect product by comparing features, prices, and specifications.`
    : 'Compare products side by side to find the perfect match for your needs.'

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    },
    robots: {
      index: true,
      follow: true,
    },
  }
}

// Enhanced server component for better SEO and performance
export default async function ComparePage() {
  const session = await auth()

  try {
    let compareItems = []
    let isGuest = false

    if (session?.user) {
      // Authenticated user - load from backend
      console.log('Loading compare data for authenticated user')
      const compareData = await getCompareList(0, 4) // Max 4 items
      compareItems = compareData.items
    } else {
      // Guest user - data will be loaded on client
      isGuest = true
      console.log('Guest user - compare data will be loaded on client')
    }

    // Generate structured data for SEO
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "ItemList",
      "name": "Product Comparison",
      "description": "Compare products side by side",
      "numberOfItems": compareItems.length,
      "itemListElement": compareItems.map((item, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "Product",
          "@id": `${process.env.NEXT_PUBLIC_APP_URL}/products/view/${item.product.slug}`,
          "name": item.product.name,
          "description": item.product.description,
          "url": `${process.env.NEXT_PUBLIC_APP_URL}/products/view/${item.product.slug}`,
          "image": item.product.images?.[0]?.original_url,
          "sku": item.variant?.sku,
          "offers": {
            "@type": "Offer",
            "price": item.variant?.price?.sell_price || item.variant?.price?.price,
            "priceCurrency": "USD",
            "availability": item.variant?.stock > 0 ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
            "url": `${process.env.NEXT_PUBLIC_APP_URL}/products/view/${item.product.slug}`
          }
        }
      }))
    }

    return (
      <>
        {/* Structured Data for SEO */}
        {compareItems.length > 0 && (
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
          />
        )}

        {/* SEO Meta Tags */}
        <div className="sr-only">
          <h1>Compare Products</h1>
          <p>Compare products side by side to find the perfect match for your needs.</p>
          {compareItems.length > 0 && (
            <p>Currently comparing {compareItems.length} products.</p>
          )}
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-6">
          <Suspense fallback={<ComparePageSkeleton />}>
            <ComparePageContent
              initialItems={compareItems}
              isGuest={isGuest}
            />
          </Suspense>
        </div>
      </>
    )
  } catch (error) {
    console.error('Error loading compare page:', error)
    notFound()
  }
}
