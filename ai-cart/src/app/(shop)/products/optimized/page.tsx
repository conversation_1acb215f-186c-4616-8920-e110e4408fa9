import { Suspense } from 'react'
import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { SEOOptimizedProductGrid, generateProductGridMetadata } from '@/components/product/seo-optimized-product-grid'
import { ProductGridSkeleton } from '@/components/product/product-grid-skeleton'
import { fetchProducts } from '@/lib/actions/product'
// import { DynamicFilterProvider } from '@/components/product/dynamic-filter-context'
// import { ProductFilters } from '@/components/product/product-filters'
// import { ProductSort } from '@/components/product/product-sort'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'

interface ProductsPageProps {
  searchParams: {
    page?: string
    category?: string
    subcategory?: string
    brand?: string
    min_price?: string
    max_price?: string
    sort?: string
    search?: string
  }
}

// Generate metadata for SEO
export async function generateMetadata({ searchParams }: ProductsPageProps): Promise<Metadata> {
  const page = parseInt(searchParams.page || '1')
  const category = searchParams.category
  const search = searchParams.search
  
  let title = 'Products'
  let description = 'Browse our collection of high-quality products.'
  
  if (search) {
    title = `Search results for "${search}"`
    description = `Find products matching "${search}". Browse our collection and find exactly what you're looking for.`
  } else if (category) {
    title = `${category.charAt(0).toUpperCase() + category.slice(1)} Products`
    description = `Browse our ${category} collection. Find the perfect ${category} products for your needs.`
  }
  
  // Fetch total count for metadata
  try {
    const { totalCount } = await fetchProducts({
      page: 0,
      size: 1,
      category: searchParams.category,
      subcategory: searchParams.subcategory,
      brand: searchParams.brand,
      minPrice: searchParams.min_price ? parseFloat(searchParams.min_price) : undefined,
      maxPrice: searchParams.max_price ? parseFloat(searchParams.max_price) : undefined,
      search: searchParams.search,
      sort: searchParams.sort
    })
    
    return generateProductGridMetadata({
      title,
      description,
      category,
      totalCount,
      currentPage: page
    })
  } catch (error) {
    console.error('Error generating metadata:', error)
    return generateProductGridMetadata({ title, description, category, currentPage: page })
  }
}

export default async function OptimizedProductsPage({ searchParams }: ProductsPageProps) {
  const page = parseInt(searchParams.page || '1')
  const pageSize = 20
  
  try {
    // Fetch initial products on server for better SEO and performance
    const { products, totalCount, hasMore } = await fetchProducts({
      page: page - 1, // Convert to 0-based
      size: pageSize,
      category: searchParams.category,
      subcategory: searchParams.subcategory,
      brand: searchParams.brand,
      minPrice: searchParams.min_price ? parseFloat(searchParams.min_price) : undefined,
      maxPrice: searchParams.max_price ? parseFloat(searchParams.max_price) : undefined,
      search: searchParams.search,
      sort: searchParams.sort
    })

    // Generate breadcrumbs
    const breadcrumbs = [
      { label: 'Home', href: '/' },
      { label: 'Products', href: '/products' }
    ]
    
    if (searchParams.category) {
      breadcrumbs.push({
        label: searchParams.category.charAt(0).toUpperCase() + searchParams.category.slice(1),
        href: `/products?category=${searchParams.category}`
      })
    }
    
    if (searchParams.subcategory) {
      breadcrumbs.push({
        label: searchParams.subcategory.charAt(0).toUpperCase() + searchParams.subcategory.slice(1),
        href: `/products?category=${searchParams.category}&subcategory=${searchParams.subcategory}`
      })
    }

    const pageTitle = searchParams.search 
      ? `Search results for "${searchParams.search}"`
      : searchParams.category
        ? `${searchParams.category.charAt(0).toUpperCase() + searchParams.category.slice(1)} Products`
        : 'All Products'

    const pageDescription = searchParams.search
      ? `Found ${totalCount} products matching "${searchParams.search}"`
      : `Browse our collection of ${totalCount} ${searchParams.category || 'products'}`

    return (
      <div className="container mx-auto px-4 py-6">
          {/* Breadcrumbs for SEO and UX */}
          <Breadcrumb className="mb-6">
            <BreadcrumbList>
              {breadcrumbs.map((crumb, index) => (
                <BreadcrumbItem key={crumb.href}>
                  {index === breadcrumbs.length - 1 ? (
                    <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
                  ) : (
                    <>
                      <BreadcrumbLink href={crumb.href}>{crumb.label}</BreadcrumbLink>
                      <BreadcrumbSeparator />
                    </>
                  )}
                </BreadcrumbItem>
              ))}
            </BreadcrumbList>
          </Breadcrumb>

          {/* Page Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {pageTitle}
            </h1>
            <p className="text-gray-600">
              {pageDescription}
            </p>
          </div>

          {/* Main Content */}
          <main className="w-full">
            {/* Sort and Results Info */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 gap-4">
              <div className="text-sm text-gray-600">
                Showing {((page - 1) * pageSize) + 1}-{Math.min(page * pageSize, totalCount)} of {totalCount} products
              </div>
              {/* <ProductSort /> */}
            </div>

              {/* Products Grid */}
              <Suspense fallback={<ProductGridSkeleton count={pageSize} />}>
                <SEOOptimizedProductGrid
                  products={products}
                  title={pageTitle}
                  description={pageDescription}
                  category={searchParams.category}
                  totalCount={totalCount}
                  currentPage={page}
                  pageSize={pageSize}
                />
              </Suspense>

              {/* Pagination for SEO */}
              {totalCount > pageSize && (
                <div className="mt-12 flex justify-center">
                  <nav aria-label="Product pagination" className="flex items-center gap-2">
                    {page > 1 && (
                      <a
                        href={`/products/optimized?${new URLSearchParams({
                          ...searchParams,
                          page: (page - 1).toString()
                        })}`}
                        className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        rel="prev"
                      >
                        Previous
                      </a>
                    )}
                    
                    <span className="px-3 py-2 text-sm font-medium text-gray-900 bg-blue-50 border border-blue-300 rounded-md">
                      Page {page} of {Math.ceil(totalCount / pageSize)}
                    </span>
                    
                    {hasMore && (
                      <a
                        href={`/products/optimized?${new URLSearchParams({
                          ...searchParams,
                          page: (page + 1).toString()
                        })}`}
                        className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        rel="next"
                      >
                        Next
                      </a>
                    )}
                  </nav>
                </div>
              )}
            </main>
          </div>
    )
  } catch (error) {
    console.error('Error loading products page:', error)
    notFound()
  }
}
