import { NextResponse } from 'next/server'
import { fetchProducts } from '@/lib/actions/product'
import { getHostShop } from '@/lib/db/store/shop-query'

export async function GET() {
  try {
    const shop = await getHostShop()
    if (!shop?.id) {
      return new NextResponse('Shop not found', { status: 404 })
    }

    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://example.com'
    
    // Fetch all products for sitemap
    const { products } = await fetchProducts({
      page: 0,
      size: 10000, // Large number to get all products
    })

    // Static pages
    const staticPages = [
      '',
      '/products',
      '/compare',
      '/about',
      '/contact',
      '/privacy',
      '/terms'
    ]

    // Generate sitemap XML
    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">
  ${staticPages.map(page => `
  <url>
    <loc>${baseUrl}${page}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>${page === '' ? 'daily' : page === '/products' ? 'hourly' : 'weekly'}</changefreq>
    <priority>${page === '' ? '1.0' : page === '/products' ? '0.9' : '0.7'}</priority>
  </url>`).join('')}
  ${products.map(product => `
  <url>
    <loc>${baseUrl}/products/view/${product.slug}</loc>
    <lastmod>${product.updatedAt || new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
    ${product.images?.map(image => `
    <image:image>
      <image:loc>${image.original_url}</image:loc>
      <image:title>${product.name}</image:title>
      <image:caption>${product.description || product.name}</image:caption>
    </image:image>`).join('') || ''}
  </url>`).join('')}
</urlset>`

    return new NextResponse(sitemap, {
      headers: {
        'Content-Type': 'application/xml',
        'Cache-Control': 'public, max-age=3600, s-maxage=3600' // Cache for 1 hour
      }
    })
  } catch (error) {
    console.error('Error generating sitemap:', error)
    return new NextResponse('Error generating sitemap', { status: 500 })
  }
}
