import { NextResponse } from 'next/server'

export async function GET() {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://example.com'
  
  const robotsTxt = `User-agent: *
Allow: /

# Allow all crawlers to access product pages
Allow: /products/
Allow: /products/view/
Allow: /compare

# Disallow admin and private areas
Disallow: /admin/
Disallow: /dashboard/
Disallow: /api/
Disallow: /auth/
Disallow: /_next/
Disallow: /checkout/
Disallow: /account/

# Disallow search result pages with parameters to avoid duplicate content
Disallow: /products?*
Disallow: /products/optimized?*

# Allow specific important search pages
Allow: /products?category=*
Allow: /products/optimized?category=*

# Crawl delay for respectful crawling
Crawl-delay: 1

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml

# Additional sitemaps if needed
# Sitemap: ${baseUrl}/products-sitemap.xml
# Sitemap: ${baseUrl}/categories-sitemap.xml`

  return new NextResponse(robotsTxt, {
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400' // Cache for 24 hours
    }
  })
}
