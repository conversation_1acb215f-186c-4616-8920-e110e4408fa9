import { Metadata } from "next";
import { auth } from "~/auth";
import { redirect } from "next/navigation";
import { getWishlist } from "@/lib/actions/wishlist";
import WishlistGrid from "@/components/account/wishlist-grid";
import { Suspense } from "react";
import { LoadingSkeleton } from "@/components/ui/loading-skeleton";

export const metadata: Metadata = {
  title: "My Wishlist",
  description: "View and manage your saved products",
};

export default async function WishlistPage() {
  const session = await auth();

  if (!session?.user?.id) {
    redirect("/signin?callbackUrl=/account/wishlist");
  }

  const wishlistData = await getWishlist(0, 20);

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">My Wishlist</h1>
        <p className="text-muted-foreground">
          {wishlistData.totalCount > 0 
            ? `${wishlistData.totalCount} saved ${wishlistData.totalCount === 1 ? 'item' : 'items'}`
            : 'No saved items yet'
          }
        </p>
      </div>

      <Suspense fallback={<LoadingSkeleton.ProductGridSkeleton />}>
        <WishlistGrid initialData={wishlistData} />
      </Suspense>
    </div>
  );
}
