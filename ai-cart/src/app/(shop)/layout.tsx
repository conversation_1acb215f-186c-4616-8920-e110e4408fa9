import { ShopDataProvider } from "@/components/layouts/shop-data-provider";
import { SiteFooter } from "@/components/layouts/site-footer";
import { SiteHeader } from "@/components/layouts/site-header";
import { WishlistProviderWrapper } from "@/components/wishlist/wishlist-provider-wrapper";
import { CompareProvider } from "@/components/compare/simple-compare-context";
import { CompareDebug } from "@/components/compare/compare-debug";
import { getHostShop } from "@/lib/db/store/shop-query";
import { notFound } from "next/navigation"

export async function generateMetadata() {
  const shop = await getHostShop();

  if (!shop?.id) return {};

  return {
    title: shop.name,
    description: shop.description || `${shop.name} online store`,
    icons: {
      icon: shop?.favicon_url || '/favicon.ico',
    },
  }
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

  const shop = await getHostShop()
  if(!shop?.id) {
    notFound()
  }

  return (
    <ShopDataProvider shop={shop}>
      <WishlistProviderWrapper>
        <CompareProvider>
          <SiteHeader />
            {children}
          <SiteFooter />
          <CompareDebug />
        </CompareProvider>
      </WishlistProviderWrapper>
    </ShopDataProvider>
  )
}
