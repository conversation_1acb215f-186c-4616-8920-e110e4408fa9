"use client"

import { useState } from "react";
import { WishlistResponse, WishlistItem, getWishlist } from "@/lib/actions/wishlist";
import { useWishlistContext } from "@/components/wishlist/wishlist-context";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

import { Heart, ShoppingCart, Trash2, RefreshCw } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { toast } from "sonner";
import { addToCart } from "@/lib/actions/cart";
import { showErrorToast } from "@/lib/handle-error";

interface WishlistGridProps {
  initialData: WishlistResponse;
}

export default function WishlistGrid({ initialData }: WishlistGridProps) {
  const { removeFromWishlistAction } = useWishlistContext();
  const [wishlistData, setWishlistData] = useState(initialData);
  const [loading, setLoading] = useState(false);
  const [removingItems, setRemovingItems] = useState<Set<number>>(new Set());
  const [addingToCart, setAddingToCart] = useState<Set<number>>(new Set());

  const handleRemoveFromWishlist = async (variantId: number) => {
    setRemovingItems(prev => new Set(prev).add(variantId));

    try {
      const result = await removeFromWishlistAction(variantId);
      if (!result.success) {
        toast.error(result.error || "Failed to remove item");
        return;
      }

      // Remove item from local state
      setWishlistData(prev => ({
        ...prev,
        items: prev.items.filter(item => item.variant.id !== variantId),
        totalCount: prev.totalCount - 1
      }));

      toast.success("Removed from wishlist");
    } catch (error) {
      console.error("Failed to remove item:", error);
      toast.error("Failed to remove item");
    } finally {
      setRemovingItems(prev => {
        const next = new Set(prev);
        next.delete(variantId);
        return next;
      });
    }
  };

  const handleAddToCart = async (item: WishlistItem) => {
    setAddingToCart(prev => new Set(prev).add(item.variant.id));

    try {
      const { error } = await addToCart(item.variant.id, null, 1); // variantId, productId, quantity
      if (error) {
        showErrorToast(error);
        return;
      }

      toast.success("Added to cart");
    } catch (error) {
      toast.error("Failed to add to cart");
    } finally {
      setAddingToCart(prev => {
        const next = new Set(prev);
        next.delete(item.variant.id);
        return next;
      });
    }
  };

  const loadMore = async () => {
    if (loading || !wishlistData.hasMore) return;

    setLoading(true);
    try {
      const nextPage = Math.floor(wishlistData.items.length / wishlistData.size);
      const newData = await getWishlist(nextPage, wishlistData.size);
      
      setWishlistData(prev => ({
        ...newData,
        items: [...prev.items, ...newData.items]
      }));
    } catch (error) {
      toast.error("Failed to load more items");
    } finally {
      setLoading(false);
    }
  };

  if (wishlistData.items.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto max-w-md">
          <div className="mb-4">
            <Heart className="mx-auto h-16 w-16 text-muted-foreground/50" />
          </div>
          <h3 className="text-lg font-medium text-foreground mb-2">Your wishlist is empty</h3>
          <p className="text-muted-foreground mb-4">
            Start adding products you love to your wishlist
          </p>
          <Button asChild>
            <Link href="/products">Browse Products</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Wishlist Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {wishlistData.items.map((item) => (
          <Card key={item.id} className="group overflow-hidden">
            <CardContent className="p-0">
              {/* Product Image */}
              <div className="relative aspect-square overflow-hidden">
                {item.product.images && item.product.images.length > 0 ? (
                  <Image
                    src={item.product.images[0].original_url || item.product.images[0].medium_url}
                    alt={item.product.name}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                ) : (
                  <div className="flex h-full items-center justify-center bg-muted">
                    <span className="text-muted-foreground">No image</span>
                  </div>
                )}
                
                {/* Action Buttons */}
                <div className="absolute top-2 right-2 flex flex-col gap-2 opacity-0 transition-opacity group-hover:opacity-100">
                  <Button
                    size="icon"
                    variant="secondary"
                    onClick={() => handleRemoveFromWishlist(item.variant.id)}
                    disabled={removingItems.has(item.variant.id)}
                    className="h-8 w-8"
                  >
                    {removingItems.has(item.variant.id) ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>

              {/* Product Info */}
              <div className="p-4 space-y-3">
                <div>
                  <h3 className="font-medium line-clamp-2">
                    <Link 
                      href={`/products/view/${item.product.slug}`}
                      className="hover:underline"
                    >
                      {item.product.name}
                    </Link>
                  </h3>
                  <p className="text-lg font-bold mt-1">
                    ${item.product.variants && item.product.variants.length > 0
                      ? (item.product.variants[0].price?.sell_price || item.product.variants[0].price?.price || 0).toFixed(2)
                      : '0.00'}
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <Button
                    onClick={() => handleAddToCart(item)}
                    disabled={addingToCart.has(item.variant.id)}
                    className="flex-1"
                    size="sm"
                  >
                    {addingToCart.has(item.variant.id) ? (
                      <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <ShoppingCart className="h-4 w-4 mr-2" />
                    )}
                    Add to Cart
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleRemoveFromWishlist(item.variant.id)}
                    disabled={removingItems.has(item.variant.id)}
                  >
                    <Heart className="h-4 w-4 fill-current" />
                  </Button>
                </div>

                {/* Added Date */}
                <p className="text-xs text-muted-foreground">
                  Added {new Date(item.createdAt).toLocaleDateString()}
                </p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More Button */}
      {wishlistData.hasMore && (
        <div className="text-center">
          <Button
            onClick={loadMore}
            disabled={loading}
            variant="outline"
            className="min-w-32"
          >
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                Loading...
              </>
            ) : (
              'Load More'
            )}
          </Button>
        </div>
      )}
    </div>
  );
}
