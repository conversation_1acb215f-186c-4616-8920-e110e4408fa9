"use client"

import { useEffect, useState, useCallback } from 'react';
import { useInView } from 'react-intersection-observer';
import { Button } from '@/components/ui/button';
import { RefreshCw, ChevronUp } from 'lucide-react';
import { fetchProducts, ProductUrlParams } from '@/lib/actions/product';
import { Product } from '@/types/product';

interface SimpleProductLoaderProps {
  initialProducts: Product[];
  searchParams: ProductUrlParams;
  onProductsUpdate: (products: Product[], hasMore: boolean) => void;
}

export default function SimpleProductLoader({ 
  initialProducts, 
  searchParams, 
  onProductsUpdate 
}: SimpleProductLoaderProps) {
  const [products, setProducts] = useState<Product[]>(initialProducts);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [error, setError] = useState<string | null>(null);
  const [showScrollTop, setShowScrollTop] = useState(false);

  const { ref, inView } = useInView({
    threshold: 0.1,
    rootMargin: '100px'
  });

  // Reset when search params change
  useEffect(() => {
    setProducts(initialProducts);
    setPage(1);
    setHasMore(initialProducts.length === 12); // Assuming 12 per page
    setError(null);
  }, [initialProducts, searchParams]);

  const loadMoreProducts = useCallback(async () => {
    if (loading || !hasMore) return;

    setLoading(true);
    setError(null);

    try {
      const result = await fetchProducts({
        ...searchParams,
        page: page.toString()
      });

      if (result.length === 0) {
        setHasMore(false);
      } else {
        const newProducts = [...products, ...result];
        setProducts(newProducts);
        setPage(prev => prev + 1);
        setHasMore(result.length === 12);
        onProductsUpdate(newProducts, result.length === 12);
      }
    } catch (err) {
      setError('Failed to load more products');
      console.error('Error loading products:', err);
    } finally {
      setLoading(false);
    }
  }, [loading, hasMore, page, products, searchParams, onProductsUpdate]);

  useEffect(() => {
    if (inView && hasMore && !loading) {
      loadMoreProducts();
    }
  }, [inView, hasMore, loading, loadMoreProducts]);

  // Show scroll to top button when user scrolls down
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 1000);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleRetry = () => {
    setError(null);
    loadMoreProducts();
  };

  return (
    <>
      <div
        ref={ref}
        className="flex flex-col justify-center items-center py-8 min-h-[120px]"
      >
        {loading && (
          <div className="flex flex-col items-center gap-3">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
            <p className="text-sm text-muted-foreground">Loading more products...</p>
          </div>
        )}

        {error && (
          <div className="flex flex-col items-center gap-3 text-center">
            <div className="text-destructive">
              <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div>
              <h3 className="font-medium text-foreground mb-1">Failed to load products</h3>
              <p className="text-sm text-muted-foreground mb-3">{error}</p>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRetry}
                disabled={loading}
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Try again
              </Button>
            </div>
          </div>
        )}

        {!loading && !error && !hasMore && products.length > 0 && (
          <div className="flex flex-col items-center gap-3 text-center">
            <div className="text-muted-foreground">
              <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="font-medium text-foreground mb-1">You&apos;ve reached the end!</h3>
              <p className="text-sm text-muted-foreground">
                You&apos;ve seen all {products.length} products matching your criteria.
              </p>
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={scrollToTop}
              className="mt-2"
            >
              <ChevronUp className="w-4 h-4 mr-2" />
              Back to top
            </Button>
          </div>
        )}

        {!loading && !error && !hasMore && products.length === 0 && (
          <div className="flex flex-col items-center gap-3 text-center py-12">
            <div className="text-muted-foreground">
              <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-medium text-foreground mb-2">No products found</h3>
              <p className="text-muted-foreground max-w-md">
                We couldn&apos;t find any products matching your search criteria. Try adjusting your filters or search terms.
              </p>
            </div>
          </div>
        )}

        {!loading && !error && hasMore && (
          <div className="flex flex-col items-center gap-3">
            <div className="w-full h-px bg-border"></div>
            <p className="text-xs text-muted-foreground">Scroll down to load more products</p>
          </div>
        )}
      </div>

      {/* Scroll to top button */}
      {showScrollTop && (
        <Button
          onClick={scrollToTop}
          size="icon"
          className="fixed bottom-6 right-6 z-50 rounded-full shadow-lg"
          aria-label="Scroll to top"
        >
          <ChevronUp className="w-4 h-4" />
        </Button>
      )}
    </>
  );
}
