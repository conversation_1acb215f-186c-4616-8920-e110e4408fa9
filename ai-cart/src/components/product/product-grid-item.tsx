"use client"

import Image from "next/image"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { Heart, ShoppingCart, Search, RefreshCw, Star, Clock, RotateCcw } from 'lucide-react'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Portal, Arrow } from "@radix-ui/react-tooltip"
import React from 'react';
import { cn } from "@/lib/utils"
import { toast } from "sonner"

import { Product, Variant } from "@/types/product"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { addToCart } from "@/lib/actions/cart"
import { OptimizedCompareToggle } from "@/components/compare/optimized-compare-toggle"
import { showErrorToast } from "@/lib/handle-error"


const ActionButton: React.FC<{ icon: React.ReactNode; label: string, className?: string, onClick?: () => void, disabled?: boolean }> = ({ icon, label, className = "",  onClick, disabled = false}) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger asChild>
        <button
        onClick={onClick}
        disabled={disabled}
        className={cn(
          "bg-white rounded-full p-1.5 sm:p-2 shadow hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",
          className

        )}
        >
          {icon}
        </button>
      </TooltipTrigger>
      <Portal>
        <TooltipContent className="bg-gray-800 text-white px-2 py-1 rounded text-xs sm:text-sm" sideOffset={5}>
          {label}
          <Arrow className="fill-gray-800" />
        </TooltipContent>
      </Portal>
    </Tooltip>
  </TooltipProvider>
)


interface ProductGridProps {
  product: Product
  isFavorite?: boolean
  onFavoriteClick?: (id: number) => Promise<void>
  wishlistLoading?: boolean
  isInCompare?: boolean
  showCompareButton?: boolean
  priority?: boolean // For image loading priority
}

export default function ProductGridItem({
  product,
  isFavorite = false,
  onFavoriteClick,
  wishlistLoading = false,
  isInCompare = false,
  showCompareButton = true,
  priority = false
}: ProductGridProps) {

  const {push} = useRouter()

  const quickViewHandler = () => {
    push(`/products/view/${product.slug}`)
  };

  const [variant, setVariant] = React.useState(product.variants[0]);
  const [isAddingToCart, setIsAddingToCart] = React.useState(false);

  const onClickHandle = (variantItem: Variant) => {
    setVariant(variantItem);
  };

  const onAddToCartHandler = async () => {
    if(isAddingToCart) return;
    setIsAddingToCart(true);
    const {error} = await addToCart(product.id, variant.id, 1);
    setIsAddingToCart(false);
    if (error) {
      showErrorToast(error)
      return;
    }
    toast.success("Product added to cart")
  }



  const _variantsWithImage = product.variants?.filter(v => v.image?.id)

  return (
    <div key={product.id} className="group overflow-hidden relative bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow">
      {product?.sale && (
        <div className="absolute top-0 right-0 z-10">
          <div className="bg-orange-400 text-white px-6 py-1 rotate-45 transform translate-x-5 -translate-y-2">
            Sale
          </div>
        </div>
      )}

      {/* Image */}
      <div className="relative overflow-hidden rounded-t-lg">

        <AspectRatio ratio={3/4}>
          <div className="absolute inset-0 w-full h-full">
            {product.images?.length > 0 &&  (<Image
            src={variant.image?.original_url || product.images[0].original_url}
            fill
            alt={product.name || "Product image"}
            className="object-cover transition-transform duration-300 ease-out group-hover:scale-110"
            priority={priority}
            sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, (max-width: 1280px) 20vw, 16vw"
            />
            )}
          </div>
        </AspectRatio>

        <div className="absolute top-2 right-2 sm:top-4 sm:right-4 flex flex-col gap-1 sm:gap-2">
          <ActionButton
            onClick={() => onFavoriteClick?.(variant.id)}
            icon={
              wishlistLoading ? (
                <div className="w-4 h-4 sm:w-5 sm:h-5 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
              ) : (
                <Heart className={`w-4 h-4 sm:w-5 sm:h-5 text-gray-600 ${isFavorite ? 'fill-red-500 text-red-500' : ''}`} />
              )
            }
            label={isFavorite ? "Remove from Wishlist" : "Add to Wishlist"}
            disabled={wishlistLoading}
          />
          <ActionButton onClick={onAddToCartHandler} icon={<ShoppingCart className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />} label="Add to Cart" className="translate-x-10 opacity-0 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-200"/>
          <ActionButton onClick={quickViewHandler} icon={<Search className="w-4 h-4 sm:w-5 sm:h-5 text-gray-600" />} label="Quick View" className="translate-x-10 opacity-0 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-200 delay-75"/>
          {showCompareButton && (
            <div className="translate-x-10 opacity-0 group-hover:translate-x-0 group-hover:opacity-100 transition-all duration-200 delay-100">
              <OptimizedCompareToggle variantId={variant.id} size="sm" />
            </div>
          )}
        </div>


        {_variantsWithImage?.length > 1 ? (
          <ul className="absolute bottom-0 left-0">
            {_variantsWithImage.map((variantItem, i) => (
              <li
              className={cn(
                "block",
                variant.id === variantItem.id ? "" : "opacity-30 hover:opacity-100 active:opacity-100"
              )}
              key={i}>
                <Image src={`${variantItem.image?.original_url}`} height={50} width={36} alt="wishlist" onClick={() => onClickHandle(variantItem)} style={{cursor: "pointer"}} role="button"/>
              </li>
            ))}
          </ul>
        ) : (
          ""
        )}


      </div>
      {/* Image end */}


      <div className="p-2 sm:p-4">
        <div className="flex items-center justify-between mb-1 sm:mb-2">
          {product.brand ? <h3 className="text-sm sm:text-lg font-medium text-gray-900 truncate">{product.brand}</h3> : ""}
          <div className="flex items-center">
            <Star className="w-3 h-3 sm:w-5 sm:h-5 text-yellow-400" />
            <span className="ml-1 text-xs sm:text-sm text-gray-600">{product?.rating}</span>
          </div>
        </div>

        <p className="text-gray-600 mb-2 sm:mb-3 text-sm sm:text-base line-clamp-2">
          <Link href={`/products/view/${product.slug}`} className="hover:underline">
            {product.name}
          </Link>
        </p>
        <div className="flex items-center gap-1 sm:gap-2 mb-2">
          <span className="text-lg sm:text-xl font-bold">${variant.price.sell_price.toFixed(2)}</span>
          {variant.price.compare_price > 0 && (
            <span className="text-gray-500 line-through text-sm sm:text-base">${variant.price.compare_price.toFixed(2)}</span>
          )}
        </div>

        {/* {product.colors && (
          <div className="flex gap-2 mb-3">
            {product.colors.map((color, index) => (
              <div 
                key={index}
                className={`w-4 h-4 rounded-full border border-gray-300 ${color}`}
              />
            ))}
          </div>
        )} */}
        {!!(variant.price.discount && variant.price.discount_type)  && <div className="flex items-center text-orange-500">
          <Clock className="w-4 h-4 mr-1" />
          <span className="text-sm">Limited Time Offer: {variant.price.discount}% off</span>
        </div>
        }
      </div>
    </div>
  )
}