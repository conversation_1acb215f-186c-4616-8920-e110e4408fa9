"use client"

import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import { Product, FilterOptions } from '@/types/product';
import { ProductUrlParams } from '@/lib/actions/product';
import { 
  calculateDynamicFilterCounts, 
  DynamicFilterCounts, 
  FilterParams 
} from '@/lib/utils/dynamic-filter-utils';

interface DynamicFilterContextType {
  dynamicFilters: DynamicFilterCounts;
  isLoading: boolean;
  updateProducts: (products: Product[]) => void;
  updateFilters: (filters: ProductUrlParams) => void;
}

const DynamicFilterContext = createContext<DynamicFilterContextType | undefined>(undefined);

interface DynamicFilterProviderProps {
  children: React.ReactNode;
  staticFilterOptions: FilterOptions;
  initialProducts: Product[];
  initialFilters: ProductUrlParams;
}

export function DynamicFilterProvider({
  children,
  staticFilterOptions,
  initialProducts,
  initialFilters
}: DynamicFilterProviderProps) {
  const [products, setProducts] = useState<Product[]>(initialProducts);
  const [currentFilters, setCurrentFilters] = useState<ProductUrlParams>(initialFilters);
  const [isLoading, setIsLoading] = useState(false);

  // Calculate dynamic filter counts whenever products or filters change
  const dynamicFilters = useMemo(() => {
    if (!products.length) {
      return {
        categories: [],
        brands: [],
        attributes: []
      };
    }

    const filterParams: FilterParams = {
      categories: currentFilters.categories,
      brands: currentFilters.brands,
      attributes: currentFilters.attributes,
      minPrice: currentFilters.minPrice,
      maxPrice: currentFilters.maxPrice,
      q: currentFilters.q
    };

    return calculateDynamicFilterCounts(
      products,
      filterParams,
      {
        categories: staticFilterOptions.categories || [],
        brands: staticFilterOptions.brands || [],
        attributes: staticFilterOptions.attributes || []
      }
    );
  }, [products, currentFilters, staticFilterOptions]);

  const updateProducts = (newProducts: Product[]) => {
    setProducts(newProducts);
  };

  const updateFilters = (newFilters: ProductUrlParams) => {
    setCurrentFilters(newFilters);
  };

  const value: DynamicFilterContextType = {
    dynamicFilters,
    isLoading,
    updateProducts,
    updateFilters
  };

  return (
    <DynamicFilterContext.Provider value={value}>
      {children}
    </DynamicFilterContext.Provider>
  );
}

export function useDynamicFilters() {
  const context = useContext(DynamicFilterContext);
  if (context === undefined) {
    throw new Error('useDynamicFilters must be used within a DynamicFilterProvider');
  }
  return context;
}

// Hook to get dynamic filter counts for a specific filter type
export function useDynamicFilterCounts() {
  const { dynamicFilters } = useDynamicFilters();
  return dynamicFilters;
}

// Hook to check if a filter should be hidden
export function useFilterVisibility() {
  const { dynamicFilters } = useDynamicFilters();
  
  return {
    shouldShowCategory: (categoryId: number) => {
      const category = findCategoryById(dynamicFilters.categories, categoryId);
      return category && category.product_count > 0;
    },
    shouldShowBrand: (brandId: number) => {
      const brand = dynamicFilters.brands.find(b => b.id === brandId);
      return brand && brand.product_count > 0;
    },
    shouldShowAttribute: (attributeId: number) => {
      const attribute = dynamicFilters.attributes.find(a => a.attribute_id === attributeId);
      return attribute && attribute.values.length > 0;
    },
    shouldShowAttributeValue: (attributeValueId: number) => {
      for (const attribute of dynamicFilters.attributes) {
        const value = attribute.values.find(v => v.attribute_value_id === attributeValueId);
        if (value && value.product_count > 0) {
          return true;
        }
      }
      return false;
    }
  };
}

// Helper function to find category by ID in tree structure
function findCategoryById(categories: any[], categoryId: number): any {
  for (const category of categories) {
    if (category.id === categoryId) {
      return category;
    }
    if (category.children) {
      const found = findCategoryById(category.children, categoryId);
      if (found) return found;
    }
  }
  return null;
}
