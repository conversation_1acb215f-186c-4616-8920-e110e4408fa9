"use client"

import { useState, useCallback, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { X, Filter, RotateCcw } from "lucide-react"
import { FilterOptions } from "@/types/product"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown } from "lucide-react"

interface EnhancedFilterContentProps {
  filterOptions: FilterOptions;
}

export function EnhancedFilterContent({ filterOptions }: EnhancedFilterContentProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const [priceRange, setPriceRange] = useState<[number, number]>([
    Number(searchParams.get('minPrice')) || filterOptions.priceRange[0],
    Number(searchParams.get('maxPrice')) || filterOptions.priceRange[1]
  ])
  
  const [openSections, setOpenSections] = useState({
    categories: true,
    brands: true,
    price: true,
    availability: false
  })

  // Separate state for each attribute
  const [openAttributes, setOpenAttributes] = useState<Record<number, boolean>>({})

  const updateURL = useCallback((key: string, value: string | null) => {
    const params = new URLSearchParams(searchParams.toString())
    
    if (value && value !== '') {
      params.set(key, value)
    } else {
      params.delete(key)
    }
    
    // Reset page when filters change
    params.delete('page')
    
    router.push(`/products?${params.toString()}`, { scroll: false })
  }, [searchParams, router])

  const toggleCategory = (categoryId: string) => {
    const currentCategories = searchParams.get('categories')?.split(',') || []
    const newCategories = currentCategories.includes(categoryId)
      ? currentCategories.filter(id => id !== categoryId)
      : [...currentCategories, categoryId]
    
    updateURL('categories', newCategories.length > 0 ? newCategories.join(',') : null)
  }

  const toggleBrand = (brandId: string) => {
    const currentBrands = searchParams.get('brands')?.split(',') || []
    const newBrands = currentBrands.includes(brandId)
      ? currentBrands.filter(id => id !== brandId)
      : [...currentBrands, brandId]

    updateURL('brands', newBrands.length > 0 ? newBrands.join(',') : null)
  }

  const handlePriceChange = (values: number[]) => {
    setPriceRange([values[0], values[1]])
  }

  const applyPriceFilter = useCallback(() => {
    const params = new URLSearchParams(searchParams.toString())

    // Set both min and max price at the same time
    params.set('minPrice', priceRange[0].toString())
    params.set('maxPrice', priceRange[1].toString())

    // Reset page when filters change
    params.delete('page')

    router.push(`/products?${params.toString()}`, { scroll: false })
  }, [priceRange, searchParams, router])

  // Auto-apply price filter with debounce when price range changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      // Only apply if the price range is different from URL params
      const currentMinPrice = Number(searchParams.get('minPrice')) || filterOptions.priceRange[0]
      const currentMaxPrice = Number(searchParams.get('maxPrice')) || filterOptions.priceRange[1]

      if (priceRange[0] !== currentMinPrice || priceRange[1] !== currentMaxPrice) {
        applyPriceFilter()
      }
    }, 1000) // 1 second debounce

    return () => clearTimeout(timeoutId)
  }, [priceRange, applyPriceFilter, searchParams, filterOptions.priceRange])

  const clearAllFilters = () => {
    router.push('/products', { scroll: false })
    setPriceRange(filterOptions.priceRange)
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (searchParams.get('categories')) count++
    if (searchParams.get('brands')) count++
    if (searchParams.get('minPrice') || searchParams.get('maxPrice')) count++
    if (searchParams.get('q')) count++
    if (searchParams.get('inStock')) count++
    if (searchParams.get('featured')) count++
    return count
  }

  const toggleSection = (section: keyof typeof openSections) => {
    setOpenSections(prev => ({ ...prev, [section]: !prev[section] }))
  }

  const toggleAttribute = (attributeId: number) => {
    setOpenAttributes(prev => ({
      ...prev,
      [attributeId]: !prev[attributeId]
    }))
  }

  const selectedCategories = searchParams.get('categories')?.split(',') || []
  const selectedBrands = searchParams.get('brands')?.split(',') || []
  const activeFiltersCount = getActiveFiltersCount()

  return (
    <div className="space-y-6">
      {/* Filter Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4" />
          <h3 className="font-semibold">Filters</h3>
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="text-xs">
              {activeFiltersCount}
            </Badge>
          )}
        </div>
        {activeFiltersCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="h-8 px-2 text-xs"
          >
            <RotateCcw className="h-3 w-3 mr-1" />
            Clear
          </Button>
        )}
      </div>

      {/* Active Filters */}
      {activeFiltersCount > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Active Filters</h4>
          <div className="flex flex-wrap gap-2">
            {selectedCategories.map(categoryId => {
              const category = filterOptions.categories.find(c => c.id.toString() === categoryId)
              return category ? (
                <Badge key={categoryId} variant="outline" className="text-xs">
                  {category.name}
                  <X 
                    className="h-3 w-3 ml-1 cursor-pointer" 
                    onClick={() => toggleCategory(categoryId)}
                  />
                </Badge>
              ) : null
            })}
            {selectedBrands.map(brandId => {
              const brand = filterOptions.brands.find(b => b.id.toString() === brandId)
              return brand ? (
                <Badge key={brandId} variant="outline" className="text-xs">
                  {brand.name}
                  <X 
                    className="h-3 w-3 ml-1 cursor-pointer" 
                    onClick={() => toggleBrand(brandId)}
                  />
                </Badge>
              ) : null
            })}
          </div>
        </div>
      )}

      <Separator />

      {/* Categories */}
      <Collapsible open={openSections.categories} onOpenChange={() => toggleSection('categories')}>
        <CollapsibleTrigger className="flex items-center justify-between w-full">
          <h4 className="font-medium">Categories</h4>
          <ChevronDown className={`h-4 w-4 transition-transform ${openSections.categories ? 'rotate-180' : ''}`} />
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 mt-3">
          {filterOptions.categories.map((category) => (
            <div key={category.id} className="flex items-center space-x-2">
              <Checkbox
                id={`category-${category.id}`}
                checked={selectedCategories.includes(category.id.toString())}
                onCheckedChange={() => toggleCategory(category.id.toString())}
              />
              <Label 
                htmlFor={`category-${category.id}`} 
                className="text-sm cursor-pointer flex-1"
              >
                {category.name}
              </Label>
              <span className="text-xs text-muted-foreground">
                ({category.product_count || 0})
              </span>
            </div>
          ))}
        </CollapsibleContent>
      </Collapsible>

      <Separator />

      {/* Brands */}
      <Collapsible open={openSections.brands} onOpenChange={() => toggleSection('brands')}>
        <CollapsibleTrigger className="flex items-center justify-between w-full">
          <h4 className="font-medium">Brands</h4>
          <ChevronDown className={`h-4 w-4 transition-transform ${openSections.brands ? 'rotate-180' : ''}`} />
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 mt-3">
          {filterOptions.brands.length === 0 ? (
            <p className="text-sm text-muted-foreground">No brands available</p>
          ) : (
            filterOptions.brands.map((brand) => (
              <div key={brand.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`brand-${brand.id}`}
                  checked={selectedBrands.includes(brand.id.toString())}
                  onCheckedChange={() => toggleBrand(brand.id.toString())}
                />
                <Label
                  htmlFor={`brand-${brand.id}`}
                  className="text-sm cursor-pointer flex-1"
                >
                  {brand.name}
                  <span className="text-xs text-muted-foreground ml-1">
                    ({brand.product_count || 0})
                  </span>
                </Label>
              </div>
            ))
          )}
        </CollapsibleContent>
      </Collapsible>

      <Separator />

      {/* Attributes */}
      {filterOptions.attributes && filterOptions.attributes.length > 0 && (
        <>
          {filterOptions.attributes.map((attribute) => (
            <Collapsible
              key={attribute.attribute_id}
              open={openAttributes[attribute.attribute_id] ?? false}
              onOpenChange={() => toggleAttribute(attribute.attribute_id)}
            >
              <CollapsibleTrigger className="flex items-center justify-between w-full">
                <h4 className="font-medium">{attribute.attribute_name}</h4>
                <ChevronDown className={`h-4 w-4 transition-transform ${openAttributes[attribute.attribute_id] ? 'rotate-180' : ''}`} />
              </CollapsibleTrigger>
              <CollapsibleContent className="space-y-2 mt-3">
                {attribute.values.map((value) => (
                  <div key={value.attribute_value_id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`attr-${value.attribute_value_id}`}
                        checked={searchParams.get('attributes')?.includes(value.attribute_value_id.toString()) || false}
                        onCheckedChange={(checked) => {
                          const currentAttrs = searchParams.get('attributes')?.split(',').filter(Boolean) || [];
                          const attrId = value.attribute_value_id.toString();
                          const newAttrs = checked
                            ? [...currentAttrs, attrId]
                            : currentAttrs.filter(id => id !== attrId);
                          updateURL('attributes', newAttrs.length > 0 ? newAttrs.join(',') : null);
                        }}
                      />
                      <Label htmlFor={`attr-${value.attribute_value_id}`} className="text-sm cursor-pointer flex items-center gap-2">
                        {value.color && (
                          <div
                            className="w-4 h-4 rounded-full border border-gray-300"
                            style={{ backgroundColor: value.color }}
                          />
                        )}
                        {value.value}
                      </Label>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      ({value.product_count || 0})
                    </span>
                  </div>
                ))}
              </CollapsibleContent>
            </Collapsible>
          ))}
          <Separator />
        </>
      )}

      {/* Price Range */}
      <Collapsible open={openSections.price} onOpenChange={() => toggleSection('price')}>
        <CollapsibleTrigger className="flex items-center justify-between w-full">
          <h4 className="font-medium">Price Range</h4>
          <ChevronDown className={`h-4 w-4 transition-transform ${openSections.price ? 'rotate-180' : ''}`} />
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-4 mt-3">
          <div className="space-y-3">
            <div className="px-2">
              <Slider
                value={priceRange}
                onValueChange={handlePriceChange}
                max={filterOptions.priceRange[1]}
                min={filterOptions.priceRange[0]}
                step={1}
                className="w-full"
              />
            </div>
            <div className="flex items-center justify-between text-sm font-medium">
              <div className="flex items-center gap-1">
                <span className="text-muted-foreground">Min:</span>
                <span className="text-foreground">${priceRange[0]}</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-muted-foreground">Max:</span>
                <span className="text-foreground">${priceRange[1]}</span>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div className="space-y-1">
                <label className="text-xs text-muted-foreground">Min Price</label>
                <input
                  type="number"
                  value={priceRange[0]}
                  onChange={(e) => {
                    const newMin = Math.max(filterOptions.priceRange[0], parseInt(e.target.value) || 0);
                    if (newMin <= priceRange[1]) {
                      setPriceRange([newMin, priceRange[1]]);
                    }
                  }}
                  className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-primary"
                  min={filterOptions.priceRange[0]}
                  max={priceRange[1]}
                />
              </div>
              <div className="space-y-1">
                <label className="text-xs text-muted-foreground">Max Price</label>
                <input
                  type="number"
                  value={priceRange[1]}
                  onChange={(e) => {
                    const newMax = Math.min(filterOptions.priceRange[1], parseInt(e.target.value) || filterOptions.priceRange[1]);
                    if (newMax >= priceRange[0]) {
                      setPriceRange([priceRange[0], newMax]);
                    }
                  }}
                  className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-primary"
                  min={priceRange[0]}
                  max={filterOptions.priceRange[1]}
                />
              </div>
            </div>
            <Button
              onClick={applyPriceFilter}
              size="sm"
              className="w-full"
              variant="outline"
            >
              Apply Price Filter
            </Button>
          </div>
        </CollapsibleContent>
      </Collapsible>

      <Separator />

      {/* Availability */}
      <Collapsible open={openSections.availability} onOpenChange={() => toggleSection('availability')}>
        <CollapsibleTrigger className="flex items-center justify-between w-full">
          <h4 className="font-medium">Availability</h4>
          <ChevronDown className={`h-4 w-4 transition-transform ${openSections.availability ? 'rotate-180' : ''}`} />
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 mt-3">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="in-stock"
              checked={searchParams.get('inStock') === 'true'}
              onCheckedChange={(checked) => updateURL('inStock', checked ? 'true' : null)}
            />
            <Label htmlFor="in-stock" className="text-sm cursor-pointer">
              In Stock Only
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="featured"
              checked={searchParams.get('featured') === 'true'}
              onCheckedChange={(checked) => updateURL('featured', checked ? 'true' : null)}
            />
            <Label htmlFor="featured" className="text-sm cursor-pointer">
              Featured Products
            </Label>
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  )
}
