"use client"
import { useState } from 'react';
import ProductGridItem from "@/components/product/product-grid-item";
import { Product } from '@/types/product';
import SimpleProductLoader from './simple-product-loader';
import { ProductUrlParams } from '@/lib/actions/product';
import { addToWishlist, removeFromWishlist, getWishlistVariantIds } from '@/lib/actions/wishlist';

import { useSession } from 'next-auth/react';
import { toast } from 'sonner';
import { useEffect } from 'react';

interface SimpleProductGridProps {
  initialProducts: Product[];
  searchParams: ProductUrlParams;
}

export default function SimpleProductGrid({ initialProducts, searchParams }: SimpleProductGridProps) {
  const { data: session } = useSession();
  const [products, setProducts] = useState<Product[]>(initialProducts);
  const [favorites, setFavorites] = useState<Set<number>>(new Set());
  const [wishlistLoading, setWishlistLoading] = useState(false);

  // Load wishlist on mount
  useEffect(() => {
    const loadWishlist = async () => {
      if (session?.user) {
        try {
          const wishlistIds = await getWishlistVariantIds();
          setFavorites(new Set(wishlistIds));
        } catch (error) {
          console.error('Failed to load wishlist and compare data:', error);
        }
      } else {
        setFavorites(new Set());
      }
    };

    loadWishlist();
  }, [session]);

  const handleProductsUpdate = (newProducts: Product[], hasMore: boolean) => {
    setProducts(newProducts);
  };

  const toggleFavorite = async (variantId: number) => {
    if (!session?.user) {
      toast.error('Please sign in to add items to your wishlist');
      return;
    }

    setWishlistLoading(true);
    const isFavorite = favorites.has(variantId);

    try {
      if (isFavorite) {
        await removeFromWishlist(variantId);
        setFavorites(prev => {
          const newSet = new Set(prev);
          newSet.delete(variantId);
          return newSet;
        });
        toast.success('Removed from wishlist');
      } else {
        await addToWishlist(variantId);
        setFavorites(prev => new Set([...prev, variantId]));
        toast.success('Added to wishlist');
      }
    } catch (error) {
      console.error('Wishlist error:', error);
      toast.error('Failed to update wishlist');
    } finally {
      setWishlistLoading(false);
    }
  };



  return (
    <>
      {/* Enhanced responsive grid with better mobile spacing */}
      <div className="grid grid-cols-2 gap-3 sm:gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6">
        {products.map((product) => {
          const firstVariantId = product.variants[0]?.id;
          return (
            <ProductGridItem
              key={product.id}
              product={product}
              isFavorite={firstVariantId ? favorites.has(firstVariantId) : false}
              onFavoriteClick={toggleFavorite}
              wishlistLoading={wishlistLoading}
            />
          );
        })}
      </div>
      
      {/* Infinite scroll loader */}
      <SimpleProductLoader
        initialProducts={initialProducts}
        searchParams={searchParams}
        onProductsUpdate={handleProductsUpdate}
      />
    </>
  );
}

// Loading skeleton component
function ProductGridSkeleton() {
  return (
    <div className="grid grid-cols-2 gap-3 sm:gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6">
      {Array.from({ length: 12 }).map((_, i) => (
        <div key={i} className="space-y-3">
          <div className="aspect-square w-full animate-pulse rounded-lg bg-muted" />
          <div className="space-y-2">
            <div className="h-4 w-3/4 animate-pulse rounded bg-muted" />
            <div className="h-4 w-1/2 animate-pulse rounded bg-muted" />
          </div>
        </div>
      ))}
    </div>
  );
}
