"use client"

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { Heart, RefreshCw } from "lucide-react";
import { toast } from "sonner";
import { useWishlistContext } from "@/components/wishlist/wishlist-context";
import { checkWishlistStatus } from "@/lib/actions/wishlist";
import { cn } from "@/lib/utils";

interface WishlistButtonProps {
  productId?: number; // Keep for backward compatibility
  variantId?: number; // New preferred prop
  variant?: "default" | "outline" | "ghost";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  showText?: boolean;
}

export default function WishlistButton({
  productId,
  variantId,
  variant = "outline",
  size = "default",
  className,
  showText = true
}: WishlistButtonProps) {
  const { data: session } = useSession();
  const { addToWishlistAction, removeFromWishlistAction, isInWishlist: contextIsInWishlist } = useWishlistContext();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  // Use variantId if provided, otherwise fall back to productId for backward compatibility
  const targetId = variantId || productId;

  // Use context to determine if item is in wishlist
  const isInWishlist = targetId ? contextIsInWishlist(targetId) : false;

  // Set initial loading to false since we're using context
  useEffect(() => {
    setInitialLoading(false);
  }, []);

  const handleToggleWishlist = async () => {
    if (!targetId) {
      toast.error('Invalid product or variant');
      return;
    }

    setLoading(true);

    try {
      if (isInWishlist) {
        const result = await removeFromWishlistAction(targetId);
        if (!result.success) {
          toast.error(result.error || 'Failed to remove from wishlist');
          return;
        }
        toast.success('Removed from wishlist');
      } else {
        const result = await addToWishlistAction(targetId);
        if (!result.success) {
          toast.error(result.error || 'Failed to add to wishlist');
          return;
        }
        toast.success('Added to wishlist');
      }
    } catch (error) {
      toast.error('Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <Button 
        variant={variant} 
        size={size}
        className={cn("w-full", className)}
        disabled
      >
        <RefreshCw className="h-4 w-4 animate-spin mr-2" />
        {showText && "Loading..."}
      </Button>
    );
  }

  return (
    <Button 
      variant={variant} 
      size={size}
      className={cn("w-full", className)}
      onClick={handleToggleWishlist}
      disabled={loading}
    >
      {loading ? (
        <RefreshCw className="h-4 w-4 animate-spin mr-2" />
      ) : (
        <Heart 
          className={cn(
            "h-4 w-4",
            showText && "mr-2",
            isInWishlist && "fill-red-500 text-red-500"
          )} 
        />
      )}
      {showText && (isInWishlist ? "Remove from Wishlist" : "Add to Wishlist")}
    </Button>
  );
}
