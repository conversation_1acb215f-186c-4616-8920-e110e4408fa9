"use client"

import { useState, memo } from "react"
import Image from "next/image"
import Link from "next/link"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Heart, ShoppingCart, RotateCcw, Star } from 'lucide-react'
import { Product } from "@/types/product"
import { useCompareContext } from "@/components/compare/compare-context"
import { useWishlistContext } from "@/components/wishlist/wishlist-context"
import { addToCart } from "@/lib/actions/cart"
import { toast } from "sonner"
import { formatPrice, cn } from "@/lib/utils"

interface ProductGridItemProps {
  product: Product
  isFavorite?: boolean
  isInCompare?: boolean
  priority?: boolean
  loading?: 'eager' | 'lazy'
  position?: number // For structured data position
}

// Memoized component for better performance
export const ProductGridItem = memo(function ProductGridItem({
  product,
  isFavorite = false,
  isInCompare = false,
  priority = false,
  loading = 'lazy',
  position
}: ProductGridItemProps) {
  const { addToWishlistAction, removeFromWishlistAction } = useWishlistContext()
  const { addToCompareAction, removeFromCompareAction } = useCompareContext()
  const [isAddingToCart, setIsAddingToCart] = useState(false)
  const [wishlistLoading, setWishlistLoading] = useState(false)
  const [compareLoading, setCompareLoading] = useState(false)

  const variant = product.variants[0]
  const mainImage = product.images?.[0]
  const price = variant?.price?.sell_price || variant?.price?.price || 0
  const comparePrice = variant?.price?.compare_price
  const hasDiscount = comparePrice && comparePrice > price
  const isOutOfStock = variant?.stock !== undefined && variant.stock <= 0

  const handleWishlistToggle = async () => {
    if (!variant?.id) return
    
    setWishlistLoading(true)
    try {
      const result = isFavorite 
        ? await removeFromWishlistAction(variant.id)
        : await addToWishlistAction(variant.id)
      
      if (!result.success) {
        toast.error(result.error || 'Failed to update wishlist')
      }
    } catch (error) {
      toast.error('Something went wrong')
    } finally {
      setWishlistLoading(false)
    }
  }

  const handleCompareToggle = async () => {
    if (!variant?.id) return
    
    setCompareLoading(true)
    try {
      const result = isInCompare 
        ? await removeFromCompareAction(variant.id)
        : await addToCompareAction(variant.id)
      
      if (!result.success) {
        toast.error(result.error || 'Failed to update compare list')
      }
    } catch (error) {
      toast.error('Something went wrong')
    } finally {
      setCompareLoading(false)
    }
  }

  const handleAddToCart = async () => {
    if (!variant?.id || isAddingToCart || isOutOfStock) return
    
    setIsAddingToCart(true)
    try {
      const result = await addToCart(variant.id, null, 1)
      if (result.success) {
        toast.success('Added to cart')
      } else {
        toast.error(result.message || 'Failed to add to cart')
      }
    } catch (error) {
      toast.error('Failed to add to cart')
    } finally {
      setIsAddingToCart(false)
    }
  }

  // Generate structured data for this product
  const productStructuredData = {
    "@type": "Product",
    "@id": `${process.env.NEXT_PUBLIC_APP_URL}/products/view/${product.slug}`,
    "name": product.name,
    "description": product.description,
    "image": mainImage?.original_url,
    "sku": variant?.sku,
    "brand": {
      "@type": "Brand",
      "name": product.brand || "Store Brand"
    },
    "offers": {
      "@type": "Offer",
      "price": price,
      "priceCurrency": "USD",
      "availability": isOutOfStock ? "https://schema.org/OutOfStock" : "https://schema.org/InStock",
      "url": `${process.env.NEXT_PUBLIC_APP_URL}/products/view/${product.slug}`,
      "priceValidUntil": new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30 days from now
    },
    "aggregateRating": product.rating ? {
      "@type": "AggregateRating",
      "ratingValue": product.rating,
      "reviewCount": product.reviewCount || 1
    } : undefined
  }

  return (
    <article 
      className="group relative bg-white rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-lg transition-all duration-200"
      itemScope 
      itemType="https://schema.org/Product"
      data-product-id={product.id}
      data-position={position}
    >
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(productStructuredData) }}
      />

      {/* Product Image */}
      <div className="relative overflow-hidden rounded-t-lg">
        <AspectRatio ratio={1} className="bg-gray-100">
          <Link 
            href={`/products/view/${product.slug}`}
            className="block w-full h-full"
            aria-label={`View ${product.name}`}
          >
            {mainImage ? (
              <Image
                src={mainImage.medium_url || mainImage.original_url}
                alt={product.name}
                fill
                className="object-cover transition-transform duration-300 group-hover:scale-105"
                priority={priority}
                loading={loading}
                sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
                itemProp="image"
              />
            ) : (
              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                <span className="text-gray-400 text-sm">No image</span>
              </div>
            )}
          </Link>
        </AspectRatio>

        {/* Badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-1">
          {hasDiscount && (
            <Badge variant="destructive" className="text-xs">
              {Math.round(((comparePrice - price) / comparePrice) * 100)}% OFF
            </Badge>
          )}
          {isOutOfStock && (
            <Badge variant="secondary" className="text-xs">
              Out of Stock
            </Badge>
          )}
        </div>

        {/* Action Buttons */}
        <div className="absolute top-2 right-2 flex flex-col gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <Button
            variant="secondary"
            size="icon"
            className="h-8 w-8"
            onClick={handleWishlistToggle}
            disabled={wishlistLoading}
            aria-label={isFavorite ? "Remove from wishlist" : "Add to wishlist"}
          >
            <Heart 
              className={cn(
                "h-4 w-4",
                isFavorite && "fill-red-500 text-red-500"
              )} 
            />
          </Button>
          
          <Button
            variant="secondary"
            size="icon"
            className="h-8 w-8"
            onClick={handleCompareToggle}
            disabled={compareLoading}
            aria-label={isInCompare ? "Remove from compare" : "Add to compare"}
          >
            <RotateCcw 
              className={cn(
                "h-4 w-4",
                isInCompare && "fill-blue-500 text-blue-500"
              )} 
            />
          </Button>
        </div>
      </div>

      {/* Product Info */}
      <div className="p-4">
        <div className="space-y-2">
          {/* Product Name */}
          <Link 
            href={`/products/view/${product.slug}`}
            className="block"
          >
            <h3 
              className="font-medium text-sm text-gray-900 line-clamp-2 hover:text-blue-600 transition-colors"
              itemProp="name"
            >
              {product.name}
            </h3>
          </Link>

          {/* Rating */}
          {product.rating && (
            <div className="flex items-center gap-1" itemProp="aggregateRating" itemScope itemType="https://schema.org/AggregateRating">
              <div className="flex">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={cn(
                      "h-3 w-3",
                      i < Math.floor(product.rating) 
                        ? "fill-yellow-400 text-yellow-400" 
                        : "text-gray-300"
                    )}
                  />
                ))}
              </div>
              <span className="text-xs text-gray-500">
                (<span itemProp="reviewCount">{product.reviewCount || 0}</span>)
              </span>
              <meta itemProp="ratingValue" content={product.rating.toString()} />
            </div>
          )}

          {/* Price */}
          <div className="flex items-center gap-2" itemProp="offers" itemScope itemType="https://schema.org/Offer">
            <span 
              className="font-bold text-lg text-gray-900"
              itemProp="price"
              content={price.toString()}
            >
              {formatPrice(price)}
            </span>
            {hasDiscount && (
              <span className="text-sm text-gray-500 line-through">
                {formatPrice(comparePrice)}
              </span>
            )}
            <meta itemProp="priceCurrency" content="USD" />
            <meta 
              itemProp="availability" 
              content={isOutOfStock ? "https://schema.org/OutOfStock" : "https://schema.org/InStock"} 
            />
          </div>

          {/* SKU */}
          {variant?.sku && (
            <p className="text-xs text-gray-500" itemProp="sku">
              SKU: {variant.sku}
            </p>
          )}
        </div>

        {/* Add to Cart Button */}
        <Button
          onClick={handleAddToCart}
          disabled={isAddingToCart || isOutOfStock}
          className="w-full mt-3"
          size="sm"
        >
          {isAddingToCart ? (
            <>
              <ShoppingCart className="mr-2 h-4 w-4 animate-pulse" />
              Adding...
            </>
          ) : isOutOfStock ? (
            "Out of Stock"
          ) : (
            <>
              <ShoppingCart className="mr-2 h-4 w-4" />
              Add to Cart
            </>
          )}
        </Button>
      </div>
    </article>
  )
})
