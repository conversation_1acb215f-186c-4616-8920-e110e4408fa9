"use client"
import { useState, useEffect } from 'react';
import ProductGridItem from "@/components/product/product-grid-item";
import { Product } from '@/types/product';
import { ProductUrlParams } from '@/lib/actions/product';
import { getWishlistVariantIds } from '@/lib/actions/wishlist'
import { useWishlistContext } from '@/components/wishlist/wishlist-context';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';

interface SSRProductGridProps {
  products: Product[];
  searchParams: ProductUrlParams;
  hasMore: boolean;
  currentPage: number;
}

export default function SSRProductGrid({ 
  products, 
  searchParams, 
  hasMore, 
  currentPage 
}: SSRProductGridProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const { addToWishlistAction, removeFromWishlistAction } = useWishlistContext();
  const [favorites, setFavorites] = useState<Set<number>>(new Set());
  const [wishlistLoading, setWishlistLoading] = useState(false);

  // Load wishlist on mount
  useEffect(() => {
    const loadWishlist = async () => {
      if (session?.user) {
        try {
          const wishlistIds = await getWishlistVariantIds();
          setFavorites(new Set(wishlistIds));
        } catch (error) {
          console.error('Failed to load wishlist:', error);
        }
      }
    };

    loadWishlist();
  }, [session]);

  const toggleFavorite = async (variantId: number) => {
    setWishlistLoading(true);
    const isFavorite = favorites.has(variantId);

    try {
      if (isFavorite) {
        const result = await removeFromWishlistAction(variantId);
        if (!result.success) {
          toast.error(result.error || 'Failed to remove from wishlist');
          return;
        }
        setFavorites(prev => {
          const newSet = new Set(prev);
          newSet.delete(variantId);
          return newSet;
        });
        toast.success('Removed from wishlist');
      } else {
        const result = await addToWishlistAction(variantId);
        if (!result.success) {
          toast.error(result.error || 'Failed to add to wishlist');
          return;
        }
        setFavorites(prev => new Set([...prev, variantId]));
        toast.success('Added to wishlist');
      }
    } catch (error) {
      console.error('Wishlist error:', error);
      toast.error('Failed to update wishlist');
    } finally {
      setWishlistLoading(false);
    }
  };

  const loadMoreProducts = () => {
    const params = new URLSearchParams();
    
    // Copy all current search params
    Object.entries(searchParams).forEach(([key, value]) => {
      if (value && key !== 'page') {
        params.set(key, value.toString());
      }
    });
    
    // Set next page
    params.set('page', (currentPage + 1).toString());
    
    // Navigate to new URL - this will trigger SSR
    router.push(`/products?${params.toString()}`, { scroll: false });
  };

  return (
    <>
      {/* Enhanced responsive grid with better mobile spacing */}
      <div className="grid grid-cols-2 gap-3 sm:gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6">
        {products.map((product) => {
          const firstVariantId = product.variants[0]?.id;
          return (
            <ProductGridItem
              key={product.id}
              product={product}
              isFavorite={firstVariantId ? favorites.has(firstVariantId) : false}
              onFavoriteClick={toggleFavorite}
              wishlistLoading={wishlistLoading}
            />
          );
        })}
      </div>
      
      {/* Load More Button for SSR */}
      {hasMore && (
        <div className="flex justify-center mt-8">
          <Button 
            onClick={loadMoreProducts}
            variant="outline"
            size="lg"
            className="px-8"
          >
            Load More Products
          </Button>
        </div>
      )}
      
      {/* End of results message */}
      {!hasMore && products.length > 0 && (
        <div className="flex flex-col items-center gap-3 text-center mt-8">
          <div className="text-muted-foreground">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div>
            <h3 className="font-medium text-foreground mb-1">You&apos;ve reached the end!</h3>
            <p className="text-sm text-muted-foreground">
              You&apos;ve seen all {products.length} products matching your criteria.
            </p>
          </div>
        </div>
      )}
      
      {/* No products found */}
      {products.length === 0 && (
        <div className="flex flex-col items-center gap-3 text-center py-12">
          <div className="text-muted-foreground">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-medium text-foreground mb-2">No products found</h3>
            <p className="text-muted-foreground max-w-md">
              We couldn&apos;t find any products matching your search criteria. Try adjusting your filters or search terms.
            </p>
          </div>
        </div>
      )}
    </>
  );
}
