"use client"
import { useState, useEffect, useCallback, useRef } from 'react';
import { useInView } from 'react-intersection-observer';
import ProductGridItem from "@/components/product/product-grid-item";
import { Product } from '@/types/product';
import { ProductUrlParams, fetchProducts } from '@/lib/actions/product';
import { useDynamicFilters } from '@/components/product/dynamic-filter-context';
import { getWishlistVariantIds } from '@/lib/actions/wishlist'
import { SimpleCompareToggle } from '@/components/compare/simple-compare-toggle'
import { useWishlistContext } from '@/components/wishlist/wishlist-context';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { ChevronUp, RefreshCw } from 'lucide-react';
import { useInfiniteScrollPerformance } from '@/hooks/use-performance-observer';

interface EnhancedProductGridProps {
  initialProducts: Product[];
  searchParams: ProductUrlParams;
  totalCount?: number;
  autoLoad?: boolean; // Option to enable/disable auto-loading
}

export default function EnhancedProductGrid({
  initialProducts,
  searchParams,
  totalCount = 0,
  autoLoad = true // Default to auto-loading enabled
}: EnhancedProductGridProps) {
  const { data: session } = useSession();
  const { addToWishlistAction, removeFromWishlistAction } = useWishlistContext();
  const { addToCompareAction, removeFromCompareAction } = useCompareContext();
  const [products, setProducts] = useState<Product[]>(initialProducts);
  const [favorites, setFavorites] = useState<Set<number>>(new Set());
  const [compareItems, setCompareItems] = useState<Set<number>>(new Set());
  const [wishlistLoading, setWishlistLoading] = useState(false);
  const [compareLoading, setCompareLoading] = useState(false);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(() => {
    // Has more if we haven't reached the total count yet
    return totalCount === 0 || initialProducts.length < totalCount;
  });
  const [page, setPage] = useState(0); // API uses 0-based pagination
  const [error, setError] = useState<string | null>(null);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [emptyPageCount, setEmptyPageCount] = useState(0); // Track consecutive empty pages
  const loadingRef = useRef(false);

  // Dynamic filter context
  const { updateProducts, updateFilters } = useDynamicFilters();

  // Monitor performance for infinite scroll
  useInfiniteScrollPerformance();

  // Intersection Observer for infinite scroll with proper control
  const { ref: loadMoreRef, inView } = useInView({
    threshold: 0.1,
    rootMargin: '50px',
    triggerOnce: false,
  });

  // Use a more stable approach - only reset when URL actually changes
  const searchParamsString = JSON.stringify(searchParams);
  const prevSearchParamsRef = useRef(searchParamsString);
  const isFirstRender = useRef(true);

  useEffect(() => {
    const searchParamsChanged = prevSearchParamsRef.current !== searchParamsString;

    if (isFirstRender.current || searchParamsChanged) {
      // Only reset on first render or when search params actually change
      setProducts(initialProducts);
      setPage(0); // Reset to 0-based pagination
      setHasMore(() => {
        return totalCount === 0 || initialProducts.length < totalCount;
      });
      setError(null);
      setEmptyPageCount(0); // Reset empty page counter
      loadingRef.current = false;
      loadTriggeredRef.current = false;
      lastInViewRef.current = false;

      prevSearchParamsRef.current = searchParamsString;
      isFirstRender.current = false;

      // Update dynamic filter context with initial products and new filters
      updateProducts(initialProducts);
      updateFilters(searchParams);
    }
  }, [searchParamsString, initialProducts, totalCount]);

  // Load wishlist on mount and when session changes
  useEffect(() => {
    const loadWishlist = async () => {
      console.log('loadWishlist called, session:', session?.user ? 'authenticated' : session === null ? 'not authenticated' : 'loading');

      // Wait a bit for session to be fully loaded
      if (session === undefined) {
        // Session is still loading, don't do anything yet
        console.log('Session still loading, waiting...');
        return;
      }

      if (session?.user) {
        try {
          console.log('User authenticated, loading wishlist and compare data...');
          const [wishlistIds, compareIds] = await Promise.all([
            getWishlistVariantIds(),
            getCompareVariantIds()
          ]);
          console.log('Loaded wishlist variant IDs:', wishlistIds);
          console.log('Loaded compare variant IDs:', compareIds);
          setFavorites(new Set(wishlistIds));
          setCompareItems(new Set(compareIds));
        } catch (error) {
          console.error('Failed to load wishlist and compare data:', error);
          // Clear favorites and compare items on error to avoid stale state
          setFavorites(new Set());
          setCompareItems(new Set());
        }
      } else {
        // Clear favorites and compare items when user is not logged in
        console.log('User not authenticated, clearing favorites and compare items');
        setFavorites(new Set());
        setCompareItems(new Set());
      }
    };

    loadWishlist();
  }, [session]); // Watch the entire session object

  // Handle scroll to top button
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 1000);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Load more products - SIMPLIFIED and FIXED
  const loadMoreProducts = useCallback(async () => {
    // Simple guards to prevent multiple simultaneous calls
    if (loading || !hasMore || loadingRef.current) {
      return;
    }

    // Additional safety check: don't load if we already have all products
    if (totalCount > 0 && products.length >= totalCount) {
      setHasMore(false);
      return;
    }

    // Set loading state IMMEDIATELY to prevent duplicate calls
    loadingRef.current = true;
    setLoading(true);
    setError(null);

    try {
      const nextPage = page + 1;

      const newProducts = await fetchProducts({
        ...searchParams,
        page: nextPage.toString(),
        pageSize: '20'
      });

      if (newProducts.length === 0) {
        // Increment empty page counter
        const newEmptyCount = emptyPageCount + 1;
        setEmptyPageCount(newEmptyCount);

        // Stop only after 3 consecutive empty pages OR if we've reached the total count
        if (newEmptyCount >= 3 || (totalCount > 0 && products.length >= totalCount)) {
          setHasMore(false);
        } else {
          // Continue to next page even if this one was empty
          setPage(nextPage);
        }
      } else {
        // Reset empty page counter since we got products
        setEmptyPageCount(0);

        // Avoid duplicates
        const existingIds = new Set(products.map(p => p.id));
        const uniqueNewProducts = newProducts.filter(p => !existingIds.has(p.id));
        const updatedProducts = [...products, ...uniqueNewProducts];

        // Update products and page
        setProducts(updatedProducts);
        setPage(nextPage);

        // Update dynamic filter context with new products
        updateProducts(updatedProducts);

        // Check if we should continue loading
        // Continue if we haven't reached the total count yet
        const shouldContinue = totalCount === 0 || updatedProducts.length < totalCount;

        setHasMore(shouldContinue);
      }
    } catch (err) {
      console.error('Error loading products:', err);
      setError('Failed to load more products');
    } finally {
      // CRITICAL: Reset loading state
      setLoading(false);
      loadingRef.current = false;
    }
  }, [loading, hasMore, page, searchParams, totalCount, products]);

  // Track if we've already triggered loading for current inView state
  const lastInViewRef = useRef(false);
  const loadTriggeredRef = useRef(false);

  // Trigger load more when in view - COMPLETELY REWRITTEN to prevent infinite calls
  useEffect(() => {
    // Only trigger when inView changes from false to true
    if (inView && !lastInViewRef.current && !loadTriggeredRef.current) {
      lastInViewRef.current = true;

      if (autoLoad && hasMore && !loading && !loadingRef.current) {
        loadTriggeredRef.current = true;

        const timer = setTimeout(() => {
          // Final check before loading
          if (hasMore && !loading && !loadingRef.current) {
            loadMoreProducts();
          }
        }, 100);

        return () => clearTimeout(timer);
      }
    } else if (!inView) {
      lastInViewRef.current = false;
      loadTriggeredRef.current = false;
    }
  }, [inView, autoLoad, hasMore, loading, loadMoreProducts]);

  const toggleFavorite = async (variantId: number) => {
    if (!session?.user) {
      toast.error('Please sign in to add items to your wishlist');
      return;
    }

    setWishlistLoading(true);
    const isFavorite = favorites.has(variantId);

    try {
      if (isFavorite) {
        const result = await removeFromWishlistAction(variantId);
        if (!result.success) {
          toast.error(result.error || 'Failed to remove from wishlist');
          return;
        }
        setFavorites(prev => {
          const newSet = new Set(prev);
          newSet.delete(variantId);
          return newSet;
        });
        toast.success('Removed from wishlist');
      } else {
        const result = await addToWishlistAction(variantId);
        if (!result.success) {
          toast.error(result.error || 'Failed to add to wishlist');
          return;
        }
        setFavorites(prev => new Set([...prev, variantId]));
        toast.success('Added to wishlist');
      }
    } catch (error) {
      console.error('Wishlist error:', error);
      toast.error('Failed to update wishlist');
    } finally {
      setWishlistLoading(false);
    }
  };

  const toggleCompare = async (variantId: number) => {
    setCompareLoading(true);
    const isInCompare = compareItems.has(variantId);

    try {
      if (isInCompare) {
        const result = await removeFromCompareAction(variantId);
        if (!result.success) {
          toast.error(result.error || 'Failed to remove from compare');
          return;
        }
        setCompareItems(prev => {
          const newSet = new Set(prev);
          newSet.delete(variantId);
          return newSet;
        });
        toast.success('Removed from compare');
      } else {
        const result = await addToCompareAction(variantId);
        if (!result.success) {
          toast.error(result.error || 'Failed to add to compare');
          return;
        }
        setCompareItems(prev => new Set([...prev, variantId]));
        toast.success('Added to compare');
      }
    } catch (error) {
      console.error('Compare error:', error);
      toast.error('Failed to update compare list');
    } finally {
      setCompareLoading(false);
    }
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleRetry = () => {
    setError(null);
    loadMoreProducts();
  };

  return (
    <>
      {/* Products Grid */}
      <div className="grid grid-cols-2 gap-3 sm:gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6">
        {products.map((product, index) => {
          const firstVariantId = product.variants[0]?.id;
          const isFav = firstVariantId ? favorites.has(firstVariantId) : false;
          const isInCompare = firstVariantId ? compareItems.has(firstVariantId) : false;
          // Debug log for first few products
          if (index < 3) {
            console.log(`Product ${product.id} (${product.name}): variant=${firstVariantId}, isFavorite=${isFav}, isInCompare=${isInCompare}`);
          }

          return (
            <ProductGridItem
              key={`${product.id}-${index}`}
              product={product}
              isFavorite={isFav}
              onFavoriteClick={toggleFavorite}
              wishlistLoading={wishlistLoading}
              isInCompare={isInCompare}
              onCompareClick={toggleCompare}
              compareLoading={compareLoading}
              priority={index < 8} // Prioritize first 8 images
            />
          );
        })}
      </div>

      {/* Loading/Error States */}
      <div ref={loadMoreRef} className="flex flex-col justify-center items-center py-8 min-h-[120px]">
        {/* Manual Load More Button */}
        {!autoLoad && hasMore && !loading && !error && (
          <Button
            onClick={loadMoreProducts}
            variant="outline"
            size="lg"
            className="mb-4"
            disabled={loading}
          >
            {loading ? 'Loading...' : 'Load More Products'}
          </Button>
        )}

        {loading && (
          <div className="flex flex-col items-center gap-3">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
            <p className="text-sm text-muted-foreground">Loading more products...</p>
          </div>
        )}

        {error && (
          <div className="flex flex-col items-center gap-3 text-center">
            <div className="text-destructive">
              <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div>
              <h3 className="font-medium text-foreground mb-1">Failed to load products</h3>
              <p className="text-sm text-muted-foreground mb-3">{error}</p>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRetry}
                disabled={loading}
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Try again
              </Button>
            </div>
          </div>
        )}

        {!loading && !error && !hasMore && products.length > 0 && (
          <div className="flex flex-col items-center gap-3 text-center">
            <div className="text-muted-foreground">
              <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h3 className="font-medium text-foreground mb-1">You&apos;ve reached the end!</h3>
              <p className="text-sm text-muted-foreground">
                You&apos;ve seen all {products.length} products{totalCount > 0 && ` of ${totalCount}`} matching your criteria.
              </p>
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={scrollToTop}
              className="mt-2"
            >
              <ChevronUp className="w-4 h-4 mr-2" />
              Back to top
            </Button>
          </div>
        )}

        {!loading && !error && !hasMore && products.length === 0 && (
          <div className="flex flex-col items-center gap-3 text-center py-12">
            <div className="text-muted-foreground">
              <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <div>
              <h3 className="text-lg font-medium text-foreground mb-2">No products found</h3>
              <p className="text-muted-foreground max-w-md">
                We couldn&apos;t find any products matching your search criteria. Try adjusting your filters or search terms.
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Scroll to top button */}
      {showScrollTop && (
        <Button
          onClick={scrollToTop}
          size="icon"
          className="fixed bottom-6 right-6 z-50 rounded-full shadow-lg"
          aria-label="Scroll to top"
        >
          <ChevronUp className="w-4 h-4" />
        </Button>
      )}
    </>
  );
}
