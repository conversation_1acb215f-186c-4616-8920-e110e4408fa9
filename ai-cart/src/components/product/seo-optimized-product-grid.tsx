import { Suspense } from 'react'
import { Metada<PERSON> } from 'next'
import { Product } from '@/types/product'
import { ProductGridItem } from '@/components/product/product-grid-item-seo'
import { ProductGridSkeleton } from '@/components/product/product-grid-skeleton'
import { getWishlistVariantIds } from '@/lib/actions/wishlist'
import { auth } from '~/auth'

interface SEOOptimizedProductGridProps {
  products: Product[]
  title?: string
  description?: string
  category?: string
  totalCount?: number
  currentPage?: number
  pageSize?: number
}

// Server component for better SEO and performance
export async function SEOOptimizedProductGrid({
  products,
  title,
  description,
  category,
  totalCount = 0,
  currentPage = 1,
  pageSize = 20
}: SEOOptimizedProductGridProps) {
  const session = await auth()
  
  // Pre-load wishlist data on server for better performance
  let wishlistIds: number[] = []

  if (session?.user?.id) {
    try {
      wishlistIds = await getWishlistVariantIds()
    } catch (error) {
      console.error('Failed to load wishlist data:', error)
    }
  }

  // Generate structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": title || "Products",
    "description": description,
    "numberOfItems": totalCount,
    "itemListElement": products.map((product, index) => ({
      "@type": "ListItem",
      "position": (currentPage - 1) * pageSize + index + 1,
      "item": {
        "@type": "Product",
        "@id": `${process.env.NEXT_PUBLIC_APP_URL}/products/view/${product.slug}`,
        "name": product.name,
        "description": product.description,
        "url": `${process.env.NEXT_PUBLIC_APP_URL}/products/view/${product.slug}`,
        "image": product.images?.[0]?.original_url,
        "sku": product.variants?.[0]?.sku,
        "offers": {
          "@type": "Offer",
          "price": product.variants?.[0]?.price?.sell_price || product.variants?.[0]?.price?.price,
          "priceCurrency": "USD", // You might want to make this dynamic
          "availability": product.variants?.[0]?.stock > 0 ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
          "url": `${process.env.NEXT_PUBLIC_APP_URL}/products/view/${product.slug}`
        }
      }
    }))
  }

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      
      {/* SEO Meta Tags */}
      <div className="sr-only">
        <h1>{title || "Products"}</h1>
        {description && <p>{description}</p>}
        {category && <p>Category: {category}</p>}
        <p>Showing {products.length} of {totalCount} products</p>
      </div>

      {/* Product Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 sm:gap-6">
        <Suspense fallback={<ProductGridSkeleton count={products.length} />}>
          {products.map((product, index) => {
            const firstVariantId = product.variants[0]?.id
            const isFavorite = firstVariantId ? wishlistIds.includes(firstVariantId) : false
            
            return (
              <ProductGridItem
                key={product.id}
                product={product}
                isFavorite={isFavorite}
                priority={index < 8} // Prioritize first 8 images for LCP
                loading={index < 4 ? 'eager' : 'lazy'} // Load first 4 images eagerly
                position={(currentPage - 1) * pageSize + index + 1}
              />
            )
          })}
        </Suspense>
      </div>

      {/* Pagination info for SEO */}
      {totalCount > pageSize && (
        <div className="sr-only">
          <p>
            Page {currentPage} of {Math.ceil(totalCount / pageSize)} 
            ({totalCount} total products)
          </p>
        </div>
      )}
    </>
  )
}

// Generate metadata for better SEO
export function generateProductGridMetadata({
  title,
  description,
  category,
  totalCount,
  currentPage = 1
}: {
  title?: string
  description?: string
  category?: string
  totalCount?: number
  currentPage?: number
}): Metadata {
  const pageTitle = currentPage > 1 
    ? `${title || 'Products'} - Page ${currentPage}`
    : title || 'Products'
    
  const pageDescription = description || 
    `Browse our collection of ${totalCount || ''} ${category || 'products'}. Find the perfect items for your needs.`

  return {
    title: pageTitle,
    description: pageDescription,
    openGraph: {
      title: pageTitle,
      description: pageDescription,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: pageTitle,
      description: pageDescription,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    alternates: {
      canonical: currentPage > 1 
        ? `${process.env.NEXT_PUBLIC_APP_URL}/products?page=${currentPage}`
        : `${process.env.NEXT_PUBLIC_APP_URL}/products`
    }
  }
}
