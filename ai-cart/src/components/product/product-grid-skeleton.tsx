import { Skeleton } from "@/components/ui/skeleton"
import { AspectRatio } from "@/components/ui/aspect-ratio"

interface ProductGridSkeletonProps {
  count?: number
}

export function ProductGridSkeleton({ count = 8 }: ProductGridSkeletonProps) {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <ProductItemSkeleton key={index} />
      ))}
    </>
  )
}

function ProductItemSkeleton() {
  return (
    <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
      {/* Image Skeleton */}
      <AspectRatio ratio={1} className="bg-gray-100">
        <Skeleton className="w-full h-full" />
      </AspectRatio>
      
      {/* Content Skeleton */}
      <div className="p-4 space-y-3">
        {/* Title */}
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </div>
        
        {/* Rating */}
        <div className="flex items-center gap-1">
          <Skeleton className="h-3 w-16" />
          <Skeleton className="h-3 w-8" />
        </div>
        
        {/* Price */}
        <div className="flex items-center gap-2">
          <Skeleton className="h-6 w-20" />
          <Skeleton className="h-4 w-16" />
        </div>
        
        {/* SKU */}
        <Skeleton className="h-3 w-24" />
        
        {/* Button */}
        <Skeleton className="h-9 w-full" />
      </div>
    </div>
  )
}
