// import { siteConfig } from "@/config/site"
import { CartSheet } from "@/components/checkout/cart-sheet"
import { MainNav } from "@/components/layouts/main-nav"
import { MobileNav } from "@/components/layouts/mobile-nav"
import { ProductsCombobox } from "@/components/products-combobox"
import { SiteTopHeader } from "@/components/layouts/site-top-header"
import { AuthDropdown } from "@/components/layouts/auth-dropdown"
import { NotificationsDropdown } from "@/components/layouts/notifications-dropdown"
import { WishlistSheet } from "@/components/wishlist/wishlist-sheet"
import { SimpleCompareButton } from "@/components/compare/simple-compare-button"
import { fetchNavigation } from "@/lib/actions/navigation-menu"
import { auth } from '~/auth'

export async function SiteHeader() {

  const session = await auth()
  const navigationMenu = await fetchNavigation()

  return (
    <>
    <SiteTopHeader />
    <header className="sticky top-0 z-50 w-full border-b bg-background">
      <div >
        <div className="container flex h-16 items-center">
          <MainNav items={navigationMenu} />
          <MobileNav items={navigationMenu} />
          <div className="flex flex-1 items-center justify-end space-x-4">
            <nav className="flex items-center space-x-2">
              <ProductsCombobox />
              <WishlistSheet />
              <SimpleCompareButton />
              <CartSheet />
              {!!session?.user?.id && <NotificationsDropdown /> }
              <AuthDropdown />
            </nav>
          </div>
        </div>
      </div>
    </header>
    </>
  )
}
