"use client"

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Heart, ShoppingCart, X } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useWishlistContext } from "@/components/wishlist/wishlist-context";
import { addToCart } from "@/lib/actions/cart";
import { toast } from "sonner";
import Image from "next/image";
import Link from "next/link";

export default function WishlistDropdown() {
  const { data: session } = useSession();
  const { wishlistCount, wishlistItems, removeFromWishlistAction } = useWishlistContext();
  // No need for local loading state, using context data

  const handleRemoveFromWishlist = async (productId: number) => {
    try {
      const result = await removeFromWishlistAction(productId);
      if (!result.success) {
        toast.error(result.error || "Failed to remove item");
        return;
      }
      toast.success("Removed from wishlist");
    } catch (error) {
      toast.error("Failed to remove item");
    }
  };

  const handleAddToCart = async (item: any) => {
    try {
      const { error } = await addToCart(item.product.id, 1, 1);
      if (error) {
        toast.error(error);
        return;
      }
      toast.success("Added to cart");
    } catch (error) {
      toast.error("Failed to add to cart");
    }
  };

  if (!session?.user?.id) {
    return null;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Heart className="h-5 w-5" />
          {wishlistCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {wishlistCount > 99 ? '99+' : wishlistCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Wishlist ({wishlistCount})</span>
          {wishlistCount > 0 && (
            <Button variant="ghost" size="sm" asChild>
              <Link href="/account/wishlist">View All</Link>
            </Button>
          )}
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        {wishlistItems.length === 0 ? (
          <div className="p-4 text-center text-muted-foreground">
            <Heart className="mx-auto h-8 w-8 mb-2 opacity-50" />
            <p className="text-sm">Your wishlist is empty</p>
          </div>
        ) : (
          <>
            {wishlistItems.map((item) => (
              <DropdownMenuItem key={item.id} className="p-0">
                <div className="flex items-center gap-3 p-3 w-full">
                  {/* Product Image */}
                  <div className="relative h-12 w-12 flex-shrink-0 overflow-hidden rounded">
                    {item.product.image ? (
                      <Image
                        src={item.product.image}
                        alt={item.product.name}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="flex h-full items-center justify-center bg-muted">
                        <span className="text-xs text-muted-foreground">No image</span>
                      </div>
                    )}
                  </div>
                  
                  {/* Product Info */}
                  <div className="flex-1 min-w-0">
                    <Link 
                      href={`/products/view/${item.product.slug}`}
                      className="block"
                    >
                      <p className="text-sm font-medium line-clamp-1 hover:underline">
                        {item.product.name}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        ${item.product.price?.toFixed(2)}
                      </p>
                    </Link>
                  </div>
                  
                  {/* Action Buttons */}
                  <div className="flex flex-col gap-1">
                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-6 w-6"
                      onClick={(e) => {
                        e.preventDefault();
                        handleAddToCart(item);
                      }}
                    >
                      <ShoppingCart className="h-3 w-3" />
                    </Button>
                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-6 w-6"
                      onClick={(e) => {
                        e.preventDefault();
                        handleRemoveFromWishlist(item.product.id);
                      }}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </DropdownMenuItem>
            ))}
            
            {wishlistCount > 5 && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/account/wishlist" className="text-center">
                    View {wishlistCount - 5} more items
                  </Link>
                </DropdownMenuItem>
              </>
            )}
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
