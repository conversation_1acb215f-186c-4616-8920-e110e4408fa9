import { Product } from '@/types/product';
import { HostShopData } from '@/lib/db/store/shop-query';

interface ProductListSchemaProps {
  products: Product[];
  shop: HostShopData;
  totalCount: number;
  searchQuery?: string;
  category?: string;
}

export default function ProductListSchema({ 
  products, 
  shop, 
  totalCount, 
  searchQuery, 
  category 
}: ProductListSchemaProps) {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://localhost:3000';
  
  // Generate structured data for the product list
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    "name": searchQuery 
      ? `Search results for "${searchQuery}"` 
      : category 
        ? `${category} Products`
        : "Products",
    "description": `Browse our collection of ${totalCount} products${searchQuery ? ` matching "${searchQuery}"` : ''}`,
    "numberOfItems": totalCount,
    "itemListElement": products.slice(0, 20).map((product, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "item": {
        "@type": "Product",
        "@id": `${baseUrl}/product/${product.slug}`,
        "name": product.name,
        "description": product.description || `${product.name} - High quality product`,
        "image": product.images?.[0]?.original_url || '',
        "url": `${baseUrl}/product/${product.slug}`,
        "sku": product.variants?.[0]?.sku || '',
        "brand": {
          "@type": "Brand",
          "name": product.brand || shop.name
        },
        "offers": {
          "@type": "AggregateOffer",
          "priceCurrency": "USD",
          "lowPrice": product.variants?.length > 0
            ? Math.min(...product.variants.map(v => v.price.price / 100)).toFixed(2)
            : "0",
          "highPrice": product.variants?.length > 0
            ? Math.max(...product.variants.map(v => v.price.price / 100)).toFixed(2)
            : "0",
          "offerCount": product.variants?.length || 1,
          "availability": "https://schema.org/InStock",
          "seller": {
            "@type": "Organization",
            "name": shop.name
          }
        },
        "aggregateRating": product.rating ? {
          "@type": "AggregateRating",
          "ratingValue": product.rating,
          "reviewCount": 1,
          "bestRating": 5,
          "worstRating": 1
        } : undefined
      }
    })),
    "mainEntity": {
      "@type": "WebPage",
      "@id": `${baseUrl}/products`,
      "name": "Products",
      "description": `Shop ${shop.name} - Browse our extensive collection of products`,
      "url": `${baseUrl}/products`,
      "isPartOf": {
        "@type": "WebSite",
        "@id": `${baseUrl}`,
        "name": shop.name,
        "url": baseUrl
      }
    }
  };

  // Add breadcrumb structured data
  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": baseUrl
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "Products",
        "item": `${baseUrl}/products`
      }
    ]
  };

  // Add search action if it's a search page
  const searchActionData = searchQuery ? {
    "@context": "https://schema.org",
    "@type": "SearchAction",
    "target": {
      "@type": "EntryPoint",
      "urlTemplate": `${baseUrl}/products?q={search_term_string}`
    },
    "query-input": "required name=search_term_string"
  } : null;

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbData)
        }}
      />
      {searchActionData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(searchActionData)
          }}
        />
      )}
    </>
  );
}
