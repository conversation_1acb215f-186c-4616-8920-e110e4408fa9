"use client"

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { getWishlistCount, getWishlist, addToWishlist, removeFromWishlist } from '@/lib/actions/wishlist'
import { toast } from 'sonner'

type WishlistContextType = {
  wishlistCount: number
  wishlistItems: any[]
  loading: boolean
  refreshWishlist: () => Promise<void>
  updateWishlistCount: (count: number) => void
  removeWishlistItem: (productId: number) => void
  addToWishlistAction: (variantId: number) => Promise<{ success: boolean; error?: string }>
  removeFromWishlistAction: (variantId: number) => Promise<{ success: boolean; error?: string }>
  isInWishlist: (variantId: number) => boolean
}

const WishlistContext = createContext<WishlistContextType | undefined>(undefined)

export const WishlistProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { data: session } = useSession()
  const [wishlistCount, setWishlistCount] = useState(0)
  const [wishlistItems, setWishlistItems] = useState<any[]>([])
  const [loading, setLoading] = useState(false)

  console.log('🔄 WishlistProvider render. Session:', session?.user?.id || 'No session', 'Client:', typeof window !== 'undefined')



  const refreshWishlist = useCallback(async () => {
    // Only run on client side
    if (typeof window === 'undefined') {
      console.log('🔄 refreshWishlist called on server, skipping')
      return
    }

    console.log('🔄 refreshWishlist called. Session:', session?.user?.id)

    if (!session?.user?.id) {
      console.log('🔄 No user session, clearing wishlist')
      setWishlistCount(0)
      setWishlistItems([])
      return
    }

    try {
      console.log('🔄 Refreshing wishlist for user:', session.user.id)
      setLoading(true)

      console.log('🔄 Calling getWishlistCount...')
      const count = await getWishlistCount()
      console.log('🔄 getWishlistCount result:', count)

      console.log('🔄 Calling getWishlist...')
      const items = await getWishlist(0, 50) // Get all items
      console.log('🔄 getWishlist result:', items)

      console.log('✅ Wishlist refreshed - Count:', count, 'Items:', items.items?.length || 0)
      setWishlistCount(count)
      setWishlistItems(items.items || [])
    } catch (error) {
      console.error('❌ Failed to load wishlist data:', error)
      setWishlistCount(0)
      setWishlistItems([])
    } finally {
      setLoading(false)
    }
  }, [session?.user?.id])

  const updateWishlistCount = (count: number) => {
    setWishlistCount(count)
  }

  const removeWishlistItem = (variantId: number) => {
    setWishlistItems(prev => prev.filter(item => item.variant.id !== variantId))
    setWishlistCount(prev => Math.max(0, prev - 1))
  }

  const addToWishlistAction = async (variantId: number): Promise<{ success: boolean; error?: string }> => {
    if (!session?.user?.id) {
      return { success: false, error: 'Please sign in to add items to your wishlist' }
    }

    try {
      console.log('🔄 Adding to wishlist:', variantId, 'Current count:', wishlistCount)
      const { error } = await addToWishlist(variantId)
      if (error) {
        return { success: false, error }
      }

      // Update local state immediately
      setWishlistCount(prev => {
        const newCount = prev + 1
        console.log('✅ Updated wishlist count:', prev, '->', newCount)
        return newCount
      })

      // Refresh full wishlist data in background
      refreshWishlist()

      return { success: true }
    } catch (error) {
      return { success: false, error: 'Something went wrong' }
    }
  }

  const removeFromWishlistAction = async (variantId: number): Promise<{ success: boolean; error?: string }> => {
    if (!session?.user?.id) {
      return { success: false, error: 'Please sign in to manage your wishlist' }
    }

    try {
      console.log('🔄 Removing from wishlist:', variantId, 'Current count:', wishlistCount)
      const { error } = await removeFromWishlist(variantId)
      if (error) {
        return { success: false, error }
      }

      // Update local state immediately
      removeWishlistItem(variantId)
      console.log('✅ Removed from wishlist, new count should be:', wishlistCount - 1)

      // Refresh full wishlist data in background
      refreshWishlist()

      return { success: true }
    } catch (error) {
      return { success: false, error: 'Something went wrong' }
    }
  }

  const isInWishlist = (variantId: number): boolean => {
    return wishlistItems.some(item => item.variant.id === variantId)
  }

  // Load wishlist when session changes (client-side only)
  useEffect(() => {
    console.log('🔄 useEffect triggered. Client:', typeof window !== 'undefined', 'Session:', session?.user?.id)
    if (typeof window !== 'undefined') {
      refreshWishlist()
    }
  }, [session?.user?.id, refreshWishlist])

  return (
    <WishlistContext.Provider value={{
      wishlistCount,
      wishlistItems,
      loading,
      refreshWishlist,
      updateWishlistCount,
      removeWishlistItem,
      addToWishlistAction,
      removeFromWishlistAction,
      isInWishlist
    }}>
      {children}
    </WishlistContext.Provider>
  )
}

export const useWishlistContext = () => {
  const context = useContext(WishlistContext)
  if (context === undefined) {
    throw new Error('useWishlistContext must be used within a WishlistProvider')
  }
  return context
}
