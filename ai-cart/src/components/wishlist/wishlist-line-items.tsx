"use client"

import Image from "next/image"
import Link from "next/link"
import { useState } from "react"
import { Heart, ShoppingCart, Trash2 } from "lucide-react"
import { toast } from "sonner"

import { cn, formatPrice } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { addToCart } from "@/lib/actions/cart"
import { useWishlistContext } from "@/components/wishlist/wishlist-context"

interface WishlistLineItemsProps extends React.HTMLAttributes<HTMLDivElement> {
  items: any[]
  onItemRemoved?: (variantId: number) => void
  loading?: boolean
}

export function WishlistLineItems({
  items,
  className,
  onItemRemoved,
  loading = false,
  ...props
}: WishlistLineItemsProps) {
  const [removingItems, setRemovingItems] = useState<Set<number>>(new Set())
  const [addingToCart, setAddingToCart] = useState<Set<number>>(new Set())
  const { removeFromWishlistAction } = useWishlistContext()

  const handleRemoveFromWishlist = async (variantId: number) => {
    try {
      setRemovingItems(prev => new Set(prev).add(variantId))

      console.log('🔄 Removing from wishlist (line-items):', variantId)
      const result = await removeFromWishlistAction(variantId)
      console.log('🔄 Remove result (line-items):', result)

      if (!result.success) {
        console.error('❌ Remove failed (line-items):', result.error)
        toast.error(result.error || "Failed to remove from wishlist")
      } else {
        console.log('✅ Remove successful (line-items)')
        onItemRemoved?.(variantId)
        toast.success("Removed from wishlist")
      }
    } catch (error) {
      console.error('❌ Error removing from wishlist (line-items):', error)
      toast.error("Failed to remove from wishlist")
    } finally {
      setRemovingItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(variantId)
        return newSet
      })
    }
  }

  const handleAddToCart = async (item: WishlistItem) => {
    try {
      setAddingToCart(prev => new Set(prev).add(item.variant.id))

      const result = await addToCart(item.variant.id, null, 1)

      if (result.success) {
        toast.success("Added to cart")
      } else {
        toast.error(result.message || "Failed to add to cart")
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      toast.error("Failed to add to cart")
    } finally {
      setAddingToCart(prev => {
        const newSet = new Set(prev)
        newSet.delete(item.variant.id)
        return newSet
      })
    }
  }

  if (loading) {
    return (
      <div className={cn("flex flex-col space-y-4 pr-6", className)} {...props}>
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="flex items-center space-x-4">
            <div className="size-16 animate-pulse rounded bg-muted" />
            <div className="flex-1 space-y-2">
              <div className="h-4 animate-pulse rounded bg-muted" />
              <div className="h-3 w-20 animate-pulse rounded bg-muted" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <ScrollArea className={cn("flex-1", className)} {...props}>
      <div className="flex flex-col gap-3 pr-6">
        {items.map((item) => {
          const product = item.product
          const variant = item.variant
          const isRemoving = removingItems.has(variant.id)
          const isAddingToCart = addingToCart.has(variant.id)
          
          return (
            <div key={item.id} className="space-y-3">
              <div className="flex items-start justify-between gap-4">
                <div className="flex items-center space-x-4">
                  <div className="relative aspect-square size-16 min-w-fit overflow-hidden rounded">
                    {product.images?.[0]?.medium_url ? (
                      <Image
                        src={product.images[0].medium_url}
                        alt={product.name}
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                        fill
                        className="absolute object-cover"
                        loading="lazy"
                      />
                    ) : (
                      <div className="flex h-full items-center justify-center bg-secondary">
                        <Heart className="size-4 text-muted-foreground" />
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col space-y-1 self-start">
                    <Link
                      aria-label={`View ${product.name}`}
                      href={`/products/view/${product.slug}`}
                      className="line-clamp-1 text-sm font-medium transition-colors hover:text-muted-foreground"
                    >
                      {product.name}
                    </Link>
                    <span className="line-clamp-1 text-xs capitalize text-muted-foreground">
                      {product.category?.name}
                    </span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">
                        {formatPrice(
                          variant.price?.sell_price || variant.price?.price || 0
                        )}
                      </span>
                      {variant.price?.compare_price &&
                       variant.price.compare_price > (variant.price?.sell_price || variant.price?.price || 0) && (
                        <span className="text-xs text-muted-foreground line-through">
                          {formatPrice(variant.price.compare_price)}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex flex-col gap-2">
                  <Button
                    aria-label="Remove from wishlist"
                    size="icon"
                    variant="outline"
                    className="size-8"
                    onClick={() => handleRemoveFromWishlist(variant.id)}
                    disabled={isRemoving}
                  >
                    {isRemoving ? (
                      <div className="size-3 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    ) : (
                      <Trash2 className="size-3" />
                    )}
                  </Button>
                  <Button
                    aria-label="Add to cart"
                    size="icon"
                    variant="outline"
                    className="size-8"
                    onClick={() => handleAddToCart(item)}
                    disabled={isAddingToCart}
                  >
                    {isAddingToCart ? (
                      <div className="size-3 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    ) : (
                      <ShoppingCart className="size-3" />
                    )}
                  </Button>
                </div>
              </div>
              <Separator />
            </div>
          )
        })}
      </div>
    </ScrollArea>
  )
}
