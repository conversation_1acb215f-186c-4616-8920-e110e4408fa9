"use client"

import Link from "next/link"
import { useSession } from "next-auth/react"
import { Heart } from "lucide-react"

import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { But<PERSON>, buttonVariants } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet"
import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { WishlistLineItems } from "@/components/wishlist/wishlist-line-items"
import { useWishlistContext } from "@/components/wishlist/wishlist-context"

export function WishlistSheet() {
  const { data: session } = useSession()
  const { wishlistCount, wishlistItems, loading, removeWishlistItem } = useWishlistContext()

  // Debug logging
  console.log('🛒 WishlistSheet render - Count:', wishlistCount, 'Items:', wishlistItems.length)

  // Don't show wishlist button if user is not authenticated
  if (!session?.user?.id) {
    return null
  }

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button
          aria-label="Open wishlist"
          variant="outline"
          size="icon"
          className="relative"
        >
          {wishlistCount > 0 && (
            <Badge
              variant="secondary"
              className="absolute -right-2 -top-2 size-6 justify-center rounded-full p-2.5"
            >
              {wishlistCount > 99 ? '99+' : wishlistCount}
            </Badge>
          )}
          <Heart className="size-4" aria-hidden="true" />
        </Button>
      </SheetTrigger>
      <SheetContent className="flex w-full flex-col pr-0 sm:max-w-lg">
        <SheetHeader className="space-y-2.5 pr-6">
          <SheetTitle>Wishlist {wishlistCount > 0 && `(${wishlistCount})`}</SheetTitle>
          <Separator />
        </SheetHeader>
        <VisuallyHidden>
          <SheetDescription>
            Wishlist products open
          </SheetDescription>
        </VisuallyHidden>

        {wishlistCount > 0 ? (
          <>
            <WishlistLineItems
              items={wishlistItems}
              className="flex-1"
              onItemRemoved={removeWishlistItem}
              loading={loading}
            />
            <div className="space-y-4 pr-6">
              <Separator />
              <div className="space-y-1.5 text-sm">
                <div className="flex">
                  <span className="flex-1">Total Items</span>
                  <span>{wishlistCount}</span>
                </div>
              </div>
              <SheetFooter>
                <SheetTrigger asChild>
                  <Link
                    aria-label="View your wishlist"
                    href="/account/wishlist"
                    className={buttonVariants({
                      size: "sm",
                      className: "w-full",
                    })}
                  >
                    View All Wishlist Items
                  </Link>
                </SheetTrigger>
              </SheetFooter>
            </div>
          </>
        ) : (
          <div className="flex h-full flex-col items-center justify-center space-y-1">
            <Heart
              className="mb-4 size-16 text-muted-foreground"
              aria-hidden="true"
            />
            <div className="text-xl font-medium text-muted-foreground">
              Your wishlist is empty
            </div>
            <SheetTrigger asChild>
              <Link
                aria-label="Add items to your wishlist"
                href="/products"
                className={cn(
                  buttonVariants({
                    variant: "link",
                    size: "sm",
                    className: "text-sm text-muted-foreground",
                  })
                )}
              >
                Discover products to add to your wishlist
              </Link>
            </SheetTrigger>
          </div>
        )}
      </SheetContent>
    </Sheet>
  )
}
