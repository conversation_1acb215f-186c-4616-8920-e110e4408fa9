"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { X, ShoppingCart, Loader2, RotateCcw, Trash2, ArrowLeft } from "lucide-react"
import { useCompare } from "@/components/compare/simple-compare-context"
import { getProductsByVariantIds } from "@/lib/actions/compare"
import { addToCart } from "@/lib/actions/cart"
import { toast } from "sonner"
import { formatPrice } from "@/lib/utils"
import Image from "next/image"
import Link from "next/link"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { useRouter } from "next/navigation"

interface CompareProduct {
  id: number
  variant_id: number
  product: {
    id: number
    name: string
    slug: string
    description?: string
    images: Array<{
      id: number
      original_url: string
      medium_url?: string
    }>
  }
  variant: {
    id: number
    sku: string
    price: {
      price: number
      sell_price?: number
      compare_price?: number
    }
    stock?: number
  }
}

// Cache for product data to avoid repeated API calls
const productCache = new Map<string, CompareProduct[]>()

export function SimpleComparePage() {
  const router = useRouter()
  const { getCompareItems, removeFromCompare, clearCompare } = useCompare()
  const [products, setProducts] = useState<CompareProduct[]>([])
  const [loading, setLoading] = useState(true)
  const [mounted, setMounted] = useState(false)
  const [removingItems, setRemovingItems] = useState<Set<number>>(new Set())
  const [addingToCart, setAddingToCart] = useState<Set<number>>(new Set())

  // Handle hydration
  useEffect(() => {
    setMounted(true)
  }, [])

  // Load products data after component is mounted with caching
  useEffect(() => {
    if (!mounted) return

    const loadProducts = async () => {
      try {
        setLoading(true)
        const variantIds = getCompareItems()

        console.log('Loading compare products for variant IDs:', variantIds)

        if (variantIds.length === 0) {
          console.log('No variant IDs found')
          setProducts([])
          return
        }

        // Create cache key from sorted variant IDs
        const cacheKey = variantIds.sort().join(',')

        // Check cache first
        if (productCache.has(cacheKey)) {
          console.log('Using cached compare products')
          setProducts(productCache.get(cacheKey)!)
          return
        }

        console.log('Fetching products from API...')
        const compareItems = await getProductsByVariantIds(variantIds)
        console.log('Received compare items:', compareItems)

        // Cache the results
        productCache.set(cacheKey, compareItems)
        setProducts(compareItems)
      } catch (error) {
        console.error('Error loading compare products:', error)
        toast.error('Failed to load compare products')
      } finally {
        setLoading(false)
      }
    }

    loadProducts()
  }, [mounted, getCompareItems])

  const handleRemove = async (variantId: number) => {
    setRemovingItems(prev => new Set(prev).add(variantId))
    
    try {
      await removeFromCompare(variantId)
      setProducts(prev => prev.filter(p => p.variant.id !== variantId))
    } catch (error) {
      console.error('Error removing item:', error)
    } finally {
      setRemovingItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(variantId)
        return newSet
      })
    }
  }

  const handleAddToCart = async (product: CompareProduct) => {
    setAddingToCart(prev => new Set(prev).add(product.variant.id))
    
    try {
      const result = await addToCart(product.variant.id, null, 1)
      if (result.success) {
        toast.success('Added to cart')
      } else {
        toast.error(result.message || 'Failed to add to cart')
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      toast.error('Failed to add to cart')
    } finally {
      setAddingToCart(prev => {
        const newSet = new Set(prev)
        newSet.delete(product.variant.id)
        return newSet
      })
    }
  }

  const handleClearAll = async () => {
    try {
      await clearCompare()
      setProducts([])
    } catch (error) {
      console.error('Error clearing compare:', error)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  if (products.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
          <RotateCcw className="h-16 w-16 text-muted-foreground mb-4" />
          <h1 className="text-2xl font-bold mb-2">No products to compare</h1>
          <p className="text-muted-foreground mb-6 max-w-md">
            Add products to your compare list to see them here. You can compare up to 4 products at once.
          </p>
          <Button onClick={() => router.push('/products')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Browse Products
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold">Compare Products</h1>
          <p className="text-muted-foreground mt-1">
            Comparing {products.length} product{products.length !== 1 ? 's' : ''}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button 
            variant="outline" 
            onClick={handleClearAll}
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Clear All
          </Button>
        </div>
      </div>

      {/* Mobile View - Cards */}
      <div className="block md:hidden space-y-4">
        {products.map((product) => {
          const mainImage = product.product.images?.[0]
          const price = product.variant.price.sell_price || product.variant.price.price
          const comparePrice = product.variant.price.compare_price
          const hasDiscount = comparePrice && comparePrice > price
          const isOutOfStock = product.variant.stock !== undefined && product.variant.stock <= 0
          const isRemoving = removingItems.has(product.variant.id)
          const isAddingToCart = addingToCart.has(product.variant.id)

          return (
            <Card key={product.variant.id}>
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg line-clamp-2">
                    <Link href={`/products/view/${product.product.slug}`} className="hover:text-blue-600">
                      {product.product.name}
                    </Link>
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemove(product.variant.id)}
                    disabled={isRemoving}
                    className="text-muted-foreground hover:text-destructive"
                  >
                    {isRemoving ? <Loader2 className="h-4 w-4 animate-spin" /> : <X className="h-4 w-4" />}
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Image */}
                <div className="w-full max-w-xs mx-auto">
                  <AspectRatio ratio={1} className="bg-gray-100 rounded-lg overflow-hidden">
                    {mainImage ? (
                      <Image
                        src={mainImage.medium_url || mainImage.original_url}
                        alt={product.product.name}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 100vw, 300px"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-400">No image</span>
                      </div>
                    )}
                  </AspectRatio>
                </div>

                {/* Details */}
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Price</span>
                    <div className="flex items-center gap-2">
                      <span className="text-xl font-bold">{formatPrice(price)}</span>
                      {hasDiscount && (
                        <span className="text-sm text-muted-foreground line-through">
                          {formatPrice(comparePrice)}
                        </span>
                      )}
                    </div>
                  </div>

                  {product.variant.sku && (
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">SKU</span>
                      <p className="text-sm">{product.variant.sku}</p>
                    </div>
                  )}

                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Availability</span>
                    <div>
                      {isOutOfStock ? (
                        <Badge variant="secondary">Out of Stock</Badge>
                      ) : (
                        <Badge variant="default">In Stock</Badge>
                      )}
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <Button
                  onClick={() => handleAddToCart(product)}
                  disabled={isAddingToCart || isOutOfStock}
                  className="w-full"
                >
                  {isAddingToCart ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Adding...
                    </>
                  ) : (
                    <>
                      <ShoppingCart className="mr-2 h-4 w-4" />
                      Add to Cart
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Desktop View - Table */}
      <div className="hidden md:block">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-32">Product</TableHead>
                {products.map((product) => (
                  <TableHead key={product.variant.id} className="text-center min-w-[250px]">
                    <div className="flex items-center justify-between">
                      <span className="truncate">{product.product.name}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemove(product.variant.id)}
                        disabled={removingItems.has(product.variant.id)}
                        className="text-muted-foreground hover:text-destructive ml-2"
                      >
                        {removingItems.has(product.variant.id) ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <X className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {/* Product Images */}
              <TableRow>
                <TableCell className="font-medium">Image</TableCell>
                {products.map((product) => {
                  const mainImage = product.product.images?.[0]
                  return (
                    <TableCell key={product.variant.id} className="text-center">
                      <div className="w-32 h-32 mx-auto">
                        <AspectRatio ratio={1} className="bg-gray-100 rounded-lg overflow-hidden">
                          {mainImage ? (
                            <Link href={`/products/view/${product.product.slug}`}>
                              <Image
                                src={mainImage.medium_url || mainImage.original_url}
                                alt={product.product.name}
                                fill
                                className="object-cover hover:scale-105 transition-transform"
                                sizes="128px"
                              />
                            </Link>
                          ) : (
                            <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                              <span className="text-gray-400 text-xs">No image</span>
                            </div>
                          )}
                        </AspectRatio>
                      </div>
                    </TableCell>
                  )
                })}
              </TableRow>

              {/* Price */}
              <TableRow>
                <TableCell className="font-medium">Price</TableCell>
                {products.map((product) => {
                  const price = product.variant.price.sell_price || product.variant.price.price
                  const comparePrice = product.variant.price.compare_price
                  const hasDiscount = comparePrice && comparePrice > price
                  
                  return (
                    <TableCell key={product.variant.id} className="text-center">
                      <div className="space-y-1">
                        <div className="text-xl font-bold">{formatPrice(price)}</div>
                        {hasDiscount && (
                          <div className="text-sm text-muted-foreground line-through">
                            {formatPrice(comparePrice)}
                          </div>
                        )}
                      </div>
                    </TableCell>
                  )
                })}
              </TableRow>

              {/* SKU */}
              <TableRow>
                <TableCell className="font-medium">SKU</TableCell>
                {products.map((product) => (
                  <TableCell key={product.variant.id} className="text-center">
                    {product.variant.sku || '-'}
                  </TableCell>
                ))}
              </TableRow>

              {/* Availability */}
              <TableRow>
                <TableCell className="font-medium">Availability</TableCell>
                {products.map((product) => {
                  const isOutOfStock = product.variant.stock !== undefined && product.variant.stock <= 0
                  return (
                    <TableCell key={product.variant.id} className="text-center">
                      {isOutOfStock ? (
                        <Badge variant="secondary">Out of Stock</Badge>
                      ) : (
                        <Badge variant="default">In Stock</Badge>
                      )}
                    </TableCell>
                  )
                })}
              </TableRow>

              {/* Actions */}
              <TableRow>
                <TableCell className="font-medium">Actions</TableCell>
                {products.map((product) => {
                  const isOutOfStock = product.variant.stock !== undefined && product.variant.stock <= 0
                  const isAddingToCart = addingToCart.has(product.variant.id)
                  
                  return (
                    <TableCell key={product.variant.id} className="text-center">
                      <div className="space-y-2">
                        <Button
                          onClick={() => handleAddToCart(product)}
                          disabled={isAddingToCart || isOutOfStock}
                          className="w-full"
                          size="sm"
                        >
                          {isAddingToCart ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Adding...
                            </>
                          ) : (
                            <>
                              <ShoppingCart className="mr-2 h-4 w-4" />
                              Add to Cart
                            </>
                          )}
                        </Button>
                        <Link href={`/products/view/${product.product.slug}`}>
                          <Button variant="outline" size="sm" className="w-full">
                            View Details
                          </Button>
                        </Link>
                      </div>
                    </TableCell>
                  )
                })}
              </TableRow>
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  )
}
