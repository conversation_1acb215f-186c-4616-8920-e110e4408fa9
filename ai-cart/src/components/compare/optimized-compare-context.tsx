"use client"

import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react'
import { compareStorage } from '@/lib/storage/compare-storage'
import { getProductsByVariantIds } from '@/lib/actions/compare'
import { toast } from 'sonner'

interface CompareProduct {
  id: number
  variant_id: number
  variant: {
    id: number
    sku: string
    price: {
      price: number
      sell_price?: number
      compare_price?: number
    }
    stock?: number
  }
  product: {
    id: number
    name: string
    slug: string
    description?: string
    images: Array<{
      id: number
      original_url: string
      medium_url?: string
    }>
  }
}

interface OptimizedCompareContextType {
  compareCount: number
  isInCompare: (variantId: number) => boolean
  addToCompare: (variantId: number) => Promise<{ success: boolean; error?: string }>
  removeFromCompare: (variantId: number) => Promise<{ success: boolean; error?: string }>
  clearCompare: () => Promise<{ success: boolean; error?: string }>
  getCompareItems: () => number[]
  getCompareProducts: () => Promise<CompareProduct[]>
  isLoading: boolean
  products: CompareProduct[]
}

const OptimizedCompareContext = createContext<OptimizedCompareContextType | undefined>(undefined)

export function useOptimizedCompare() {
  const context = useContext(OptimizedCompareContext)
  if (context === undefined) {
    throw new Error('useOptimizedCompare must be used within an OptimizedCompareProvider')
  }
  return context
}

// Cache for product data
const productCache = new Map<string, CompareProduct[]>()
const loadingPromises = new Map<string, Promise<CompareProduct[]>>()

export function OptimizedCompareProvider({ children }: { children: React.ReactNode }) {
  const [compareCount, setCompareCount] = useState(0)
  const [mounted, setMounted] = useState(false)
  const [products, setProducts] = useState<CompareProduct[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const backgroundLoadRef = useRef<NodeJS.Timeout>()

  // Handle hydration
  useEffect(() => {
    setMounted(true)
  }, [])

  // Load initial count from localStorage after hydration
  useEffect(() => {
    if (!mounted) return

    const count = compareStorage.getCount()
    const variantIds = compareStorage.getVariantIds()
    console.log('OptimizedCompare - Initial load:', { count, variantIds })
    setCompareCount(count)

    // Start background loading if there are items
    if (count > 0) {
      scheduleBackgroundLoad()
    }
  }, [mounted])

  // Schedule background loading after page is fully loaded
  const scheduleBackgroundLoad = useCallback(() => {
    // Clear any existing timeout
    if (backgroundLoadRef.current) {
      clearTimeout(backgroundLoadRef.current)
    }
    
    // Schedule loading after a short delay to ensure page is loaded
    backgroundLoadRef.current = setTimeout(() => {
      loadProductsInBackground()
    }, 100) // Small delay to let page finish loading
  }, [])

  // Background product loading
  const loadProductsInBackground = useCallback(async () => {
    if (!mounted) return

    const variantIds = compareStorage.getVariantIds()
    console.log('Background loading - variantIds:', variantIds)

    if (variantIds.length === 0) {
      console.log('Background loading - no variants, clearing products')
      setProducts([])
      setIsLoading(false)
      return
    }

    const cacheKey = variantIds.sort().join(',')
    console.log('Background loading - cacheKey:', cacheKey)

    // Check cache first
    if (productCache.has(cacheKey)) {
      console.log('Background loading - using cached products')
      setProducts(productCache.get(cacheKey)!)
      setIsLoading(false)
      return
    }

    // Check if already loading
    if (loadingPromises.has(cacheKey)) {
      try {
        const cachedProducts = await loadingPromises.get(cacheKey)!
        setProducts(cachedProducts)
        setIsLoading(false)
      } catch (error) {
        console.error('Error loading cached products:', error)
        setIsLoading(false)
      }
      return
    }

    // Start loading
    console.log('Background loading - starting API call')
    setIsLoading(true)
    const loadingPromise = getProductsByVariantIds(variantIds)
    loadingPromises.set(cacheKey, loadingPromise)

    try {
      const compareProducts = await loadingPromise
      console.log('Background loading - API response:', compareProducts.length, 'products')

      // Cache the results
      productCache.set(cacheKey, compareProducts)
      setProducts(compareProducts)

      console.log('Background loaded compare products:', compareProducts.length)
    } catch (error) {
      console.error('Error loading compare products in background:', error)
    } finally {
      setIsLoading(false)
      loadingPromises.delete(cacheKey)
    }
  }, [mounted])

  // Check if item is in compare
  const isInCompare = useCallback((variantId: number): boolean => {
    if (!mounted) return false
    return compareStorage.hasItem(variantId)
  }, [mounted])

  // Add item to compare
  const addToCompare = useCallback(async (variantId: number): Promise<{ success: boolean; error?: string }> => {
    try {
      const result = compareStorage.addItem(variantId)
      
      if (result.success) {
        setCompareCount(prev => prev + 1)
        toast.success('Added to compare')
        
        // Schedule background load for new product
        scheduleBackgroundLoad()
      } else {
        toast.error(result.error || 'Failed to add to compare')
      }
      
      return result
    } catch (error) {
      const errorMessage = 'Failed to add to compare'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    }
  }, [scheduleBackgroundLoad])

  // Remove item from compare
  const removeFromCompare = useCallback(async (variantId: number): Promise<{ success: boolean; error?: string }> => {
    try {
      const result = compareStorage.removeItem(variantId)
      
      if (result.success) {
        setCompareCount(prev => Math.max(0, prev - 1))
        toast.success('Removed from compare')
        
        // Update products state by removing the item
        setProducts(prev => prev.filter(p => p.variant.id !== variantId))
        
        // Clear cache since items changed
        productCache.clear()
        loadingPromises.clear()
      } else {
        toast.error(result.error || 'Failed to remove from compare')
      }
      
      return result
    } catch (error) {
      const errorMessage = 'Failed to remove from compare'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    }
  }, [])

  // Clear all compare items
  const clearCompare = useCallback(async (): Promise<{ success: boolean; error?: string }> => {
    try {
      const result = compareStorage.clearAll()
      
      if (result.success) {
        setCompareCount(0)
        setProducts([])
        toast.success('Compare list cleared')
        
        // Clear cache
        productCache.clear()
        loadingPromises.clear()
      } else {
        toast.error('Failed to clear compare list')
      }
      
      return result
    } catch (error) {
      const errorMessage = 'Failed to clear compare list'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    }
  }, [])

  // Get all compare items (variant IDs)
  const getCompareItems = useCallback((): number[] => {
    if (!mounted) return []
    return compareStorage.getVariantIds()
  }, [mounted])

  // Get compare products without setting loading state (to avoid render issues)
  const getCompareProducts = useCallback(async (): Promise<CompareProduct[]> => {
    if (!mounted) return []

    const variantIds = compareStorage.getVariantIds()
    if (variantIds.length === 0) return []

    const cacheKey = variantIds.sort().join(',')

    // Return cached if available
    if (productCache.has(cacheKey)) {
      return productCache.get(cacheKey)!
    }

    // If already loading, wait for it
    if (loadingPromises.has(cacheKey)) {
      return await loadingPromises.get(cacheKey)!
    }

    // Start loading without setting loading state (to avoid render issues)
    const loadingPromise = getProductsByVariantIds(variantIds)
    loadingPromises.set(cacheKey, loadingPromise)

    try {
      const compareProducts = await loadingPromise
      productCache.set(cacheKey, compareProducts)
      setProducts(compareProducts)
      return compareProducts
    } catch (error) {
      console.error('Error loading compare products:', error)
      return []
    } finally {
      loadingPromises.delete(cacheKey)
    }
  }, [mounted])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (backgroundLoadRef.current) {
        clearTimeout(backgroundLoadRef.current)
      }
    }
  }, [])

  const value: OptimizedCompareContextType = {
    compareCount,
    isInCompare,
    addToCompare,
    removeFromCompare,
    clearCompare,
    getCompareItems,
    getCompareProducts,
    isLoading,
    products
  }

  return (
    <OptimizedCompareContext.Provider value={value}>
      {children}
    </OptimizedCompareContext.Provider>
  )
}
