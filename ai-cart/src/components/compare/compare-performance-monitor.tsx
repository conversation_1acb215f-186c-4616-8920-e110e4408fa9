"use client"

import { useEffect, useRef } from 'react'
import { useCompareContext } from '@/components/compare/compare-context'

interface PerformanceMetrics {
  compareLoadTime: number
  compareActionTime: number
  memoryUsage: number
  renderTime: number
}

export function ComparePerformanceMonitor() {
  const { compareItems, compareCount } = useCompareContext()
  const metricsRef = useRef<PerformanceMetrics>({
    compareLoadTime: 0,
    compareActionTime: 0,
    memoryUsage: 0,
    renderTime: 0
  })
  const renderStartRef = useRef<number>(0)

  // Monitor render performance
  useEffect(() => {
    renderStartRef.current = performance.now()
    
    return () => {
      const renderTime = performance.now() - renderStartRef.current
      metricsRef.current.renderTime = renderTime
      
      // Log performance metrics in development
      if (process.env.NODE_ENV === 'development') {
        console.log('Compare Performance Metrics:', {
          renderTime: `${renderTime.toFixed(2)}ms`,
          compareCount,
          itemsCount: compareItems.length,
          memoryUsage: getMemoryUsage()
        })
      }
    }
  }, [compareItems, compareCount])

  // Monitor memory usage
  useEffect(() => {
    const interval = setInterval(() => {
      metricsRef.current.memoryUsage = getMemoryUsage()
    }, 5000) // Check every 5 seconds

    return () => clearInterval(interval)
  }, [])

  // Report performance to analytics (if needed)
  useEffect(() => {
    if (typeof window !== 'undefined' && 'gtag' in window) {
      // Report to Google Analytics
      ;(window as any).gtag('event', 'compare_performance', {
        custom_parameter_1: metricsRef.current.renderTime,
        custom_parameter_2: compareCount,
        custom_parameter_3: metricsRef.current.memoryUsage
      })
    }
  }, [compareCount])

  return null // This is a monitoring component, no UI
}

function getMemoryUsage(): number {
  if (typeof window !== 'undefined' && 'performance' in window && 'memory' in (window.performance as any)) {
    return (window.performance as any).memory.usedJSHeapSize / 1024 / 1024 // MB
  }
  return 0
}

// Hook for measuring compare action performance
export function useCompareActionPerformance() {
  const measureAction = async <T,>(
    actionName: string,
    action: () => Promise<T>
  ): Promise<T> => {
    const startTime = performance.now()
    
    try {
      const result = await action()
      const endTime = performance.now()
      const duration = endTime - startTime
      
      // Log performance in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`Compare Action "${actionName}" took ${duration.toFixed(2)}ms`)
      }
      
      // Report to analytics if available
      if (typeof window !== 'undefined' && 'gtag' in window) {
        ;(window as any).gtag('event', 'compare_action_performance', {
          action_name: actionName,
          duration: Math.round(duration),
          success: true
        })
      }
      
      return result
    } catch (error) {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      // Report error to analytics
      if (typeof window !== 'undefined' && 'gtag' in window) {
        ;(window as any).gtag('event', 'compare_action_performance', {
          action_name: actionName,
          duration: Math.round(duration),
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
      
      throw error
    }
  }

  return { measureAction }
}
