import { Skeleton } from "@/components/ui/skeleton"
import { AspectRatio } from "@/components/ui/aspect-ratio"

export function ComparePageSkeleton() {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="text-center space-y-4">
        <Skeleton className="h-8 w-64 mx-auto" />
        <Skeleton className="h-4 w-96 mx-auto" />
      </div>

      {/* Compare Table Skeleton */}
      <div className="overflow-x-auto">
        <div className="min-w-full">
          {/* Product Images Row */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="space-y-4">
                <AspectRatio ratio={1} className="bg-gray-100">
                  <Skeleton className="w-full h-full" />
                </AspectRatio>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-6 w-20" />
                </div>
              </div>
            ))}
          </div>

          {/* Comparison Rows */}
          <div className="space-y-4">
            {Array.from({ length: 8 }).map((_, rowIndex) => (
              <div key={rowIndex} className="grid grid-cols-1 md:grid-cols-4 gap-4 py-3 border-b border-gray-200">
                <div className="font-medium">
                  <Skeleton className="h-4 w-24" />
                </div>
                {Array.from({ length: 3 }).map((_, colIndex) => (
                  <div key={colIndex}>
                    <Skeleton className="h-4 w-full" />
                  </div>
                ))}
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-8 w-full" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export function CompareItemSkeleton() {
  return (
    <div className="space-y-4">
      <AspectRatio ratio={1} className="bg-gray-100">
        <Skeleton className="w-full h-full" />
      </AspectRatio>
      <div className="space-y-2">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-6 w-20" />
      </div>
      <div className="space-y-2">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-8 w-full" />
      </div>
    </div>
  )
}

export function CompareEmptySkeleton() {
  return (
    <div className="text-center py-12 space-y-4">
      <Skeleton className="h-16 w-16 rounded-full mx-auto" />
      <Skeleton className="h-6 w-48 mx-auto" />
      <Skeleton className="h-4 w-64 mx-auto" />
      <Skeleton className="h-10 w-32 mx-auto" />
    </div>
  )
}
