"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>rig<PERSON> } from "@/components/ui/sheet"
import { Badge } from "@/components/ui/badge"
import { RotateCcw } from "lucide-react"
import { useCompareContext } from "@/components/compare/compare-context"
import { CompareLineItems } from "@/components/compare/compare-line-items"

export function CompareHeaderButton() {
  const [open, setOpen] = useState(false)
  const { compareCount, compareItems } = useCompareContext()

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <RotateCcw className="h-5 w-5" />
          <span className="sr-only">Compare</span>
          {compareCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {compareCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent className="w-full sm:max-w-lg">
        <SheetHeader>
          <SheetTitle>Compare Products ({compareCount})</SheetTitle>
        </SheetHeader>
        <div className="mt-6 h-full">
          <CompareLineItems 
            items={compareItems}
            onItemRemoved={() => {
              // The context will handle the state update
            }}
          />
        </div>
      </SheetContent>
    </Sheet>
  )
}
