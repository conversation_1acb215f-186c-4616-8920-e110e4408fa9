"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { RotateCcw, Loader2 } from "lucide-react"
import { useCompare } from "@/components/compare/simple-compare-context"
import { cn } from "@/lib/utils"

interface SimpleCompareToggleProps {
  variantId: number
  className?: string
  size?: "sm" | "default" | "lg"
}

export function SimpleCompareToggle({
  variantId,
  className,
  size = "sm"
}: SimpleCompareToggleProps) {
  const { isInCompare, addToCompare, removeFromCompare } = useCompare()
  const [loading, setLoading] = useState(false)
  const [mounted, setMounted] = useState(false)

  // Prevent hydration mismatch by only checking localStorage after mount
  useEffect(() => {
    setMounted(true)
  }, [])

  // Only check compare status after component is mounted (client-side)
  const inCompare = mounted ? isInCompare(variantId) : false

  const handleToggle = async () => {
    setLoading(true)

    try {
      if (inCompare) {
        await removeFromCompare(variantId)
      } else {
        await addToCompare(variantId)
      }
    } catch (error) {
      console.error('Error toggling compare:', error)
    } finally {
      setLoading(false)
    }
  }

  // Show neutral state during SSR and initial hydration
  if (!mounted) {
    return (
      <Button
        variant="secondary"
        size={size}
        disabled
        className={cn("transition-colors", className)}
        aria-label="Add to compare"
      >
        <RotateCcw className="h-4 w-4" />
      </Button>
    )
  }

  return (
    <Button
      variant={inCompare ? "default" : "secondary"}
      size={size}
      onClick={handleToggle}
      disabled={loading}
      className={cn(
        "transition-colors",
        inCompare && "bg-blue-600 hover:bg-blue-700 text-white",
        className
      )}
      aria-label={inCompare ? "Remove from compare" : "Add to compare"}
    >
      {loading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <RotateCcw
          className={cn(
            "h-4 w-4",
            inCompare && "text-white"
          )}
        />
      )}
    </Button>
  )
}
