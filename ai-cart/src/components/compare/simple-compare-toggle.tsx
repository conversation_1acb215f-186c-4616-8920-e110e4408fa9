"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { RotateCcw, Loader2 } from "lucide-react"
import { useCompare } from "@/components/compare/simple-compare-context"
import { cn } from "@/lib/utils"

interface SimpleCompareToggleProps {
  variantId: number
  className?: string
  size?: "sm" | "default" | "lg"
}

export function SimpleCompareToggle({ 
  variantId, 
  className,
  size = "sm" 
}: SimpleCompareToggleProps) {
  const { isInCompare, addToCompare, removeFromCompare } = useCompare()
  const [loading, setLoading] = useState(false)
  
  const inCompare = isInCompare(variantId)

  const handleToggle = async () => {
    setLoading(true)
    
    try {
      if (inCompare) {
        await removeFromCompare(variantId)
      } else {
        await addToCompare(variantId)
      }
    } catch (error) {
      console.error('Error toggling compare:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Button
      variant={inCompare ? "default" : "secondary"}
      size={size}
      onClick={handleToggle}
      disabled={loading}
      className={cn(
        "transition-colors",
        inCompare && "bg-blue-600 hover:bg-blue-700 text-white",
        className
      )}
      aria-label={inCompare ? "Remove from compare" : "Add to compare"}
    >
      {loading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <RotateCcw 
          className={cn(
            "h-4 w-4",
            inCompare && "text-white"
          )} 
        />
      )}
    </Button>
  )
}
