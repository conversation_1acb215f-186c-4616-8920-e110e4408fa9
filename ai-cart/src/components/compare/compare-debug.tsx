"use client"

import { useEffect, useState } from "react"
import { compareStorage } from "@/lib/storage/compare-storage"
import { useCompare } from "@/components/compare/simple-compare-context"

export function CompareDebug() {
  const [mounted, setMounted] = useState(false)
  const [storageData, setStorageData] = useState<any>(null)
  const { compareCount, getCompareItems } = useCompare()

  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!mounted) return

    const updateData = () => {
      const data = compareStorage.exportData()
      setStorageData(data)
    }

    updateData()
    
    // Update every second to see changes
    const interval = setInterval(updateData, 1000)
    
    return () => clearInterval(interval)
  }, [mounted])

  if (!mounted) return null

  return (
    <div className="fixed bottom-4 left-4 bg-black text-white p-4 rounded-lg text-xs max-w-sm z-50">
      <h3 className="font-bold mb-2">Compare Debug</h3>
      <div className="space-y-1">
        <div>Context Count: {compareCount}</div>
        <div>Storage Count: {storageData?.variantIds?.length || 0}</div>
        <div>Variant IDs: {JSON.stringify(storageData?.variantIds || [])}</div>
        <div>Context Items: {JSON.stringify(getCompareItems())}</div>
        <div>Last Updated: {storageData?.lastUpdated ? new Date(storageData.lastUpdated).toLocaleTimeString() : 'Never'}</div>
      </div>
    </div>
  )
}
