"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Sheet, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import { RotateCcw } from "lucide-react"
import { useCompare } from "@/components/compare/simple-compare-context"
import { CompareSheet } from "@/components/compare/simple-compare-sheet"

export function SimpleCompareButton() {
  const { compareCount } = useCompare()
  const [open, setOpen] = useState(false)

  // Don't render if no items
  if (compareCount === 0) {
    return null
  }

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <RotateCcw className="h-4 w-4" />
          <span className="sr-only">Compare products</span>
          {compareCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {compareCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent className="w-full sm:max-w-lg">
        <SheetHeader>
          <SheetTitle>
            Compare Products ({compareCount})
          </SheetTitle>
        </SheetHeader>
        <div className="mt-6 h-full">
          <CompareSheet onClose={() => setOpen(false)} />
        </div>
      </SheetContent>
    </Sheet>
  )
}
