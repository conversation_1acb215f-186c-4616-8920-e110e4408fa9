"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { X, ShoppingCart, Loader2, RotateCcw, Star, ArrowLeft } from "lucide-react"
import { CompareItem } from "@/lib/actions/compare"
import { useCompareContext } from "@/components/compare/compare-context"
import { addToCart } from "@/lib/actions/cart"
import { toast } from "sonner"
import { formatPrice } from "@/lib/utils"

export function ComparePageContent() {
  const { data: session } = useSession()
  const router = useRouter()
  const { compareItems, removeFromCompareAction, clearCompareAction, loading } = useCompareContext()
  const [removingItems, setRemovingItems] = useState<Set<number>>(new Set())
  const [addingToCart, setAddingToCart] = useState<Set<number>>(new Set())
  const [clearing, setClearing] = useState(false)

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!session?.user && !loading) {
      router.push('/auth/signin?callbackUrl=/compare')
    }
  }, [session, loading, router])

  const handleRemoveFromCompare = async (variantId: number) => {
    try {
      setRemovingItems(prev => new Set(prev).add(variantId))
      
      const result = await removeFromCompareAction(variantId)
      
      if (!result.success) {
        toast.error(result.error || "Failed to remove from compare")
      } else {
        toast.success("Removed from compare")
      }
    } catch (error) {
      console.error('Error removing from compare:', error)
      toast.error("Failed to remove from compare")
    } finally {
      setRemovingItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(variantId)
        return newSet
      })
    }
  }

  const handleAddToCart = async (item: CompareItem) => {
    try {
      setAddingToCart(prev => new Set(prev).add(item.variant.id))
      
      const result = await addToCart(item.variant.id, null, 1)
      
      if (result.success) {
        toast.success("Added to cart")
      } else {
        toast.error(result.message || "Failed to add to cart")
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      toast.error("Failed to add to cart")
    } finally {
      setAddingToCart(prev => {
        const newSet = new Set(prev)
        newSet.delete(item.variant.id)
        return newSet
      })
    }
  }

  const handleClearAll = async () => {
    try {
      setClearing(true)
      const result = await clearCompareAction()
      
      if (!result.success) {
        toast.error(result.error || "Failed to clear compare list")
      } else {
        toast.success("Compare list cleared")
        router.push('/products')
      }
    } catch (error) {
      console.error('Error clearing compare list:', error)
      toast.error("Failed to clear compare list")
    } finally {
      setClearing(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </div>
    )
  }

  if (!session?.user) {
    return null // Will redirect to login
  }

  if (compareItems.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col items-center justify-center h-64 text-center">
          <RotateCcw className="h-16 w-16 text-muted-foreground mb-4" />
          <h1 className="text-2xl font-bold text-muted-foreground mb-2">
            No products to compare
          </h1>
          <p className="text-muted-foreground mb-6">
            Add products to compare their features and prices
          </p>
          <Button asChild>
            <Link href="/products">
              Browse Products
            </Link>
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/products">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Products
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Compare Products</h1>
            <p className="text-muted-foreground">
              Comparing {compareItems.length} product{compareItems.length !== 1 ? 's' : ''}
            </p>
          </div>
        </div>
        
        <Button
          variant="outline"
          onClick={handleClearAll}
          disabled={clearing}
        >
          {clearing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Clearing...
            </>
          ) : (
            "Clear All"
          )}
        </Button>
      </div>

      {/* Mobile View - Cards */}
      <div className="block lg:hidden space-y-6">
        {compareItems.map((item) => {
          const product = item.product
          const variant = item.variant
          const isRemoving = removingItems.has(variant.id)
          const isAddingToCart = addingToCart.has(variant.id)
          const mainImage = product.images?.find(img => img.id === variant.image_id) || product.images?.[0]

          return (
            <Card key={item.id}>
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-lg line-clamp-2">{product.name}</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveFromCompare(variant.id)}
                    disabled={isRemoving}
                  >
                    {isRemoving ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <X className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Image */}
                <div className="relative w-full h-48 bg-gray-100 rounded-lg overflow-hidden">
                  {mainImage ? (
                    <Image
                      src={mainImage.medium_url || mainImage.original_url}
                      alt={product.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                      <span className="text-gray-400">No image</span>
                    </div>
                  )}
                </div>

                {/* Details */}
                <div className="space-y-2">
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">SKU:</span>
                    <span className="ml-2 text-sm">{variant.sku || 'N/A'}</span>
                  </div>
                  
                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Price:</span>
                    <div className="ml-2 inline-flex items-center space-x-2">
                      <span className="text-lg font-bold">
                        {formatPrice(variant.price?.sell_price || variant.price?.price || 0)}
                      </span>
                      {variant.price?.compare_price &&
                       variant.price.compare_price > (variant.price?.sell_price || variant.price?.price || 0) && (
                        <span className="text-sm text-muted-foreground line-through">
                          {formatPrice(variant.price.compare_price)}
                        </span>
                      )}
                    </div>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-muted-foreground">Stock:</span>
                    <span className="ml-2 text-sm">
                      {variant.stock !== undefined ? (
                        variant.stock > 0 ? (
                          <Badge variant="secondary">{variant.stock} in stock</Badge>
                        ) : (
                          <Badge variant="destructive">Out of stock</Badge>
                        )
                      ) : (
                        'N/A'
                      )}
                    </span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-4">
                  <Button
                    onClick={() => handleAddToCart(item)}
                    disabled={isAddingToCart || (variant.stock !== undefined && variant.stock <= 0)}
                    className="flex-1"
                  >
                    {isAddingToCart ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Adding...
                      </>
                    ) : (
                      <>
                        <ShoppingCart className="mr-2 h-4 w-4" />
                        Add to Cart
                      </>
                    )}
                  </Button>
                  
                  <Button variant="outline" asChild>
                    <Link href={`/products/view/${product.slug}`}>
                      View Details
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Desktop View - Table */}
      <div className="hidden lg:block">
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[200px]">Product</TableHead>
                {compareItems.map((item) => (
                  <TableHead key={item.id} className="text-center min-w-[250px]">
                    <div className="flex items-center justify-between">
                      <span className="flex-1 text-left line-clamp-2">{item.product.name}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveFromCompare(item.variant.id)}
                        disabled={removingItems.has(item.variant.id)}
                      >
                        {removingItems.has(item.variant.id) ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <X className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {/* Images */}
              <TableRow>
                <TableCell className="font-medium">Image</TableCell>
                {compareItems.map((item) => {
                  const mainImage = item.product.images?.find(img => img.id === item.variant.image_id) || item.product.images?.[0]
                  return (
                    <TableCell key={item.id} className="text-center">
                      <div className="relative w-32 h-32 mx-auto bg-gray-100 rounded-lg overflow-hidden">
                        {mainImage ? (
                          <Image
                            src={mainImage.medium_url || mainImage.original_url}
                            alt={item.product.name}
                            fill
                            className="object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                            <span className="text-gray-400 text-xs">No image</span>
                          </div>
                        )}
                      </div>
                    </TableCell>
                  )
                })}
              </TableRow>

              {/* SKU */}
              <TableRow>
                <TableCell className="font-medium">SKU</TableCell>
                {compareItems.map((item) => (
                  <TableCell key={item.id} className="text-center">
                    {item.variant.sku || 'N/A'}
                  </TableCell>
                ))}
              </TableRow>

              {/* Price */}
              <TableRow>
                <TableCell className="font-medium">Price</TableCell>
                {compareItems.map((item) => (
                  <TableCell key={item.id} className="text-center">
                    <div className="space-y-1">
                      <div className="text-lg font-bold">
                        {formatPrice(item.variant.price?.sell_price || item.variant.price?.price || 0)}
                      </div>
                      {item.variant.price?.compare_price &&
                       item.variant.price.compare_price > (item.variant.price?.sell_price || item.variant.price?.price || 0) && (
                        <div className="text-sm text-muted-foreground line-through">
                          {formatPrice(item.variant.price.compare_price)}
                        </div>
                      )}
                    </div>
                  </TableCell>
                ))}
              </TableRow>

              {/* Stock */}
              <TableRow>
                <TableCell className="font-medium">Availability</TableCell>
                {compareItems.map((item) => (
                  <TableCell key={item.id} className="text-center">
                    {item.variant.stock !== undefined ? (
                      item.variant.stock > 0 ? (
                        <Badge variant="secondary">{item.variant.stock} in stock</Badge>
                      ) : (
                        <Badge variant="destructive">Out of stock</Badge>
                      )
                    ) : (
                      'N/A'
                    )}
                  </TableCell>
                ))}
              </TableRow>

              {/* Actions */}
              <TableRow>
                <TableCell className="font-medium">Actions</TableCell>
                {compareItems.map((item) => (
                  <TableCell key={item.id} className="text-center">
                    <div className="space-y-2">
                      <Button
                        onClick={() => handleAddToCart(item)}
                        disabled={addingToCart.has(item.variant.id) || (item.variant.stock !== undefined && item.variant.stock <= 0)}
                        className="w-full"
                        size="sm"
                      >
                        {addingToCart.has(item.variant.id) ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Adding...
                          </>
                        ) : (
                          <>
                            <ShoppingCart className="mr-2 h-4 w-4" />
                            Add to Cart
                          </>
                        )}
                      </Button>
                      
                      <Button variant="outline" size="sm" className="w-full" asChild>
                        <Link href={`/products/view/${item.product.slug}`}>
                          View Details
                        </Link>
                      </Button>
                    </div>
                  </TableCell>
                ))}
              </TableRow>
            </TableBody>
          </Table>
        </Card>
      </div>
    </div>
  )
}
