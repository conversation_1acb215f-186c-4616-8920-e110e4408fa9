"use client"

import { useEffect, useRef } from 'react'
import { useSession } from 'next-auth/react'
import { useCompareContext } from '@/components/compare/compare-context'
import { guestCompareStorage } from '@/lib/storage/guest-compare-storage'
import { toast } from 'sonner'

/**
 * Component that handles automatic syncing of guest compare data
 * when user logs in. Should be placed in the app layout.
 */
export function CompareSyncHandler() {
  const { data: session, status } = useSession()
  const { syncGuestDataToBackend, isGuest } = useCompareContext()
  const hasAttemptedSync = useRef(false)
  const previousAuthState = useRef<boolean | null>(null)

  useEffect(() => {
    // Skip if session is still loading
    if (status === 'loading') return

    const isCurrentlyAuthenticated = !!session?.user?.id
    const wasGuest = previousAuthState.current === false
    const isNowAuthenticated = wasGuest && isCurrentlyAuthenticated

    // Update previous auth state
    previousAuthState.current = isCurrentlyAuthenticated

    // Only sync when user transitions from guest to authenticated
    if (isNowAuthenticated && !hasAttemptedSync.current) {
      handleGuestDataSync()
    }

    // Reset sync flag when user logs out
    if (!isCurrentlyAuthenticated) {
      hasAttemptedSync.current = false
    }
  }, [session?.user?.id, status])

  const handleGuestDataSync = async () => {
    try {
      hasAttemptedSync.current = true
      
      // Check if there's guest data to sync
      const guestItems = guestCompareStorage.getItems()
      if (guestItems.length === 0) {
        console.log('No guest compare data to sync')
        return
      }

      console.log(`Found ${guestItems.length} guest compare items to sync`)
      
      // Show loading toast
      const loadingToast = toast.loading('Syncing your compare list...')
      
      // Attempt to sync
      const result = await syncGuestDataToBackend()
      
      // Dismiss loading toast
      toast.dismiss(loadingToast)
      
      if (result.success) {
        if (result.synced > 0) {
          toast.success(
            `Successfully synced ${result.synced} item${result.synced !== 1 ? 's' : ''} to your compare list!`,
            {
              description: 'Your compare items are now saved to your account.',
              duration: 5000
            }
          )
        }
        
        // Show any partial sync errors
        if (result.error) {
          toast.warning('Some items could not be synced', {
            description: result.error,
            duration: 7000
          })
        }
      } else {
        toast.error('Failed to sync compare list', {
          description: result.error || 'Please try again later.',
          duration: 5000
        })
      }
    } catch (error) {
      console.error('Error during compare sync:', error)
      toast.error('Failed to sync compare list', {
        description: 'An unexpected error occurred.',
        duration: 5000
      })
    }
  }

  // This component doesn't render anything
  return null
}

/**
 * Hook to manually trigger guest data sync
 * Useful for login forms or account pages
 */
export function useCompareSyncTrigger() {
  const { syncGuestDataToBackend, isGuest } = useCompareContext()
  
  const triggerSync = async (): Promise<{ success: boolean; synced: number; error?: string }> => {
    if (isGuest) {
      return { success: false, synced: 0, error: 'User not authenticated' }
    }

    try {
      const guestItems = guestCompareStorage.getItems()
      if (guestItems.length === 0) {
        return { success: true, synced: 0 }
      }

      const result = await syncGuestDataToBackend()
      
      if (result.success && result.synced > 0) {
        toast.success(
          `Synced ${result.synced} compare item${result.synced !== 1 ? 's' : ''}!`,
          {
            description: 'Your items are now saved to your account.',
            duration: 4000
          }
        )
      }
      
      return result
    } catch (error) {
      console.error('Manual sync error:', error)
      return { success: false, synced: 0, error: 'Sync failed' }
    }
  }

  const hasGuestData = (): boolean => {
    return guestCompareStorage.getCount() > 0
  }

  return {
    triggerSync,
    hasGuestData,
    isGuest
  }
}

/**
 * Component that shows a banner prompting users to sign in
 * to save their compare list permanently
 */
export function CompareSignInPrompt() {
  const { compareCount, isGuest } = useCompareContext()
  
  // Only show for guests with items in compare
  if (!isGuest || compareCount === 0) {
    return null
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0">
          <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
          </svg>
        </div>
        <div className="flex-1">
          <h3 className="text-sm font-medium text-blue-800">
            Save your compare list
          </h3>
          <p className="text-sm text-blue-700 mt-1">
            Sign in to save your {compareCount} compare item{compareCount !== 1 ? 's' : ''} permanently and access them from any device.
          </p>
          <div className="mt-3 flex gap-2">
            <a
              href="/auth/signin"
              className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              Sign In
            </a>
            <a
              href="/auth/signup"
              className="inline-flex items-center px-3 py-1.5 border border-blue-300 text-xs font-medium rounded text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              Create Account
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}

/**
 * Storage info component for debugging/admin purposes
 */
export function CompareStorageInfo() {
  const { isGuest, compareCount } = useCompareContext()
  
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const storageInfo = guestCompareStorage.getStorageInfo()
  
  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white text-xs p-3 rounded-lg shadow-lg max-w-xs">
      <div className="font-semibold mb-2">Compare Storage Debug</div>
      <div className="space-y-1">
        <div>Mode: {isGuest ? 'Guest' : 'Authenticated'}</div>
        <div>Count: {compareCount}</div>
        <div>Storage Available: {storageInfo.isAvailable ? 'Yes' : 'No'}</div>
        <div>Storage Items: {storageInfo.itemCount}</div>
        <div>Storage Size: {storageInfo.storageSize} chars</div>
        <div>Last Updated: {new Date(storageInfo.lastUpdated).toLocaleTimeString()}</div>
      </div>
    </div>
  )
}
