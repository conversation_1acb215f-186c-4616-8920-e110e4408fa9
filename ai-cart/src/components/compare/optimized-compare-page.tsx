"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { X, ShoppingCart, Loader2, RotateCcw, Trash2, ArrowLeft } from "lucide-react"
import { useOptimizedCompare } from "@/components/compare/optimized-compare-context"
import { addToCart } from "@/lib/actions/cart"
import { toast } from "sonner"
import { formatPrice } from "@/lib/utils"
import Image from "next/image"
import Link from "next/link"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { useRouter } from "next/navigation"

interface CompareProduct {
  id: number
  variant_id: number
  product: {
    id: number
    name: string
    slug: string
    description?: string
    images: Array<{
      id: number
      original_url: string
      medium_url?: string
    }>
  }
  variant: {
    id: number
    sku: string
    price: {
      price: number
      sell_price?: number
      compare_price?: number
    }
    stock?: number
  }
}

export function OptimizedComparePage() {
  const router = useRouter()
  const { products, removeFromCompare, clearCompare, isLoading, getCompareProducts, compareCount } = useOptimizedCompare()
  const [displayProducts, setDisplayProducts] = useState<CompareProduct[]>([])
  const [mounted, setMounted] = useState(false)
  const [localLoading, setLocalLoading] = useState(false)
  const [removingItems, setRemovingItems] = useState<Set<number>>(new Set())
  const [addingToCart, setAddingToCart] = useState<Set<number>>(new Set())

  // Handle hydration
  useEffect(() => {
    setMounted(true)
  }, [])

  // Load products when mounted or when products change
  useEffect(() => {
    if (!mounted) return

    console.log('Compare page - products:', products.length, 'compareCount:', compareCount, 'isLoading:', isLoading)

    if (products.length > 0) {
      // Use products from context
      setDisplayProducts(products)
    } else if (compareCount > 0 && !isLoading) {
      // Load products if we have items but no products loaded
      setLocalLoading(true)
      getCompareProducts().then((loadedProducts) => {
        console.log('Compare page - loaded products:', loadedProducts.length)
        setDisplayProducts(loadedProducts)
        setLocalLoading(false)
      }).catch((error) => {
        console.error('Error loading products:', error)
        setLocalLoading(false)
      })
    } else if (compareCount === 0) {
      // No items in compare
      setDisplayProducts([])
    }
  }, [mounted, products, compareCount, isLoading, getCompareProducts])

  const handleRemove = async (variantId: number) => {
    setRemovingItems(prev => new Set(prev).add(variantId))
    
    try {
      await removeFromCompare(variantId)
      // Remove from local display state immediately for better UX
      setDisplayProducts(prev => prev.filter(p => p.variant.id !== variantId))
    } catch (error) {
      console.error('Error removing item:', error)
    } finally {
      setRemovingItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(variantId)
        return newSet
      })
    }
  }

  const handleAddToCart = async (product: CompareProduct) => {
    setAddingToCart(prev => new Set(prev).add(product.variant.id))
    
    try {
      const result = await addToCart(product.product.id, product.variant.id, 1)
      if (result.data) {
        toast.success('Added to cart')
      } else {
        toast.error(result.error || 'Failed to add to cart')
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      toast.error('Failed to add to cart')
    } finally {
      setAddingToCart(prev => {
        const newSet = new Set(prev)
        newSet.delete(product.variant.id)
        return newSet
      })
    }
  }

  const handleClearAll = async () => {
    try {
      await clearCompare()
      setDisplayProducts([])
      router.push('/products')
    } catch (error) {
      console.error('Error clearing compare:', error)
    }
  }

  // Show loading state
  if (!mounted || ((isLoading || localLoading) && displayProducts.length === 0)) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-muted-foreground">Loading compare products...</p>
          </div>
        </div>
      </div>
    )
  }

  // Show empty state
  if (displayProducts.length === 0) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
          <RotateCcw className="h-16 w-16 text-muted-foreground mb-4" />
          <h1 className="text-2xl font-bold mb-2">No products to compare</h1>
          <p className="text-muted-foreground mb-6 max-w-md">
            Add products to your compare list to see them here. You can compare up to 4 products at once.
          </p>

          {/* Debug info */}
          <div className="bg-gray-100 p-4 rounded-lg mb-4 text-left text-sm">
            <h3 className="font-bold mb-2">Debug Info:</h3>
            <div>Compare Count: {compareCount}</div>
            <div>Context Products: {products.length}</div>
            <div>Display Products: {displayProducts.length}</div>
            <div>Is Loading: {isLoading ? 'Yes' : 'No'}</div>
            <div>Local Loading: {localLoading ? 'Yes' : 'No'}</div>
            <div>Mounted: {mounted ? 'Yes' : 'No'}</div>
          </div>

          <Button onClick={() => router.push('/products')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Browse Products
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Debug info */}
      <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg mb-4 text-sm">
        <h3 className="font-bold mb-2">Debug Info:</h3>
        <div className="grid grid-cols-2 gap-2">
          <div>Compare Count: {compareCount}</div>
          <div>Context Products: {products.length}</div>
          <div>Display Products: {displayProducts.length}</div>
          <div>Is Loading: {isLoading ? 'Yes' : 'No'}</div>
          <div>Local Loading: {localLoading ? 'Yes' : 'No'}</div>
          <div>Mounted: {mounted ? 'Yes' : 'No'}</div>
        </div>
      </div>

      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold">Compare Products</h1>
          <p className="text-muted-foreground mt-1">
            Comparing {displayProducts.length} product{displayProducts.length !== 1 ? 's' : ''}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <Button 
            variant="outline" 
            onClick={handleClearAll}
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Clear All
          </Button>
        </div>
      </div>

      {/* Mobile View - Cards */}
      <div className="block lg:hidden space-y-4">
        {displayProducts.map((product) => {
          const mainImage = product.product.images?.[0]
          const price = product.variant.price.sell_price || product.variant.price.price
          const comparePrice = product.variant.price.compare_price
          const hasDiscount = comparePrice && comparePrice > price
          const isOutOfStock = product.variant.stock !== undefined && product.variant.stock <= 0
          const isRemoving = removingItems.has(product.variant.id)
          const isAddingToCartState = addingToCart.has(product.variant.id)

          return (
            <Card key={product.variant.id}>
              <CardContent className="p-6">
                <div className="flex gap-4">
                  {/* Product Image */}
                  <div className="flex-shrink-0 w-24 h-24">
                    <AspectRatio ratio={1} className="bg-gray-100 rounded-md overflow-hidden">
                      {mainImage ? (
                        <Image
                          src={mainImage.medium_url || mainImage.original_url}
                          alt={product.product.name}
                          fill
                          className="object-cover"
                          sizes="96px"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-400 text-xs">No image</span>
                        </div>
                      )}
                    </AspectRatio>
                  </div>

                  {/* Product Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-2">
                      <Link href={`/products/view/${product.product.slug}`}>
                        <h3 className="font-semibold text-lg line-clamp-2 hover:text-blue-600 transition-colors">
                          {product.product.name}
                        </h3>
                      </Link>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemove(product.variant.id)}
                        disabled={isRemoving}
                        className="text-muted-foreground hover:text-destructive ml-2"
                      >
                        {isRemoving ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <X className="h-4 w-4" />
                        )}
                      </Button>
                    </div>

                    {product.variant.sku && (
                      <p className="text-sm text-muted-foreground mb-2">
                        SKU: {product.variant.sku}
                      </p>
                    )}

                    <div className="flex items-center gap-2 mb-3">
                      <span className="font-bold text-xl">
                        {formatPrice(price)}
                      </span>
                      {hasDiscount && (
                        <span className="text-sm text-muted-foreground line-through">
                          {formatPrice(comparePrice)}
                        </span>
                      )}
                    </div>

                    {isOutOfStock && (
                      <Badge variant="secondary" className="mb-3">
                        Out of Stock
                      </Badge>
                    )}

                    <Button
                      onClick={() => handleAddToCart(product)}
                      disabled={isAddingToCartState || isOutOfStock}
                      className="w-full"
                      size="sm"
                    >
                      {isAddingToCartState ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Adding...
                        </>
                      ) : (
                        <>
                          <ShoppingCart className="mr-2 h-4 w-4" />
                          Add to Cart
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Background loading indicator */}
      {(isLoading || localLoading) && displayProducts.length > 0 && (
        <div className="text-center py-4">
          <p className="text-sm text-muted-foreground">Updating products...</p>
        </div>
      )}
    </div>
  )
}
