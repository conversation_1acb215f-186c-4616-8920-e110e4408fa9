"use client"

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Button } from "@/components/ui/button";
import { RefreshCw, RotateCcw } from "lucide-react";
import { toast } from "sonner";
import { useCompareContext } from "@/components/compare/compare-context";
import { cn } from "@/lib/utils";

interface CompareButtonProps {
  variantId: number;
  variant?: "default" | "outline" | "ghost";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  showText?: boolean;
}

export default function CompareButton({
  variantId,
  variant = "outline",
  size = "default",
  className,
  showText = true
}: CompareButtonProps) {
  const { data: session } = useSession();
  const { addToCompareAction, removeFromCompareAction, isInCompare: contextIsInCompare } = useCompareContext();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);

  // Use variantId if provided
  const targetId = variantId;
  
  // Use context to determine if item is in compare
  const isInCompare = targetId ? contextIsInCompare(targetId) : false;

  // Set initial loading to false since we're using context
  useEffect(() => {
    setInitialLoading(false);
  }, []);

  const handleToggleCompare = async () => {
    if (!targetId) {
      toast.error('Invalid product variant');
      return;
    }

    setLoading(true);

    try {
      if (isInCompare) {
        const result = await removeFromCompareAction(targetId);
        if (!result.success) {
          toast.error(result.error || 'Failed to remove from compare');
          return;
        }
        toast.success('Removed from compare');
      } else {
        const result = await addToCompareAction(targetId);
        if (!result.success) {
          toast.error(result.error || 'Failed to add to compare');
          return;
        }
        toast.success('Added to compare');
      }
    } catch (error) {
      toast.error('Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <Button 
        variant={variant} 
        size={size}
        className={cn("w-full", className)}
        disabled
      >
        <RefreshCw className="h-4 w-4 animate-spin mr-2" />
        {showText && "Loading..."}
      </Button>
    );
  }

  return (
    <Button 
      variant={variant} 
      size={size}
      className={cn("w-full", className)}
      onClick={handleToggleCompare}
      disabled={loading}
    >
      {loading ? (
        <RefreshCw className="h-4 w-4 animate-spin mr-2" />
      ) : (
        <RotateCcw 
          className={cn(
            "h-4 w-4",
            showText && "mr-2",
            isInCompare && "fill-blue-500 text-blue-500"
          )} 
        />
      )}
      {showText && (isInCompare ? "Remove from Compare" : "Add to Compare")}
    </Button>
  );
}
