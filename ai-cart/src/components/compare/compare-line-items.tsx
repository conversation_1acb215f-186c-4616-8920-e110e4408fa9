"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { X, ShoppingCart, Loader2, Eye, RotateCcw } from "lucide-react"
import { CompareItem } from "@/lib/actions/compare"
import { useCompareContext } from "@/components/compare/compare-context"
import { addToCart } from "@/lib/actions/cart"
import { showErrorToast } from "@/lib/handle-error"
import { toast } from "sonner"
import { formatPrice } from "@/lib/utils"

interface CompareLineItemsProps {
  items: CompareItem[]
  onItemRemoved?: (variantId: number) => void
}

export function CompareLineItems({ items, onItemRemoved }: CompareLineItemsProps) {
  const { removeFromCompareAction, clearCompareAction } = useCompareContext()
  const [removingItems, setRemovingItems] = useState<Set<number>>(new Set())
  const [addingToCart, setAddingToCart] = useState<Set<number>>(new Set())
  const [clearing, setClearing] = useState(false)

  const handleRemoveFromCompare = async (variantId: number) => {
    try {
      setRemovingItems(prev => new Set(prev).add(variantId))
      
      console.log('🔄 Removing from compare (line-items):', variantId)
      const result = await removeFromCompareAction(variantId)
      console.log('🔄 Remove result (line-items):', result)
      
      if (!result.success) {
        console.error('❌ Remove failed (line-items):', result.error)
        toast.error(result.error || "Failed to remove from compare")
      } else {
        console.log('✅ Remove successful (line-items)')
        onItemRemoved?.(variantId)
        toast.success("Removed from compare")
      }
    } catch (error) {
      console.error('❌ Error removing from compare (line-items):', error)
      toast.error("Failed to remove from compare")
    } finally {
      setRemovingItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(variantId)
        return newSet
      })
    }
  }

  const handleAddToCart = async (item: CompareItem) => {
    try {
      setAddingToCart(prev => new Set(prev).add(item.variant.id))
      
      const result = await addToCart(item.variant.id, null, 1)
      
      if (result.success) {
        toast.success("Added to cart")
      } else {
        toast.error(result.message || "Failed to add to cart")
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      toast.error("Failed to add to cart")
    } finally {
      setAddingToCart(prev => {
        const newSet = new Set(prev)
        newSet.delete(item.variant.id)
        return newSet
      })
    }
  }

  const handleClearAll = async () => {
    try {
      setClearing(true)
      const result = await clearCompareAction()
      
      if (!result.success) {
        toast.error(result.error || "Failed to clear compare list")
      } else {
        toast.success("Compare list cleared")
      }
    } catch (error) {
      console.error('Error clearing compare list:', error)
      toast.error("Failed to clear compare list")
    } finally {
      setClearing(false)
    }
  }

  if (items.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <RotateCcw className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium text-muted-foreground mb-2">
          No products to compare
        </h3>
        <p className="text-sm text-muted-foreground">
          Add products to compare their features and prices
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          {items.length} product{items.length !== 1 ? 's' : ''} to compare
        </p>
        <Button
          variant="outline"
          size="sm"
          onClick={handleClearAll}
          disabled={clearing}
        >
          {clearing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Clearing...
            </>
          ) : (
            "Clear All"
          )}
        </Button>
      </div>

      <Separator />

      <div className="space-y-4 max-h-[calc(100vh-200px)] overflow-y-auto">
        {items.map((item) => {
          const product = item.product
          const variant = item.variant
          const isRemoving = removingItems.has(variant.id)
          const isAddingToCart = addingToCart.has(variant.id)

          // Get the main product image or variant-specific image
          const mainImage = product.images?.find(img => img.id === variant.image_id) || product.images?.[0]

          return (
            <Card key={item.id} className="overflow-hidden">
              <CardContent className="p-4">
                <div className="flex gap-4">
                  {/* Product Image */}
                  <div className="flex-shrink-0">
                    <div className="relative w-16 h-16 bg-gray-100 rounded-md overflow-hidden">
                      {mainImage ? (
                        <Image
                          src={mainImage.medium_url || mainImage.original_url}
                          alt={product.name}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-400 text-xs">No image</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Product Details */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <Link 
                          href={`/products/view/${product.slug}`}
                          className="text-sm font-medium text-gray-900 hover:text-blue-600 line-clamp-2"
                        >
                          {product.name}
                        </Link>
                        
                        {variant.sku && (
                          <p className="text-xs text-muted-foreground mt-1">
                            SKU: {variant.sku}
                          </p>
                        )}

                        {/* Price */}
                        <div className="flex items-center space-x-2 mt-2">
                          <span className="text-sm font-medium">
                            {formatPrice(
                              variant.price?.sell_price || variant.price?.price || 0
                            )}
                          </span>
                          {variant.price?.compare_price &&
                           variant.price.compare_price > (variant.price?.sell_price || variant.price?.price || 0) && (
                            <span className="text-xs text-muted-foreground line-through">
                              {formatPrice(variant.price.compare_price)}
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Remove Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveFromCompare(variant.id)}
                        disabled={isRemoving}
                        className="flex-shrink-0 ml-2"
                      >
                        {isRemoving ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <X className="h-4 w-4" />
                        )}
                      </Button>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2 mt-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleAddToCart(item)}
                        disabled={isAddingToCart}
                        className="flex-1"
                      >
                        {isAddingToCart ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Adding...
                          </>
                        ) : (
                          <>
                            <ShoppingCart className="mr-2 h-4 w-4" />
                            Add to Cart
                          </>
                        )}
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        asChild
                      >
                        <Link href={`/products/view/${product.slug}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Compare Action */}
      {items.length >= 2 && (
        <>
          <Separator />
          <Button 
            className="w-full" 
            asChild
          >
            <Link href="/compare">
              Compare {items.length} Products
            </Link>
          </Button>
        </>
      )}
    </div>
  )
}
