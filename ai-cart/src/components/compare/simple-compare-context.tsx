"use client"

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { compareStorage } from '@/lib/storage/compare-storage'
import { toast } from 'sonner'

interface CompareContextType {
  compareCount: number
  isInCompare: (variantId: number) => boolean
  addToCompare: (variantId: number) => Promise<{ success: boolean; error?: string }>
  removeFromCompare: (variantId: number) => Promise<{ success: boolean; error?: string }>
  clearCompare: () => Promise<{ success: boolean; error?: string }>
  getCompareItems: () => number[]
}

const CompareContext = createContext<CompareContextType | undefined>(undefined)

export function useCompare() {
  const context = useContext(CompareContext)
  if (context === undefined) {
    throw new Error('useCompare must be used within a CompareProvider')
  }
  return context
}

export function CompareProvider({ children }: { children: React.ReactNode }) {
  const [compareCount, setCompareCount] = useState(0)
  const [mounted, setMounted] = useState(false)

  // Handle hydration
  useEffect(() => {
    setMounted(true)
  }, [])

  // Load initial count from localStorage after hydration
  useEffect(() => {
    if (!mounted) return

    const count = compareStorage.getCount()
    setCompareCount(count)
  }, [mounted])

  // Check if item is in compare
  const isInCompare = useCallback((variantId: number): boolean => {
    if (!mounted) return false
    return compareStorage.hasItem(variantId)
  }, [mounted])

  // Add item to compare
  const addToCompare = useCallback(async (variantId: number): Promise<{ success: boolean; error?: string }> => {
    try {
      const result = compareStorage.addItem(variantId)
      
      if (result.success) {
        setCompareCount(prev => prev + 1)
        toast.success('Added to compare')
      } else {
        toast.error(result.error || 'Failed to add to compare')
      }
      
      return result
    } catch (error) {
      const errorMessage = 'Failed to add to compare'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    }
  }, [])

  // Remove item from compare
  const removeFromCompare = useCallback(async (variantId: number): Promise<{ success: boolean; error?: string }> => {
    try {
      const result = compareStorage.removeItem(variantId)
      
      if (result.success) {
        setCompareCount(prev => Math.max(0, prev - 1))
        toast.success('Removed from compare')
      } else {
        toast.error(result.error || 'Failed to remove from compare')
      }
      
      return result
    } catch (error) {
      const errorMessage = 'Failed to remove from compare'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    }
  }, [])

  // Clear all compare items
  const clearCompare = useCallback(async (): Promise<{ success: boolean; error?: string }> => {
    try {
      const result = compareStorage.clearAll()
      
      if (result.success) {
        setCompareCount(0)
        toast.success('Compare list cleared')
      } else {
        toast.error('Failed to clear compare list')
      }
      
      return result
    } catch (error) {
      const errorMessage = 'Failed to clear compare list'
      toast.error(errorMessage)
      return { success: false, error: errorMessage }
    }
  }, [])

  // Get all compare items
  const getCompareItems = useCallback((): number[] => {
    if (!mounted) return []
    return compareStorage.getVariantIds()
  }, [mounted])

  const value: CompareContextType = {
    compareCount,
    isInCompare,
    addToCompare,
    removeFromCompare,
    clearCompare,
    getCompareItems
  }

  return (
    <CompareContext.Provider value={value}>
      {children}
    </CompareContext.Provider>
  )
}
