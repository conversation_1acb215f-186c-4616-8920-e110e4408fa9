"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { X, ShoppingCart, Loader2, ExternalLink, Trash2, RotateCcw } from "lucide-react"
import { useCompare } from "@/components/compare/simple-compare-context"
import { getProductsByVariantIds } from "@/lib/actions/compare"
import { addToCart } from "@/lib/actions/cart"
import { toast } from "sonner"
import { formatPrice } from "@/lib/utils"
import Image from "next/image"
import Link from "next/link"
import { AspectRatio } from "@/components/ui/aspect-ratio"

interface CompareSheetProps {
  onClose: () => void
}

interface CompareProduct {
  id: number
  variant_id: number
  product: {
    id: number
    name: string
    slug: string
    images: Array<{
      id: number
      original_url: string
      medium_url?: string
    }>
  }
  variant: {
    id: number
    sku: string
    price: {
      price: number
      sell_price?: number
      compare_price?: number
    }
    stock?: number
  }
}

export function CompareSheet({ onClose }: CompareSheetProps) {
  const { getCompareItems, removeFromCompare, clearCompare } = useCompare()
  const [products, setProducts] = useState<CompareProduct[]>([])
  const [loading, setLoading] = useState(true)
  const [removingItems, setRemovingItems] = useState<Set<number>>(new Set())
  const [addingToCart, setAddingToCart] = useState<Set<number>>(new Set())

  // Load products data
  useEffect(() => {
    const loadProducts = async () => {
      try {
        setLoading(true)
        const variantIds = getCompareItems()
        
        if (variantIds.length === 0) {
          setProducts([])
          return
        }

        const compareItems = await getProductsByVariantIds(variantIds)
        setProducts(compareItems)
      } catch (error) {
        console.error('Error loading compare products:', error)
        toast.error('Failed to load compare products')
      } finally {
        setLoading(false)
      }
    }

    loadProducts()
  }, [getCompareItems])

  const handleRemove = async (variantId: number) => {
    setRemovingItems(prev => new Set(prev).add(variantId))
    
    try {
      await removeFromCompare(variantId)
      // Remove from local state
      setProducts(prev => prev.filter(p => p.variant.id !== variantId))
    } catch (error) {
      console.error('Error removing item:', error)
    } finally {
      setRemovingItems(prev => {
        const newSet = new Set(prev)
        newSet.delete(variantId)
        return newSet
      })
    }
  }

  const handleAddToCart = async (product: CompareProduct) => {
    setAddingToCart(prev => new Set(prev).add(product.variant.id))
    
    try {
      const result = await addToCart(product.variant.id, null, 1)
      if (result.success) {
        toast.success('Added to cart')
      } else {
        toast.error(result.message || 'Failed to add to cart')
      }
    } catch (error) {
      console.error('Error adding to cart:', error)
      toast.error('Failed to add to cart')
    } finally {
      setAddingToCart(prev => {
        const newSet = new Set(prev)
        newSet.delete(product.variant.id)
        return newSet
      })
    }
  }

  const handleClearAll = async () => {
    try {
      await clearCompare()
      setProducts([])
      onClose()
    } catch (error) {
      console.error('Error clearing compare:', error)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (products.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <div className="text-muted-foreground mb-4">
          <RotateCcw className="h-16 w-16 mx-auto mb-4" />
          <h3 className="text-lg font-medium">No items to compare</h3>
          <p className="text-sm">Add products to your compare list to see them here.</p>
        </div>
        <Button onClick={onClose} variant="outline">
          Continue Shopping
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Link href="/compare">
            <Button variant="outline" size="sm">
              <ExternalLink className="h-4 w-4 mr-2" />
              View Full Compare
            </Button>
          </Link>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={handleClearAll}
          className="text-destructive hover:text-destructive"
        >
          <Trash2 className="h-4 w-4 mr-2" />
          Clear All
        </Button>
      </div>

      <Separator />

      {/* Products List */}
      <div className="space-y-4 max-h-[calc(100vh-200px)] overflow-y-auto">
        {products.map((product) => {
          const mainImage = product.product.images?.[0]
          const price = product.variant.price.sell_price || product.variant.price.price
          const comparePrice = product.variant.price.compare_price
          const hasDiscount = comparePrice && comparePrice > price
          const isOutOfStock = product.variant.stock !== undefined && product.variant.stock <= 0
          const isRemoving = removingItems.has(product.variant.id)
          const isAddingToCart = addingToCart.has(product.variant.id)

          return (
            <Card key={product.variant.id} className="relative">
              <CardContent className="p-4">
                <div className="flex gap-4">
                  {/* Product Image */}
                  <div className="flex-shrink-0 w-20 h-20">
                    <AspectRatio ratio={1} className="bg-gray-100 rounded-md overflow-hidden">
                      {mainImage ? (
                        <Image
                          src={mainImage.medium_url || mainImage.original_url}
                          alt={product.product.name}
                          fill
                          className="object-cover"
                          sizes="80px"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                          <span className="text-gray-400 text-xs">No image</span>
                        </div>
                      )}
                    </AspectRatio>
                  </div>

                  {/* Product Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <Link href={`/products/view/${product.product.slug}`}>
                          <h4 className="font-medium text-sm line-clamp-2 hover:text-blue-600 transition-colors">
                            {product.product.name}
                          </h4>
                        </Link>
                        
                        {product.variant.sku && (
                          <p className="text-xs text-muted-foreground mt-1">
                            SKU: {product.variant.sku}
                          </p>
                        )}

                        <div className="flex items-center gap-2 mt-2">
                          <span className="font-bold text-lg">
                            {formatPrice(price)}
                          </span>
                          {hasDiscount && (
                            <span className="text-sm text-muted-foreground line-through">
                              {formatPrice(comparePrice)}
                            </span>
                          )}
                        </div>

                        {isOutOfStock && (
                          <Badge variant="secondary" className="mt-1">
                            Out of Stock
                          </Badge>
                        )}
                      </div>

                      {/* Remove Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemove(product.variant.id)}
                        disabled={isRemoving}
                        className="text-muted-foreground hover:text-destructive"
                      >
                        {isRemoving ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <X className="h-4 w-4" />
                        )}
                      </Button>
                    </div>

                    {/* Add to Cart Button */}
                    <Button
                      onClick={() => handleAddToCart(product)}
                      disabled={isAddingToCart || isOutOfStock}
                      className="w-full mt-3"
                      size="sm"
                    >
                      {isAddingToCart ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Adding...
                        </>
                      ) : (
                        <>
                          <ShoppingCart className="mr-2 h-4 w-4" />
                          Add to Cart
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}
