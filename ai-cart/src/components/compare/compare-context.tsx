"use client"

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import {
  CompareItem,
  getCompareList,
  getCompareCount,
  addToCompare,
  remove<PERSON><PERSON><PERSON><PERSON>pare,
  clearCompare<PERSON>ist,
  getCompareVariantIds
} from '@/lib/actions/compare'
import { useCompareActionPerformance } from '@/components/compare/compare-performance-monitor'

interface CompareContextType {
  compareItems: CompareItem[]
  compareCount: number
  addToCompareAction: (variantId: number) => Promise<{ success: boolean; error?: string }>
  removeFromCompareAction: (variantId: number) => Promise<{ success: boolean; error?: string }>
  clearCompareAction: () => Promise<{ success: boolean; error?: string }>
  isInCompare: (variantId: number) => boolean
  refreshCompare: () => Promise<void>
  loading: boolean
}

const CompareContext = createContext<CompareContextType | undefined>(undefined)

export function CompareProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession()
  const [compareItems, setCompareItems] = useState<CompareItem[]>([])
  const [compareCount, setCompareCount] = useState(0)
  const [loading, setLoading] = useState(false)
  const { measureAction } = useCompareActionPerformance()

  // Load compare data when user is authenticated
  const refreshCompare = useCallback(async () => {
    if (!session?.user?.id) {
      setCompareItems([])
      setCompareCount(0)
      return
    }

    try {
      setLoading(true)
      console.log('🔄 Refreshing compare data...')
      
      const [compareResponse, count] = await Promise.all([
        getCompareList(0, 20), // Get first 20 items
        getCompareCount()
      ])

      console.log('✅ Compare data loaded:', { items: compareResponse.items.length, count })
      setCompareItems(compareResponse.items)
      setCompareCount(count)
    } catch (error) {
      console.error('❌ Error loading compare data:', error)
      setCompareItems([])
      setCompareCount(0)
    } finally {
      setLoading(false)
    }
  }, [session?.user?.id])

  // Load compare data on mount and when session changes
  useEffect(() => {
    if (status === 'loading') {
      console.log('Session still loading, waiting...')
      return
    }

    refreshCompare()
  }, [session?.user?.id, status, refreshCompare])

  const removeCompareItem = (variantId: number) => {
    setCompareItems(prev => prev.filter(item => item.variant.id !== variantId))
    setCompareCount(prev => Math.max(0, prev - 1))
  }

  const addToCompareAction = async (variantId: number): Promise<{ success: boolean; error?: string }> => {
    if (!session?.user?.id) {
      return { success: false, error: 'Please sign in to add items to compare' }
    }

    // Check if compare list is full (max 4 items)
    if (compareCount >= 4) {
      return { success: false, error: 'Compare list is full. You can compare up to 4 products.' }
    }

    try {
      console.log('🔄 Adding to compare:', variantId, 'Current count:', compareCount)
      const result = await measureAction('addToCompare', () => addToCompare(variantId))
      const { error } = result
      if (error) {
        return { success: false, error }
      }

      // Update local state immediately
      setCompareCount(prev => {
        const newCount = prev + 1
        console.log('✅ Updated compare count:', prev, '->', newCount)
        return newCount
      })

      // Refresh full compare data in background
      refreshCompare()

      return { success: true }
    } catch (error) {
      return { success: false, error: 'Something went wrong' }
    }
  }

  const removeFromCompareAction = async (variantId: number): Promise<{ success: boolean; error?: string }> => {
    if (!session?.user?.id) {
      return { success: false, error: 'Please sign in to manage your compare list' }
    }

    try {
      console.log('🔄 Removing from compare:', variantId, 'Current count:', compareCount)
      const result = await measureAction('removeFromCompare', () => removeFromCompare(variantId))
      const { error } = result
      if (error) {
        return { success: false, error }
      }

      // Update local state immediately
      removeCompareItem(variantId)
      console.log('✅ Removed from compare, new count should be:', compareCount - 1)

      // Refresh full compare data in background
      refreshCompare()

      return { success: true }
    } catch (error) {
      return { success: false, error: 'Something went wrong' }
    }
  }

  const clearCompareAction = async (): Promise<{ success: boolean; error?: string }> => {
    if (!session?.user?.id) {
      return { success: false, error: 'Please sign in to manage your compare list' }
    }

    try {
      console.log('🔄 Clearing compare list')
      const result = await measureAction('clearCompareList', () => clearCompareList())
      const { error } = result
      if (error) {
        return { success: false, error }
      }

      // Update local state immediately
      setCompareItems([])
      setCompareCount(0)
      console.log('✅ Compare list cleared')

      return { success: true }
    } catch (error) {
      return { success: false, error: 'Something went wrong' }
    }
  }

  const isInCompare = (variantId: number): boolean => {
    return compareItems.some(item => item.variant.id === variantId)
  }

  return (
    <CompareContext.Provider
      value={{
        compareItems,
        compareCount,
        addToCompareAction,
        removeFromCompareAction,
        clearCompareAction,
        isInCompare,
        refreshCompare,
        loading
      }}
    >
      {children}
    </CompareContext.Provider>
  )
}

export function useCompareContext() {
  const context = useContext(CompareContext)
  if (context === undefined) {
    throw new Error('useCompareContext must be used within a CompareProvider')
  }
  return context
}
