"use client"

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import {
  CompareItem,
  getCompareList,
  getCompareCount,
  addTo<PERSON>ompare,
  remove<PERSON><PERSON><PERSON><PERSON>par<PERSON>,
  clearCompare<PERSON>ist,
  getCompareVariantIds
} from '@/lib/actions/compare'
import { useCompareActionPerformance } from '@/components/compare/compare-performance-monitor'
import { guestCompareStorage } from '@/lib/storage/guest-compare-storage'
import { useOptimizedGuestCompare } from '@/hooks/use-guest-performance'
import { Product } from '@/types/product'

interface CompareContextType {
  compareItems: CompareItem[]
  compareCount: number
  loading: boolean
  isGuest: boolean
  addToCompareAction: (variantId: number) => Promise<{ success: boolean; error?: string }>
  removeFromCompareAction: (variantId: number) => Promise<{ success: boolean; error?: string }>
  clearCompareAction: () => Promise<{ success: boolean; error?: string }>
  isInCompare: (variantId: number) => boolean
  refreshCompare: () => Promise<void>
  syncGuestDataToBackend: () => Promise<{ success: boolean; synced: number; error?: string }>
}

const CompareContext = createContext<CompareContextType | undefined>(undefined)

export function CompareProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession()
  const [compareItems, setCompareItems] = useState<CompareItem[]>([])
  const [compareCount, setCompareCount] = useState(0)
  const [loading, setLoading] = useState(false)
  const { measureAction } = useCompareActionPerformance()
  const optimizedGuestCompare = useOptimizedGuestCompare()

  // Determine if user is guest (not authenticated)
  const isGuest = !session?.user

  // Load compare data for both guest and authenticated users
  const refreshCompare = useCallback(async () => {
    try {
      setLoading(true)

      if (session?.user?.id) {
        // Authenticated user - load from backend
        console.log('🔄 Refreshing compare data for authenticated user...')

        const [compareResponse, count] = await Promise.all([
          getCompareList(0, 20), // Get first 20 items
          getCompareCount()
        ])

        console.log('✅ Compare data loaded:', { items: compareResponse.items.length, count })
        setCompareItems(compareResponse.items)
        setCompareCount(count)
      } else {
        // Guest user - load from localStorage (just variant IDs)
        console.log('🔄 Refreshing compare data for guest user...')

        const guestVariantIds = guestCompareStorage.getVariantIds()
        const guestCount = guestCompareStorage.getCount()

        // For guest users, we'll load the full product data when needed (on compare page)
        // For now, just create minimal items for state management
        const minimalItems: CompareItem[] = guestVariantIds.map(variantId => ({
          id: variantId,
          variant_id: variantId,
          variant: {
            id: variantId,
            sku: '',
            price: { price: 0, sell_price: 0 }
          },
          product: {
            id: 0, // Will be loaded from API when needed
            name: 'Loading...',
            slug: '',
            images: [],
            variants: [{
              id: variantId,
              sku: '',
              price: { price: 0, sell_price: 0 }
            }]
          }
        }))

        console.log('✅ Guest compare data loaded:', { variantIds: guestVariantIds.length, count: guestCount })
        setCompareItems(minimalItems)
        setCompareCount(guestCount)
      }
    } catch (error) {
      console.error('❌ Error loading compare data:', error)
      setCompareItems([])
      setCompareCount(0)
    } finally {
      setLoading(false)
    }
  }, [session?.user?.id])

  // Load compare data on mount and when session changes
  useEffect(() => {
    if (status === 'loading') {
      console.log('Session still loading, waiting...')
      return
    }

    refreshCompare()
  }, [session?.user?.id, status, refreshCompare])

  const removeCompareItem = (variantId: number) => {
    setCompareItems(prev => prev.filter(item => item.variant.id !== variantId))
    setCompareCount(prev => Math.max(0, prev - 1))
  }

  const addToCompareAction = async (variantId: number): Promise<{ success: boolean; error?: string }> => {
    // Check if compare list is full (max 4 items)
    if (compareCount >= 4) {
      return { success: false, error: 'Compare list is full. You can compare up to 4 products.' }
    }

    try {
      console.log('🔄 Adding to compare:', variantId, 'Current count:', compareCount, 'Is guest:', isGuest)

      if (session?.user?.id) {
        // Authenticated user - use backend API
        const result = await measureAction('addToCompare', () => addToCompare(variantId))
        const { error } = result
        if (error) {
          return { success: false, error }
        }

        // Update local state immediately
        setCompareCount(prev => {
          const newCount = prev + 1
          console.log('✅ Updated compare count:', prev, '->', newCount)
          return newCount
        })

        // Refresh full compare data in background
        refreshCompare()

        return { success: true }
      } else {
        // Guest user - use localStorage (just store variant ID)
        const result = guestCompareStorage.addItem(variantId)
        if (!result.success) {
          return { success: false, error: result.error }
        }

        // Update local state immediately
        setCompareCount(prev => {
          const newCount = prev + 1
          console.log('✅ Updated guest compare count:', prev, '->', newCount)
          return newCount
        })

        // Refresh compare data to show new item
        refreshCompare()

        return { success: true }
      }
    } catch (error) {
      return { success: false, error: 'Something went wrong' }
    }
  }

  const removeFromCompareAction = async (variantId: number): Promise<{ success: boolean; error?: string }> => {
    try {
      console.log('🔄 Removing from compare:', variantId, 'Current count:', compareCount, 'Is guest:', isGuest)

      if (session?.user?.id) {
        // Authenticated user - use backend API
        const result = await measureAction('removeFromCompare', () => removeFromCompare(variantId))
        const { error } = result
        if (error) {
          return { success: false, error }
        }

        // Update local state immediately
        removeCompareItem(variantId)
        console.log('✅ Removed from compare, new count should be:', compareCount - 1)

        // Refresh full compare data in background
        refreshCompare()

        return { success: true }
      } else {
        // Guest user - use localStorage
        const result = guestCompareStorage.removeItem(variantId)
        if (!result.success) {
          return { success: false, error: result.error }
        }

        // Update local state immediately
        removeCompareItem(variantId)
        console.log('✅ Removed from guest compare, new count should be:', compareCount - 1)

        // Refresh compare data
        refreshCompare()

        return { success: true }
      }
    } catch (error) {
      return { success: false, error: 'Something went wrong' }
    }
  }

  const clearCompareAction = async (): Promise<{ success: boolean; error?: string }> => {
    try {
      console.log('🔄 Clearing compare list, current count:', compareCount, 'Is guest:', isGuest)

      if (session?.user?.id) {
        // Authenticated user - use backend API
        const result = await measureAction('clearCompareList', () => clearCompareList())
        const { error } = result
        if (error) {
          return { success: false, error }
        }

        // Update local state immediately
        setCompareItems([])
        setCompareCount(0)
        console.log('✅ Compare list cleared')

        return { success: true }
      } else {
        // Guest user - use localStorage
        const result = guestCompareStorage.clearAll()
        if (!result.success) {
          return { success: false, error: 'Failed to clear compare list' }
        }

        // Update local state immediately
        setCompareItems([])
        setCompareCount(0)
        console.log('✅ Guest compare list cleared')

        return { success: true }
      }
    } catch (error) {
      return { success: false, error: 'Something went wrong' }
    }
  }

  // Sync guest data to backend when user logs in
  const syncGuestDataToBackend = async (): Promise<{ success: boolean; synced: number; error?: string }> => {
    if (!session?.user?.id) {
      return { success: false, synced: 0, error: 'User not authenticated' }
    }

    try {
      console.log('🔄 Syncing guest compare data to backend...')
      const guestItems = guestCompareStorage.getVariantIds()

      if (guestItems.length === 0) {
        return { success: true, synced: 0 }
      }

      let syncedCount = 0
      const errors: string[] = []

      // Add each guest variant to backend
      for (const variantId of guestItems) {
        try {
          const result = await addToCompare(variantId)
          if (!result.error) {
            syncedCount++
          } else {
            errors.push(`Failed to sync variant ${variantId}: ${result.error}`)
          }
        } catch (error) {
          errors.push(`Failed to sync variant ${variantId}: ${error}`)
        }
      }

      // Clear guest data after successful sync
      if (syncedCount > 0) {
        guestCompareStorage.clearAll()
        console.log(`✅ Synced ${syncedCount} items to backend`)

        // Refresh compare data to show synced items
        refreshCompare()
      }

      return {
        success: true,
        synced: syncedCount,
        error: errors.length > 0 ? errors.join('; ') : undefined
      }
    } catch (error) {
      return { success: false, synced: 0, error: 'Failed to sync data' }
    }
  }

  const isInCompare = (variantId: number): boolean => {
    if (isGuest) {
      // For guest users, check localStorage directly for better performance
      return guestCompareStorage.hasItem(variantId)
    }
    // For authenticated users, check the loaded compare items
    return compareItems.some(item =>
      item.variant?.id === variantId || item.variant_id === variantId
    )
  }

  return (
    <CompareContext.Provider
      value={{
        compareItems,
        compareCount,
        loading,
        isGuest,
        addToCompareAction,
        removeFromCompareAction,
        clearCompareAction,
        isInCompare,
        refreshCompare,
        syncGuestDataToBackend
      }}
    >
      {children}
    </CompareContext.Provider>
  )
}

export function useCompareContext() {
  const context = useContext(CompareContext)
  if (context === undefined) {
    throw new Error('useCompareContext must be used within a CompareProvider')
  }
  return context
}
