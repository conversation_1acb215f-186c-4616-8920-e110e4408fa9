export interface DashboardStats {
  // KPI Stats
  totalRevenue: number;
  revenueGrowthPercent: number;
  totalCustomers: number;
  customerGrowthPercent: number;
  totalOrders: number;
  orderGrowthPercent: number;
  activeCustomers: number;
  activeCustomersChange: number;
  
  // Chart Data
  revenueChart: ChartDataPoint[];
  orderChart: ChartDataPoint[];
  topCategories: CategoryDataPoint[];
  recentOrders: RecentOrder[];
}

export interface ChartDataPoint {
  date: string;
  value: number;
  count: number;
}

export interface CategoryDataPoint {
  category: string;
  revenue: number;
  orderCount: number;
  color: string;
}

export interface RecentOrder {
  orderId: number;
  customerName: string;
  customerEmail: string;
  orderTotal: number;
  orderDate: string;
  status: string;
}

export interface AnalyticsApiResponse<T> {
  data?: T;
  error?: string;
}

// Chart data formats for recharts
export interface BarChartData {
  date: string;
  desktop: number;
  mobile: number;
}

export interface AreaChartData {
  month: string;
  desktop: number;
  mobile: number;
}

export interface PieChartData {
  category: string;
  visitors: number;
  fill: string;
}
