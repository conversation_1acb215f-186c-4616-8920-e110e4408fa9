export interface InventoryItem {
  variantId: number;
  productId: number;
  productName: string;
  productSlug: string;
  variantSku: string;
  currentStock: number;
  reservedStock: number;
  availableStock: number;
  createdAt: string;
  updatedAt: string;
}

export interface InventoryUpdateRequest {
  variantId: number;
  warehouseId: number;
  quantity: number;
  adjustmentType: 'SET' | 'INCREASE' | 'DECREASE';
  reason: string;
  notes?: string;
}

export interface Warehouse {
  id: number;
  name: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
  contactNumber?: string;
  isActive: boolean;
}

export interface InventoryFilters {
  search?: string;
  warehouseId?: number;
  lowStock?: boolean;
  outOfStock?: boolean;
  trackQuantity?: boolean;
  productId?: number;
  brandId?: number;
  categoryId?: number;
}

export interface InventoryResponse {
  data: InventoryItem[];
  total: number;
  page: number;
  size: number;
}

export interface StockAdjustmentHistory {
  id: number;
  variantId: number;
  warehouseId: number;
  adjustmentType: string;
  quantity: number;
  previousQuantity: number;
  newQuantity: number;
  reason: string;
  notes?: string;
  createdBy: string;
  createdAt: string;
}
