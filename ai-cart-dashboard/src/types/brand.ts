export interface Brand {
  id: number;
  name: string;
  description?: string;
  logo?: string;
  website?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface BrandFormData {
  name: string;
  description?: string;
  logo?: string;
  website?: string;
}

export interface BrandListResponse {
  data: Brand[];
  total: number;
  page: number;
  size: number;
}

export interface CreateBrandRequest {
  name: string;
  description?: string;
  logo?: string;
  website?: string;
}

export interface UpdateBrandRequest {
  name: string;
  description?: string;
  logo?: string;
  website?: string;
}
