export interface InventoryItem {
  id: number;
  productId: number;
  productName: string;
  productSlug: string;
  variantId: number;
  variantSku: string;
  variantAttributes: VariantAttribute[];
  currentStock: number;
  reservedStock: number;
  availableStock: number;
  incomingStock: number;
  committedStock: number;
  onHandStock: number;
  warehouseStocks: WarehouseStock[];
  lowStockThreshold?: number;
  trackQuantity: boolean;
  continueSellingWhenOutOfStock: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface VariantAttribute {
  attributeId: number;
  attributeName: string;
  valueId: number;
  value: string;
  color?: string;
}

export interface WarehouseStock {
  warehouseId: number;
  warehouseName: string;
  quantity: number;
  reserved: number;
  available: number;
  incoming: number;
  committed: number;
}

export interface InventoryAdjustment {
  id?: number;
  variantId: number;
  warehouseId: number;
  adjustmentType: 'INCREASE' | 'DECREASE' | 'SET' | 'TRANSFER';
  quantity: number;
  reason: string;
  notes?: string;
  referenceNumber?: string;
  createdBy?: string;
  createdAt?: string;
}

export interface StockTransfer {
  id?: number;
  fromWarehouseId: number;
  toWarehouseId: number;
  variantId: number;
  quantity: number;
  status: 'PENDING' | 'IN_TRANSIT' | 'COMPLETED' | 'CANCELLED';
  reason: string;
  notes?: string;
  transferDate?: string;
  completedDate?: string;
  createdBy?: string;
  createdAt?: string;
}

export interface InventoryListResponse {
  data: InventoryItem[];
  total: number;
  page: number;
  size: number;
}

export interface InventoryFilters {
  search?: string;
  warehouseId?: number;
  lowStock?: boolean;
  outOfStock?: boolean;
  trackQuantity?: boolean;
  productId?: number;
  brandId?: number;
  categoryId?: number;
}

export interface InventoryUpdateRequest {
  variantId: number;
  warehouseId: number;
  quantity: number;
  adjustmentType: 'INCREASE' | 'DECREASE' | 'SET';
  reason: string;
  notes?: string;
}

export interface BulkInventoryUpdateRequest {
  updates: InventoryUpdateRequest[];
  reason: string;
  notes?: string;
}

export interface InventorySettings {
  trackQuantity: boolean;
  continueSellingWhenOutOfStock: boolean;
  lowStockThreshold?: number;
}

export interface Warehouse {
  id: number;
  name: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
  contactNumber?: string;
  isActive: boolean;
}
