"use client";

import * as React from "react";
import { useRouter } from "next/navigation";
import PageContainer from '@/components/layout/page-container';
import { PageHeader } from '@/components/ui/page-header';
import { Separator } from '@/components/ui/separator';
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { OrderCreateForm } from "@/components/orders/order-create-form";
import { createOrder } from "@/lib/api/orders";
import { toast } from "sonner";

export default function CreateOrderPage() {
  const router = useRouter();
  const [loading, setLoading] = React.useState(false);

  const handleCreateOrder = async (orderData: any) => {
    try {
      setLoading(true);
      const newOrder = await createOrder(orderData);
      toast.success("Order created successfully");
      router.push(`/dashboard/orders/${newOrder.id}`);
    } catch (error) {
      toast.error("Failed to create order");
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageContainer>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push("/dashboard/orders")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Orders
          </Button>
          <PageHeader
            title="Create New Order"
            description="Create a new order manually"
          />
        </div>

        <Separator />

        {/* Order Creation Form */}
        <OrderCreateForm
          onSubmit={handleCreateOrder}
          loading={loading}
          onCancel={() => router.push("/dashboard/orders")}
        />
      </div>
    </PageContainer>
  );
}
