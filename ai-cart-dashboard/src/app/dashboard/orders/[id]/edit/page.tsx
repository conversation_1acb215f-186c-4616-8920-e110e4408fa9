"use client";

import * as React from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import PageContainer from '@/components/layout/page-container';
import { PageHeader } from '@/components/ui/page-header';
import { Separator } from '@/components/ui/separator';
import { Button } from "@/components/ui/button";
import { ArrowLeft, RefreshCw } from "lucide-react";
import { OrderEditForm } from "@/components/orders/order-edit-form";
import { getOrder, updateOrder } from "@/lib/api/orders";
import { OrderDetail } from "@/types/order";
import { toast } from "sonner";

export default function EditOrderPage() {
  const params = useParams();
  const router = useRouter();
  const orderId = parseInt(params.id as string);
  
  const [order, setOrder] = React.useState<OrderDetail | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [updating, setUpdating] = React.useState(false);

  // Load order details
  const loadOrder = React.useCallback(async () => {
    try {
      setLoading(true);
      const orderData = await getOrder(orderId);
      setOrder(orderData);
    } catch (error) {
      toast.error("Failed to load order details");
      router.push("/dashboard/orders");
    } finally {
      setLoading(false);
    }
  }, [orderId, router]);

  React.useEffect(() => {
    loadOrder();
  }, [loadOrder]);

  const handleUpdateOrder = async (updateData: any) => {
    try {
      setUpdating(true);
      const updatedOrder = await updateOrder(orderId, updateData);
      setOrder(updatedOrder);
      toast.success("Order updated successfully");
    } catch (error) {
      toast.error("Failed to update order");
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <PageContainer>
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      </PageContainer>
    );
  }

  if (!order) {
    return (
      <PageContainer>
        <div className="text-center">
          <h2 className="text-2xl font-bold">Order not found</h2>
          <Button onClick={() => router.push("/dashboard/orders")} className="mt-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Orders
          </Button>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(`/dashboard/orders/${orderId}`)}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Order
          </Button>
          <PageHeader
            title={`Edit Order #${order.id}`}
            description="Modify order details and items"
          />
        </div>

        <Separator />

        {/* Order Edit Form */}
        <OrderEditForm
          order={order}
          onSubmit={handleUpdateOrder}
          loading={updating}
          onCancel={() => router.push(`/dashboard/orders/${orderId}`)}
        />
      </div>
    </PageContainer>
  );
}
