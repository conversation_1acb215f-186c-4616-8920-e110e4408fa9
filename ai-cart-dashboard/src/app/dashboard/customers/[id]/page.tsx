import PageContainer from '@/components/layout/page-container';
import { PageHeader } from '@/components/ui/page-header';
import { Separator } from '@/components/ui/separator';
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { CustomerDetailInline } from "@/components/customers/customer-detail-inline";
import { CustomerActivityTimeline } from "@/components/customers/customer-activity-timeline";
import { CustomerOrdersTable } from "@/components/customers/customer-orders-table";
import { CustomerAddressesManager } from "@/components/customers/customer-addresses-manager";
import { CustomerContactInfo } from "@/components/customers/customer-contact-info";
import { CustomerMarketingSettings } from "@/components/customers/customer-marketing-settings";
import { CustomerTaxDetails } from "@/components/customers/customer-tax-details";
import { CustomerTags } from "@/components/customers/customer-tags";
import { CustomerNotes } from "@/components/customers/customer-notes";
import { CustomerStoreCredit } from "@/components/customers/customer-store-credit";
import { CustomerOverview } from "@/components/customers/customer-overview";
import { getCustomer } from "@/lib/api/customers";
import { CustomerDetail } from "@/types/customer";
import { notFound } from "next/navigation";
import Link from "next/link";
import { CustomerActionsServer } from "@/components/customers/customer-actions-server";

interface CustomerDetailPageProps {
  params: {
    id: string;
  };
}

export default async function CustomerDetailPage({ params }: CustomerDetailPageProps) {
  const resolvedParams = await params;
  const customerId = parseInt(resolvedParams.id);

  if (isNaN(customerId)) {
    notFound();
  }

  let customer: CustomerDetail;

  try {
    customer = await getCustomer(customerId);
  } catch (error) {
    notFound();
  }

  return (
    <PageContainer>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard/customers">
              <Button variant="outline" size="sm">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Customers
              </Button>
            </Link>
            <PageHeader
              title={`${customer.firstName} ${customer.lastName}`}
              description={customer.email}
            />
          </div>
          
          <div className="flex items-center space-x-2">
            <CustomerActionsServer customer={customer} />
          </div>
        </div>

        <Separator />

        {/* Customer Overview */}
        <CustomerOverview customer={customer} />

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Left Column */}
          <div className="lg:col-span-2 space-y-6">
            {/* Customer Details */}
            <CustomerDetailInline customer={customer} />

            {/* Customer Addresses */}
            <CustomerAddressesManager
              customerId={customer.id}
              initialAddresses={customer.addresses || []}
            />

            {/* Customer Orders */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Recent Orders</h3>
              <CustomerOrdersTable customerId={customer.id} />
            </div>

            {/* Customer Activity Timeline */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Activity Timeline</h3>
              <CustomerActivityTimeline customerId={customer.id} />
            </div>
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Contact Information */}
            <CustomerContactInfo customer={customer} />

            {/* Marketing Settings */}
            <CustomerMarketingSettings customer={customer} />

            {/* Tax Details */}
            <CustomerTaxDetails customer={customer} />

            {/* Store Credit */}
            <CustomerStoreCredit customerId={customer.id} />

            {/* Tags */}
            <CustomerTags customer={customer} />

            {/* Notes */}
            <CustomerNotes customer={customer} />
          </div>
        </div>
      </div>
    </PageContainer>
  );
}
