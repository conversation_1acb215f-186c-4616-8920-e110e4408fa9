import PageContainer from '@/components/layout/page-container';
import { <PERSON>Header } from '@/components/ui/page-header';
import { Separator } from '@/components/ui/separator';
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { CustomerCreateFormServer } from "@/components/customers/customer-create-form-server";
import { createCustomer } from "@/lib/api/customers";
import { CustomerCreateRequest } from "@/types/customer";
import { redirect } from "next/navigation";
import Link from "next/link";

// Server action for creating customer
async function createCustomerAction(formData: FormData) {
  "use server";

  const customerData: CustomerCreateRequest = {
    email: formData.get("email") as string,
    firstName: formData.get("firstName") as string,
    lastName: formData.get("lastName") as string,
    phone: formData.get("phone") as string,
    company: formData.get("company") as string,
    jobTitle: formData.get("jobTitle") as string,
    languageCode: formData.get("languageCode") as string || "en",
    currencyCode: formData.get("currencyCode") as string || "USD",
    timezone: formData.get("timezone") as string || "UTC",
    customerType: formData.get("customerType") as any || "REGULAR",
    customerTier: formData.get("customerTier") as any || "BRONZE",
    newsletterSubscribe: formData.get("newsletterSubscribe") === "on",
    emailSubscribe: formData.get("emailSubscribe") === "on",
    phoneSubscribe: formData.get("phoneSubscribe") === "on",
    smsSubscribe: formData.get("smsSubscribe") === "on",
    taxExempt: formData.get("taxExempt") === "on",
    sendWelcomeEmail: formData.get("sendWelcomeEmail") === "on",
    verifyEmail: formData.get("verifyEmail") === "on",
    notes: formData.get("notes") as string,
    tags: formData.get("tags") as string,
  };

  try {
    const newCustomer = await createCustomer(customerData);
    redirect(`/dashboard/customers/${newCustomer.id}`);
  } catch (error) {
    redirect("/dashboard/customers/create?error=creation-failed");
  }
}

export default function CreateCustomerPage() {

  return (
    <PageContainer>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/customers">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Customers
            </Button>
          </Link>
          <PageHeader
            title="Create New Customer"
            description="Add a new customer to your store"
          />
        </div>

        <Separator />

        {/* Standard Customer Creation Form */}
        <CustomerCreateFormServer action={createCustomerAction} />
      </div>
    </PageContainer>
  );
}
