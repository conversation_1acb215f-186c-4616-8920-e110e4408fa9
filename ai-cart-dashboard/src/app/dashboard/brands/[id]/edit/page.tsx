import { notFound } from 'next/navigation';
import PageContainer from '@/components/layout/page-container';
import BrandForm from '@/components/brand/brand-form';
import { fetchBrand } from '@/lib/actions/brand';

export const metadata = {
  title: 'Dashboard: Edit Brand'
}

interface PageProps {
  params: {
    id: string;
  };
}

export default async function Page({ params }: PageProps) {
  const brandId = parseInt(params.id);
  
  if (isNaN(brandId)) {
    notFound();
  }

  const brand = await fetchBrand(brandId);
  
  if (!brand) {
    notFound();
  }

  return (
    <PageContainer>
      <BrandForm
        initialData={brand}
        pageTitle={`Edit Brand: ${brand.name}`}
      />
    </PageContainer>
  );
}
