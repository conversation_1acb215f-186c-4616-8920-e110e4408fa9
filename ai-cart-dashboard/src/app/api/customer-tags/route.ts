import { NextRequest, NextResponse } from "next/server";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = searchParams.get("page") || "0";
    const size = searchParams.get("size") || "20";
    const sortField = searchParams.get("sortField") || "name";
    const ascending = searchParams.get("ascending") === "true";
    const searchQuery = searchParams.get("searchQuery") || "";

    const queryParams = new URLSearchParams({
      page,
      size,
      sortField,
      ascending: ascending.toString(),
      ...(searchQuery && { searchQuery }),
    });

    const response = await fetch(
      `${API_BASE_URL}/customer-tags?${queryParams}`,
      {
        headers: {
          "Shop-Id": request.headers.get("Shop-Id") || "1",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`API responded with status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching customer tags:", error);
    return NextResponse.json(
      { error: "Failed to fetch customer tags" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const response = await fetch(`${API_BASE_URL}/customer-tags`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Shop-Id": request.headers.get("Shop-Id") || "1",
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return NextResponse.json(
        { error: errorData.error || "Failed to create customer tag" },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error("Error creating customer tag:", error);
    return NextResponse.json(
      { error: "Failed to create customer tag" },
      { status: 500 }
    );
  }
}
