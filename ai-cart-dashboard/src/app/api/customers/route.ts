import { NextRequest, NextResponse } from 'next/server';
import { CustomerCreateRequest, CustomerListResponse } from '@/types/customer';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page') || '1';
    const size = searchParams.get('size') || '10';
    const search = searchParams.get('search') || '';
    const customerType = searchParams.get('customerType') || '';
    const customerTier = searchParams.get('customerTier') || '';
    const emailVerified = searchParams.get('emailVerified') || '';
    const accountLocked = searchParams.get('accountLocked') || '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const order = searchParams.get('order') || 'desc';

    // Build query parameters
    const queryParams = new URLSearchParams({
      page,
      size,
      sortBy,
      order,
    });

    if (search) queryParams.append('search', search);
    if (customerType) queryParams.append('customerType', customerType);
    if (customerTier) queryParams.append('customerTier', customerTier);
    if (emailVerified) queryParams.append('emailVerified', emailVerified);
    if (accountLocked) queryParams.append('accountLocked', accountLocked);

    const response = await fetch(`${API_BASE_URL}/customers?${queryParams}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Shop-Id': '1', // TODO: Get from session/context
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch customers: ${response.statusText}`);
    }

    const data: CustomerListResponse = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching customers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customers' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body: CustomerCreateRequest = await request.json();

    const response = await fetch(`${API_BASE_URL}/customers`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Shop-Id': '1', // TODO: Get from session/context
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `Failed to create customer: ${response.statusText}`);
    }

    const data = await response.json();
    return NextResponse.json(data, { status: 201 });
  } catch (error) {
    console.error('Error creating customer:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to create customer' },
      { status: 500 }
    );
  }
}
