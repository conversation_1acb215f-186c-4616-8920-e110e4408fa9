import { NextRequest, NextResponse } from "next/server";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const page = searchParams.get("page") || "0";
    const size = searchParams.get("size") || "20";

    const response = await fetch(
      `${API_BASE_URL}/api/customers/${params.id}/activity?page=${page}&size=${size}`,
      {
        headers: {
          "Shop-Id": request.headers.get("Shop-Id") || "1",
        },
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        // Return empty state if customer not found or no activity system
        return NextResponse.json({
          activities: [],
          total: 0,
          page: 0,
          size: 20,
        });
      }
      throw new Error(`API responded with status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching customer activity:", error);
    // Return empty state on error for now
    return NextResponse.json({
      activities: [],
      total: 0,
      page: 0,
      size: 20,
    });
  }
}
