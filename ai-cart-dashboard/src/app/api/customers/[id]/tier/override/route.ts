import { NextRequest, NextResponse } from "next/server";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080";

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();

    const response = await fetch(
      `${API_BASE_URL}/api/customers/${params.id}/tier/override`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Shop-Id": request.headers.get("Shop-Id") || "1",
        },
        body: JSON.stringify(body),
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return NextResponse.json(
        { error: errorData.error || "Failed to override customer tier" },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error overriding customer tier:", error);
    return NextResponse.json(
      { error: "Failed to override customer tier" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const updatedBy = searchParams.get("updated_by") || "system";

    const response = await fetch(
      `${API_BASE_URL}/api/customers/${params.id}/tier/override?updated_by=${updatedBy}`,
      {
        method: "DELETE",
        headers: {
          "Shop-Id": request.headers.get("Shop-Id") || "1",
        },
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return NextResponse.json(
        { error: errorData.error || "Failed to reset tier override" },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error resetting tier override:", error);
    return NextResponse.json(
      { error: "Failed to reset tier override" },
      { status: 500 }
    );
  }
}
