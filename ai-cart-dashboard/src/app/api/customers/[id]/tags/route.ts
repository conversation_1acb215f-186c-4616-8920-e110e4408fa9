import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const customerId = params.id;

    const response = await fetch(`${API_BASE_URL}/customers/${customerId}/tags`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Shop-Id': '1', // TODO: Get from session/context
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch customer tags: ${response.statusText}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching customer tags:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customer tags' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const customerId = params.id;
    const tagIds: number[] = await request.json();

    const response = await fetch(`${API_BASE_URL}/customers/${customerId}/tags`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Shop-Id': '1', // TODO: Get from session/context
      },
      body: JSON.stringify(tagIds),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `Failed to update customer tags: ${response.statusText}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error updating customer tags:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to update customer tags' },
      { status: 500 }
    );
  }
}
