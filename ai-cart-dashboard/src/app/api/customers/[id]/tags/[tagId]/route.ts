import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string; tagId: string } }
) {
  try {
    const customerId = params.id;
    const tagId = params.tagId;

    const response = await fetch(`${API_BASE_URL}/customers/${customerId}/tags/${tagId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Shop-Id': '1', // TODO: Get from session/context
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `Failed to add tag to customer: ${response.statusText}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error adding tag to customer:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to add tag to customer' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; tagId: string } }
) {
  try {
    const customerId = params.id;
    const tagId = params.tagId;

    const response = await fetch(`${API_BASE_URL}/customers/${customerId}/tags/${tagId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Shop-Id': '1', // TODO: Get from session/context
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.error || `Failed to remove tag from customer: ${response.statusText}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error removing tag from customer:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to remove tag from customer' },
      { status: 500 }
    );
  }
}
