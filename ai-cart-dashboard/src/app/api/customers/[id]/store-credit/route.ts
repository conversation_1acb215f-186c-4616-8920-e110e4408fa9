import { NextRequest, NextResponse } from "next/server";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const response = await fetch(
      `${API_BASE_URL}/api/customers/${params.id}/store-credit`,
      {
        headers: {
          "Shop-Id": request.headers.get("Shop-Id") || "1",
        },
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        // Return empty state if customer not found or no store credit system
        return NextResponse.json({
          current_balance: 0,
          transactions: [],
        });
      }
      throw new Error(`API responded with status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching customer store credit:", error);
    // Return empty state on error for now
    return NextResponse.json({
      current_balance: 0,
      transactions: [],
    });
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();

    const response = await fetch(
      `${API_BASE_URL}/api/customers/${params.id}/store-credit`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Shop-Id": request.headers.get("Shop-Id") || "1",
        },
        body: JSON.stringify(body),
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return NextResponse.json(
        { error: errorData.error || "Failed to update store credit" },
        { status: response.status }
      );
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error updating customer store credit:", error);
    return NextResponse.json(
      { error: "Failed to update store credit" },
      { status: 500 }
    );
  }
}
