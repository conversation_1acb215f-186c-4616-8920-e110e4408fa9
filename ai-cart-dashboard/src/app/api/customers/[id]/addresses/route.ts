import { NextRequest, NextResponse } from 'next/server';
import { CustomerAddressCreateRequest } from '@/types/customer';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';

// Helper function to make authenticated requests to backend
async function backendRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      'Shop-Id': '1', // TODO: Get from auth context
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(url, config);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
}

// GET /api/customers/[id]/addresses - Get all addresses for a customer
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const resolvedParams = await params;
    const customerId = parseInt(resolvedParams.id);

    if (isNaN(customerId)) {
      return NextResponse.json(
        { error: 'Invalid customer ID' },
        { status: 400 }
      );
    }

    const addresses = await backendRequest(`/customers/${customerId}/addresses`);
    return NextResponse.json(addresses);
  } catch (error) {
    console.error('Error fetching customer addresses:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customer addresses' },
      { status: 500 }
    );
  }
}

// POST /api/customers/[id]/addresses - Create a new address for a customer
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const resolvedParams = await params;
    const customerId = parseInt(resolvedParams.id);

    if (isNaN(customerId)) {
      return NextResponse.json(
        { error: 'Invalid customer ID' },
        { status: 400 }
      );
    }

    const addressData: CustomerAddressCreateRequest = await request.json();
    
    const newAddress = await backendRequest(`/customers/${customerId}/addresses`, {
      method: 'POST',
      body: JSON.stringify(addressData),
    });
    
    return NextResponse.json(newAddress, { status: 201 });
  } catch (error) {
    console.error('Error creating customer address:', error);
    return NextResponse.json(
      { error: 'Failed to create customer address' },
      { status: 500 }
    );
  }
}
