import { NextRequest, NextResponse } from 'next/server';
import { CustomerAddressUpdateRequest } from '@/types/customer';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';

// Helper function to make authenticated requests to backend
async function backendRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      'Shop-Id': '1', // TODO: Get from auth context
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(url, config);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
}

// GET /api/customers/[id]/addresses/[addressId] - Get a specific address
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string; addressId: string } }
) {
  try {
    const resolvedParams = await params;
    const customerId = parseInt(resolvedParams.id);
    const addressId = parseInt(resolvedParams.addressId);

    if (isNaN(customerId) || isNaN(addressId)) {
      return NextResponse.json(
        { error: 'Invalid customer ID or address ID' },
        { status: 400 }
      );
    }

    const address = await backendRequest(`/customers/${customerId}/addresses/${addressId}`);
    return NextResponse.json(address);
  } catch (error) {
    console.error('Error fetching customer address:', error);
    return NextResponse.json(
      { error: 'Failed to fetch customer address' },
      { status: 500 }
    );
  }
}

// PUT /api/customers/[id]/addresses/[addressId] - Update a specific address
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string; addressId: string } }
) {
  try {
    const resolvedParams = await params;
    const customerId = parseInt(resolvedParams.id);
    const addressId = parseInt(resolvedParams.addressId);

    if (isNaN(customerId) || isNaN(addressId)) {
      return NextResponse.json(
        { error: 'Invalid customer ID or address ID' },
        { status: 400 }
      );
    }

    const updateData: CustomerAddressUpdateRequest = await request.json();
    
    const updatedAddress = await backendRequest(`/customers/${customerId}/addresses/${addressId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
    
    return NextResponse.json(updatedAddress);
  } catch (error) {
    console.error('Error updating customer address:', error);
    return NextResponse.json(
      { error: 'Failed to update customer address' },
      { status: 500 }
    );
  }
}

// DELETE /api/customers/[id]/addresses/[addressId] - Delete a specific address
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string; addressId: string } }
) {
  try {
    const resolvedParams = await params;
    const customerId = parseInt(resolvedParams.id);
    const addressId = parseInt(resolvedParams.addressId);

    if (isNaN(customerId) || isNaN(addressId)) {
      return NextResponse.json(
        { error: 'Invalid customer ID or address ID' },
        { status: 400 }
      );
    }

    await backendRequest(`/customers/${customerId}/addresses/${addressId}`, {
      method: 'DELETE',
    });
    
    return NextResponse.json({ message: 'Address deleted successfully' });
  } catch (error) {
    console.error('Error deleting customer address:', error);
    return NextResponse.json(
      { error: 'Failed to delete customer address' },
      { status: 500 }
    );
  }
}
