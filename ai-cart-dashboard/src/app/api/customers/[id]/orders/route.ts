import { NextRequest, NextResponse } from "next/server";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url);
    const page = searchParams.get("page") || "0";
    const size = searchParams.get("size") || searchParams.get("limit") || "10";
    const sort = searchParams.get("sort") || "createdAt";
    const order = searchParams.get("order") || "desc";

    const queryParams = new URLSearchParams({
      page,
      size,
      sort,
      order,
    });

    const response = await fetch(
      `${API_BASE_URL}/api/customers/${params.id}/orders?${queryParams}`,
      {
        headers: {
          "Shop-Id": request.headers.get("Shop-Id") || "1",
        },
      }
    );

    if (!response.ok) {
      if (response.status === 404) {
        // Return empty state if customer not found or no orders
        return NextResponse.json({
          orders: [],
          total: 0,
          page: 0,
          size: parseInt(size),
        });
      }
      throw new Error(`API responded with status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching customer orders:", error);
    // Return empty state on error for now
    return NextResponse.json({
      orders: [],
      total: 0,
      page: 0,
      size: 10,
    });
  }
}
