"use server";

import { InventoryItem, InventoryUpdateRequest, InventoryResponse, InventoryFilters, Warehouse } from '@/types/inventory';
import { auth } from "~/auth";
import { revalidatePath } from 'next/cache';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';

async function request<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;

  const session = await auth();
  const accessToken = session?.accessToken;

  if (!accessToken) {
    throw new Error("Authentication required");
  }

  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      'Shop-Id': '1', // TODO: Get from auth context
      'Authorization': `Bearer ${accessToken}`,
      ...options.headers,
    },
    ...options,
  };

  console.log("warehouse url", url)

  const response = await fetch(url, config);

  console.log('Response:', response);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
}

export async function getInventoryItemsAction(
  page: number = 0,
  size: number = 20,
  sortField: string = 'productName',
  order: string = 'asc',
  filters: InventoryFilters = {}
): Promise<InventoryResponse> {
  const params = new URLSearchParams({
    page: page.toString(),
    size: size.toString(),
    sort: sortField,
    order,
  });

  // Add filters to params
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      params.append(key, value.toString());
    }
  });

  try {
    return await request<InventoryResponse>(`/inventory?${params}`);
  } catch (error) {
    console.error('Error fetching inventory items:', error);
    return { total: 0, data: [], size, page };
  }
}

export async function adjustInventoryAction(requestData: InventoryUpdateRequest): Promise<{ success: boolean; message: string }> {
  try {
    await request<void>('/inventory/adjust', {
      method: 'POST',
      body: JSON.stringify(requestData),
    });
    
    // Revalidate the inventory page to refresh data
    revalidatePath('/dashboard/inventory');
    
    return { success: true, message: 'Inventory adjusted successfully' };
  } catch (error) {
    console.error('Error adjusting inventory:', error);
    return { 
      success: false, 
      message: error instanceof Error ? error.message : 'Failed to adjust inventory' 
    };
  }
}

export async function getWarehousesAction(): Promise<Warehouse[]> {
  try {
    const data = await request<{ data: Warehouse[] }>('/warehouses');
    return data.data || [];
  } catch (error) {
    console.error('Error fetching warehouses:', error);
    return [];
  }
}

export async function getVariantInventoryAction(variantId: number): Promise<InventoryItem | null> {
  try {
    return await request<InventoryItem>(`/inventory/variant/${variantId}`);
  } catch (error) {
    console.error('Error fetching variant inventory:', error);
    return null;
  }
}

export async function getLowStockItemsAction(threshold: number = 10): Promise<InventoryItem[]> {
  try {
    const data = await request<{ data: InventoryItem[] }>(`/inventory/low-stock?threshold=${threshold}`);
    return data.data || [];
  } catch (error) {
    console.error('Error fetching low stock items:', error);
    return [];
  }
}

export async function getOutOfStockItemsAction(): Promise<InventoryItem[]> {
  try {
    const data = await request<{ data: InventoryItem[] }>('/inventory/out-of-stock');
    return data.data || [];
  } catch (error) {
    console.error('Error fetching out of stock items:', error);
    return [];
  }
}
