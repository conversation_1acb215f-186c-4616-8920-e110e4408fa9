"use server";

import { revalidatePath } from "next/cache";
import { CustomerDetail } from "@/types/customer";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080";

export async function updateCustomer(
  customerId: number,
  updateData: Partial<CustomerDetail>
): Promise<{ success: boolean; data?: CustomerDetail; error?: string }> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/customers/${customerId}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        "Shop-Id": "1", // TODO: Get from session/context
      },
      body: JSON.stringify(updateData),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        success: false,
        error: errorData.message || `Failed to update customer: ${response.status}`,
      };
    }

    const updatedCustomer = await response.json();
    
    // Revalidate the customer detail page
    revalidatePath(`/dashboard/customers/${customerId}`);
    
    return {
      success: true,
      data: updatedCustomer,
    };
  } catch (error) {
    console.error("Error updating customer:", error);
    return {
      success: false,
      error: "Network error occurred while updating customer",
    };
  }
}

export async function updateCustomerBasicInfo(
  customerId: number,
  data: {
    firstName?: string;
    lastName?: string;
    email?: string;
    phone?: string;
    company?: string;
    jobTitle?: string;
  }
): Promise<{ success: boolean; data?: CustomerDetail; error?: string }> {
  return updateCustomer(customerId, data);
}

export async function updateCustomerClassification(
  customerId: number,
  data: {
    customerType?: string;
    customerTier?: string;
    tags?: string;
  }
): Promise<{ success: boolean; data?: CustomerDetail; error?: string }> {
  return updateCustomer(customerId, data);
}

export async function updateCustomerPreferences(
  customerId: number,
  data: {
    languageCode?: string;
    currencyCode?: string;
    timezone?: string;
  }
): Promise<{ success: boolean; data?: CustomerDetail; error?: string }> {
  return updateCustomer(customerId, data);
}

export async function updateCustomerMarketing(
  customerId: number,
  data: {
    newsletterSubscribe?: boolean;
    emailSubscribe?: boolean;
    phoneSubscribe?: boolean;
    smsSubscribe?: boolean;
  }
): Promise<{ success: boolean; data?: CustomerDetail; error?: string }> {
  return updateCustomer(customerId, data);
}

export async function updateCustomerTax(
  customerId: number,
  data: {
    taxExempt?: boolean;
    taxExemptionReason?: string;
    vatNumber?: string;
    taxId?: string;
  }
): Promise<{ success: boolean; data?: CustomerDetail; error?: string }> {
  return updateCustomer(customerId, data);
}

export async function updateCustomerNotes(
  customerId: number,
  notes: string
): Promise<{ success: boolean; data?: CustomerDetail; error?: string }> {
  return updateCustomer(customerId, { notes });
}

export async function deleteCustomer(
  customerId: number
): Promise<{ success: boolean; error?: string }> {
  try {
    const response = await fetch(`${API_BASE_URL}/api/customers/${customerId}`, {
      method: "DELETE",
      headers: {
        "Shop-Id": "1", // TODO: Get from session/context
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        success: false,
        error: errorData.message || `Failed to delete customer: ${response.status}`,
      };
    }

    // Revalidate the customers list page
    revalidatePath("/dashboard/customers");
    
    return {
      success: true,
    };
  } catch (error) {
    console.error("Error deleting customer:", error);
    return {
      success: false,
      error: "Network error occurred while deleting customer",
    };
  }
}

export async function toggleCustomerStatus(
  customerId: number,
  accountLocked: boolean
): Promise<{ success: boolean; data?: CustomerDetail; error?: string }> {
  return updateCustomer(customerId, { accountLocked });
}

export async function verifyCustomerEmail(
  customerId: number
): Promise<{ success: boolean; data?: CustomerDetail; error?: string }> {
  return updateCustomer(customerId, { emailVerified: true });
}

export async function verifyCustomerPhone(
  customerId: number
): Promise<{ success: boolean; data?: CustomerDetail; error?: string }> {
  return updateCustomer(customerId, { phoneVerified: true });
}
