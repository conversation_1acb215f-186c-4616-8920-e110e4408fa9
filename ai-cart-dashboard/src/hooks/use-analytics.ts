'use client';

import { useState, useEffect } from 'react';
import { DashboardStats, ChartDataPoint, CategoryDataPoint, RecentOrder } from '@/types/analytics';

export function useDashboardStats(period: string = 'month') {
  const [data, setData] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/api/analytics/dashboard?period=${period}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch dashboard stats');
        }
        
        const stats = await response.json();
        setData(stats);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        console.error('Error fetching dashboard stats:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardStats();
  }, [period]);

  return { data, loading, error, refetch: () => fetchDashboardStats() };
}

export function useRevenueChart(period: string = 'month') {
  const [data, setData] = useState<ChartDataPoint[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRevenueChart = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/api/analytics/revenue?period=${period}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch revenue chart');
        }
        
        const chartData = await response.json();
        setData(chartData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        console.error('Error fetching revenue chart:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchRevenueChart();
  }, [period]);

  return { data, loading, error };
}

export function useCategoryAnalytics(period: string = 'month') {
  const [data, setData] = useState<CategoryDataPoint[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategoryAnalytics = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch(`/api/analytics/categories?period=${period}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch category analytics');
        }
        
        const categories = await response.json();
        setData(categories);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        console.error('Error fetching category analytics:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCategoryAnalytics();
  }, [period]);

  return { data, loading, error };
}

export function useRecentOrders() {
  const [data, setData] = useState<RecentOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchRecentOrders = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch('/api/analytics/recent-orders');
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch recent orders');
        }
        
        const orders = await response.json();
        setData(orders);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        console.error('Error fetching recent orders:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchRecentOrders();
  }, []);

  return { data, loading, error };
}
