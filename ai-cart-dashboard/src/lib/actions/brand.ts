"use server";

import { revalidatePath } from "next/cache";
import { Brand, BrandListResponse, CreateBrandRequest, UpdateBrandRequest } from "@/types/brand";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080";

export async function fetchBrands(params?: {
  page?: number;
  size?: number;
  sort?: string;
  order?: string;
  q?: string;
}): Promise<BrandListResponse> {
  try {
    const searchParams = new URLSearchParams();
    if (params?.page !== undefined) searchParams.set("page", params.page.toString());
    if (params?.size !== undefined) searchParams.set("size", params.size.toString());
    if (params?.sort) searchParams.set("sort", params.sort);
    if (params?.order) searchParams.set("order", params.order);
    if (params?.q) searchParams.set("q", params.q);

    const response = await fetch(`${API_BASE_URL}/brands?${searchParams}`, {
      headers: {
        "Shop-Id": "1",
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch brands: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching brands:", error);
    return {
      data: [],
      total: 0,
      page: 0,
      size: 10,
    };
  }
}

export async function fetchBrand(id: number): Promise<Brand | null> {
  try {
    const response = await fetch(`${API_BASE_URL}/brands/${id}`, {
      headers: {
        "Shop-Id": "1",
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch brand: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching brand:", error);
    return null;
  }
}

export async function createBrand(data: CreateBrandRequest): Promise<{ success: boolean; error?: string; data?: Brand }> {
  try {
    const response = await fetch(`${API_BASE_URL}/brands`, {
      method: "POST",
      headers: {
        "Shop-Id": "1",
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Failed to create brand: ${response.statusText}`);
    }

    const brand = await response.json();
    revalidatePath("/dashboard/brands");
    
    return { success: true, data: brand };
  } catch (error) {
    console.error("Error creating brand:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to create brand" 
    };
  }
}

export async function updateBrand(id: number, data: UpdateBrandRequest): Promise<{ success: boolean; error?: string; data?: Brand }> {
  try {
    const response = await fetch(`${API_BASE_URL}/brands/${id}`, {
      method: "PUT",
      headers: {
        "Shop-Id": "1",
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Failed to update brand: ${response.statusText}`);
    }

    const brand = await response.json();
    revalidatePath("/dashboard/brands");
    revalidatePath(`/dashboard/brands/${id}`);
    
    return { success: true, data: brand };
  } catch (error) {
    console.error("Error updating brand:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to update brand" 
    };
  }
}

export async function deleteBrand(id: number): Promise<{ success: boolean; error?: string }> {
  try {
    const response = await fetch(`${API_BASE_URL}/brands/${id}`, {
      method: "DELETE",
      headers: {
        "Shop-Id": "1",
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Failed to delete brand: ${response.statusText}`);
    }

    revalidatePath("/dashboard/brands");
    
    return { success: true };
  } catch (error) {
    console.error("Error deleting brand:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to delete brand" 
    };
  }
}
