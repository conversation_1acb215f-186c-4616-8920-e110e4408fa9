"use server";

import { revalidate<PERSON>ath } from "next/cache";
import { 
  InventoryItem, 
  InventoryListResponse, 
  InventoryFilters,
  InventoryUpdateRequest,
  BulkInventoryUpdateRequest,
  InventoryAdjustment,
  StockTransfer,
  Warehouse
} from "@/types/inventory";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080";

export async function fetchInventory(params?: {
  page?: number;
  size?: number;
  sort?: string;
  order?: string;
  filters?: InventoryFilters;
}): Promise<InventoryListResponse> {
  try {
    const searchParams = new URLSearchParams();
    if (params?.page !== undefined) searchParams.set("page", params.page.toString());
    if (params?.size !== undefined) searchParams.set("size", params.size.toString());
    if (params?.sort) searchParams.set("sort", params.sort);
    if (params?.order) searchParams.set("order", params.order);
    
    // Add filters
    if (params?.filters) {
      Object.entries(params.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.set(key, value.toString());
        }
      });
    }

    const response = await fetch(`${API_BASE_URL}/inventory?${searchParams}`, {
      headers: {
        "Shop-Id": "1",
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch inventory: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching inventory:", error);
    return {
      data: [],
      total: 0,
      page: 0,
      size: 10,
    };
  }
}

export async function fetchInventoryItem(variantId: number): Promise<InventoryItem | null> {
  try {
    const response = await fetch(`${API_BASE_URL}/inventory/variant/${variantId}`, {
      headers: {
        "Shop-Id": "1",
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      if (response.status === 404) {
        return null;
      }
      throw new Error(`Failed to fetch inventory item: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching inventory item:", error);
    return null;
  }
}

export async function updateInventory(data: InventoryUpdateRequest): Promise<{ success: boolean; error?: string }> {
  try {
    const response = await fetch(`${API_BASE_URL}/inventory/adjust`, {
      method: "POST",
      headers: {
        "Shop-Id": "1",
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Failed to update inventory: ${response.statusText}`);
    }

    revalidatePath("/dashboard/inventory");
    
    return { success: true };
  } catch (error) {
    console.error("Error updating inventory:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to update inventory" 
    };
  }
}

export async function bulkUpdateInventory(data: BulkInventoryUpdateRequest): Promise<{ success: boolean; error?: string }> {
  try {
    const response = await fetch(`${API_BASE_URL}/inventory/bulk-adjust`, {
      method: "POST",
      headers: {
        "Shop-Id": "1",
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Failed to bulk update inventory: ${response.statusText}`);
    }

    revalidatePath("/dashboard/inventory");
    
    return { success: true };
  } catch (error) {
    console.error("Error bulk updating inventory:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to bulk update inventory" 
    };
  }
}

export async function fetchInventoryAdjustments(variantId?: number, params?: {
  page?: number;
  size?: number;
}): Promise<{ data: InventoryAdjustment[]; total: number }> {
  try {
    const searchParams = new URLSearchParams();
    if (params?.page !== undefined) searchParams.set("page", params.page.toString());
    if (params?.size !== undefined) searchParams.set("size", params.size.toString());
    if (variantId) searchParams.set("variantId", variantId.toString());

    const response = await fetch(`${API_BASE_URL}/inventory/adjustments?${searchParams}`, {
      headers: {
        "Shop-Id": "1",
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch inventory adjustments: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching inventory adjustments:", error);
    return { data: [], total: 0 };
  }
}

export async function createStockTransfer(data: Omit<StockTransfer, 'id' | 'createdAt'>): Promise<{ success: boolean; error?: string; data?: StockTransfer }> {
  try {
    const response = await fetch(`${API_BASE_URL}/inventory/transfer`, {
      method: "POST",
      headers: {
        "Shop-Id": "1",
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `Failed to create stock transfer: ${response.statusText}`);
    }

    const transfer = await response.json();
    revalidatePath("/dashboard/inventory");
    
    return { success: true, data: transfer };
  } catch (error) {
    console.error("Error creating stock transfer:", error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to create stock transfer" 
    };
  }
}

export async function fetchWarehouses(): Promise<Warehouse[]> {
  try {
    const response = await fetch(`${API_BASE_URL}/warehouses`, {
      headers: {
        "Shop-Id": "1",
        "Content-Type": "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch warehouses: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data || [];
  } catch (error) {
    console.error("Error fetching warehouses:", error);
    return [];
  }
}
