import { 
  Customer, 
  CustomerDetail, 
  CustomerStats, 
  CustomerListResponse, 
  CustomerCreateRequest, 
  CustomerUpdateRequest,
  CustomerFilters 
} from '@/types/customer';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080';

// Helper function to make authenticated requests
async function request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      'Shop-Id': '1', // TODO: Get from auth context
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(url, config);

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
}

// Get customers with filters and pagination
export async function getCustomers(params: {
  page?: number;
  size?: number;
  search?: string;
  customerType?: string;
  customerTier?: string;
  emailVerified?: boolean;
  accountLocked?: boolean;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  order?: string;
}): Promise<CustomerListResponse> {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value.toString());
    }
  });

  return request<CustomerListResponse>(`/customers?${searchParams.toString()}`);
}

// Create a new customer
export async function createCustomer(customerData: CustomerCreateRequest): Promise<CustomerDetail> {
  return request<CustomerDetail>('/customers', {
    method: 'POST',
    body: JSON.stringify(customerData),
  });
}

// Get customer by ID
export async function getCustomer(id: number): Promise<CustomerDetail> {
  return request<CustomerDetail>(`/customers/${id}`);
}

// Update an existing customer
export async function updateCustomer(id: number, updateData: CustomerUpdateRequest): Promise<CustomerDetail> {
  return request<CustomerDetail>(`/customers/${id}`, {
    method: 'PUT',
    body: JSON.stringify(updateData),
  });
}

// Delete a customer
export async function deleteCustomer(id: number): Promise<void> {
  await request(`/customers/${id}`, {
    method: 'DELETE',
  });
}

// Get customer statistics
export async function getCustomerStats(): Promise<CustomerStats> {
  return request<CustomerStats>('/customers/stats');
}

// Export customers to CSV
export async function exportCustomers(filters: CustomerFilters): Promise<Blob> {
  const searchParams = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value.toString());
    }
  });

  const response = await fetch(`${API_BASE_URL}/customers/export?${searchParams.toString()}`, {
    headers: {
      'Shop-Id': '1', // TODO: Get from auth context
    },
  });

  if (!response.ok) {
    throw new Error(`Export failed: ${response.status}`);
  }

  return response.blob();
}

// Bulk update customer tiers
export async function bulkUpdateCustomerTiers(customerIds: number[], newTier: string, reason: string): Promise<void> {
  await request('/customers/bulk/tier', {
    method: 'PUT',
    body: JSON.stringify({
      customerIds,
      newTier,
      reason,
    }),
  });
}

// Search customers (simplified search)
export async function searchCustomers(query: string): Promise<Customer[]> {
  const response = await getCustomers({
    search: query,
    size: 10,
  });
  return response.customers;
}

// Get customer activity summary
export async function getCustomerActivity(id: number): Promise<any> {
  // This would typically return order history, activity logs, etc.
  // For now, we'll use the customer detail endpoint
  return getCustomer(id);
}

// Verify customer email
export async function verifyCustomerEmail(id: number): Promise<void> {
  await request(`/customers/${id}/verify-email`, {
    method: 'POST',
  });
}

// Lock/unlock customer account
export async function toggleCustomerLock(id: number, locked: boolean, reason?: string): Promise<CustomerDetail> {
  return request<CustomerDetail>(`/customers/${id}`, {
    method: 'PUT',
    body: JSON.stringify({
      accountLocked: locked,
      accountLockedReason: reason,
      updateReason: locked ? 'Account locked by admin' : 'Account unlocked by admin',
    }),
  });
}

// Update customer tier
export async function updateCustomerTier(id: number, tier: string, reason: string): Promise<CustomerDetail> {
  return request<CustomerDetail>(`/customers/${id}`, {
    method: 'PUT',
    body: JSON.stringify({
      customerTier: tier,
      updateReason: reason,
    }),
  });
}

// Update customer type
export async function updateCustomerType(id: number, type: string, reason: string): Promise<CustomerDetail> {
  return request<CustomerDetail>(`/customers/${id}`, {
    method: 'PUT',
    body: JSON.stringify({
      customerType: type,
      updateReason: reason,
    }),
  });
}

// Add customer tags
export async function updateCustomerTags(id: number, tags: string): Promise<CustomerDetail> {
  return request<CustomerDetail>(`/customers/${id}`, {
    method: 'PUT',
    body: JSON.stringify({
      tags,
      updateReason: 'Tags updated by admin',
    }),
  });
}

// Update customer notes
export async function updateCustomerNotes(id: number, notes: string): Promise<CustomerDetail> {
  return request<CustomerDetail>(`/customers/${id}`, {
    method: 'PUT',
    body: JSON.stringify({
      notes,
      updateReason: 'Notes updated by admin',
    }),
  });
}

// Email-only customer creation (for newsletter signup, lead generation)
export async function createCustomerFromEmail(emailData: {
  email: string;
  country?: string;
  city?: string;
  countryCode?: string;
  newsletterSubscribe?: boolean;
  emailSubscribe?: boolean;
  source?: string;
  campaign?: string;
  referrer?: string;
}): Promise<{
  message: string;
  customer: CustomerDetail;
  isNewCustomer: boolean;
}> {
  return request<{
    message: string;
    customer: CustomerDetail;
    isNewCustomer: boolean;
  }>('/customers/email-signup', {
    method: 'POST',
    body: JSON.stringify(emailData),
  });
}
