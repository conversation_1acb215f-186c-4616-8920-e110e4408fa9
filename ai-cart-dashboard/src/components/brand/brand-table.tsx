'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import Link from 'next/link';
import { MoreHorizontal, Edit, Trash, Plus, ExternalLink } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { AlertModal } from '@/components/modal/alert-modal';

import { Brand } from '@/types/brand';
import { deleteBrand } from '@/lib/actions/brand';

interface BrandTableProps {
  brands: Brand[];
}

export default function BrandTable({ brands }: BrandTableProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedBrand, setSelectedBrand] = useState<Brand | null>(null);

  const onDelete = async () => {
    if (!selectedBrand) return;

    setLoading(true);
    try {
      const result = await deleteBrand(selectedBrand.id);
      
      if (result.success) {
        toast.success('Brand deleted successfully');
        router.refresh();
      } else {
        toast.error(result.error || 'Failed to delete brand');
      }
    } catch (error) {
      toast.error('Failed to delete brand');
    } finally {
      setLoading(false);
      setDeleteModalOpen(false);
      setSelectedBrand(null);
    }
  };

  const handleDeleteClick = (brand: Brand) => {
    setSelectedBrand(brand);
    setDeleteModalOpen(true);
  };

  return (
    <>
      <AlertModal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        onConfirm={onDelete}
        loading={loading}
        title="Delete Brand"
        description={`Are you sure you want to delete "${selectedBrand?.name}"? This action cannot be undone.`}
      />

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Brands</CardTitle>
            <Link href="/dashboard/brands/new">
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Brand
              </Button>
            </Link>
          </div>
        </CardHeader>
        <CardContent>
          {brands.length === 0 ? (
            <div className="text-center py-8">
              <h3 className="text-lg font-semibold">No brands found</h3>
              <p className="text-muted-foreground mt-2">
                Create your first brand to organize your products.
              </p>
              <Link href="/dashboard/brands/new">
                <Button className="mt-4">
                  <Plus className="mr-2 h-4 w-4" />
                  Create Brand
                </Button>
              </Link>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Website</TableHead>
                  <TableHead>Products</TableHead>
                  <TableHead className="w-[70px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {brands.map((brand) => (
                  <TableRow key={brand.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        {brand.logo && (
                          <img 
                            src={brand.logo} 
                            alt={brand.name}
                            className="w-8 h-8 rounded object-cover"
                          />
                        )}
                        <span className="font-medium">{brand.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-muted-foreground">
                        {brand.description || 'No description'}
                      </span>
                    </TableCell>
                    <TableCell>
                      {brand.website ? (
                        <a 
                          href={brand.website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
                        >
                          <ExternalLink className="h-3 w-3" />
                          Visit
                        </a>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <span className="text-muted-foreground">0 products</span>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/dashboard/brands/${brand.id}/edit`}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteClick(brand)}
                            className="text-red-600"
                          >
                            <Trash className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </>
  );
}
