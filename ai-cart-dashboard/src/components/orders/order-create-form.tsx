"use client";

import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Plus, Trash2 } from "lucide-react";

interface OrderCreateFormProps {
  onSubmit: (orderData: any) => void;
  loading: boolean;
  onCancel: () => void;
}

export function OrderCreateForm({ onSubmit, loading, onCancel }: OrderCreateFormProps) {
  const [formData, setFormData] = React.useState({
    customerInfo: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
    },
    billing: {
      fullName: "",
      email: "",
      phone: "",
      line1: "",
      line2: "",
      city: "",
      state: "",
      country: "",
      postalCode: "",
    },
    shipping: {
      fullName: "",
      phone: "",
      line1: "",
      line2: "",
      city: "",
      state: "",
      country: "",
      postalCode: "",
    },
    items: [
      {
        productId: "",
        variantId: "",
        quantity: 1,
        price: 0,
        tax: 0,
        discount: 0,
      },
    ],
    subTotal: 0,
    totalDiscount: 0,
    shippingCost: 0,
    totalTax: 0,
    totalPrice: 0,
    currency: "USD",
    paymentType: "CASH_ON_DELIVERY",
    notes: "",
    customerNotes: "",
    shippingMethod: "",
    deliveryInstructions: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Convert string values to appropriate types
    const orderData = {
      ...formData,
      items: formData.items.map(item => ({
        ...item,
        productId: parseInt(item.productId) || null,
        variantId: parseInt(item.variantId) || null,
        price: Math.round(item.price * 100), // Convert to cents
        tax: Math.round(item.tax * 100),
        discount: Math.round(item.discount * 100),
      })),
      subTotal: Math.round(formData.subTotal * 100),
      totalDiscount: Math.round(formData.totalDiscount * 100),
      shippingCost: Math.round(formData.shippingCost * 100),
      totalTax: Math.round(formData.totalTax * 100),
      totalPrice: Math.round(formData.totalPrice * 100),
    };

    onSubmit(orderData);
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [
        ...prev.items,
        {
          productId: "",
          variantId: "",
          quantity: 1,
          price: 0,
          tax: 0,
          discount: 0,
        },
      ],
    }));
  };

  const removeItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index),
    }));
  };

  const updateItem = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      ),
    }));
  };

  // Calculate totals when items change
  React.useEffect(() => {
    const subTotal = formData.items.reduce((sum, item) => {
      return sum + (item.price * item.quantity);
    }, 0);
    
    const totalTax = formData.items.reduce((sum, item) => sum + item.tax, 0);
    const totalDiscount = formData.items.reduce((sum, item) => sum + item.discount, 0);
    const totalPrice = subTotal + totalTax + formData.shippingCost - totalDiscount;

    setFormData(prev => ({
      ...prev,
      subTotal,
      totalTax,
      totalPrice,
    }));
  }, [formData.items, formData.shippingCost]);

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Customer Information */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Information</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              value={formData.customerInfo.firstName}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                customerInfo: { ...prev.customerInfo, firstName: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              value={formData.customerInfo.lastName}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                customerInfo: { ...prev.customerInfo, lastName: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email *</Label>
            <Input
              id="email"
              type="email"
              required
              value={formData.customerInfo.email}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                customerInfo: { ...prev.customerInfo, email: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              value={formData.customerInfo.phone}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                customerInfo: { ...prev.customerInfo, phone: e.target.value }
              }))}
            />
          </div>
        </CardContent>
      </Card>

      {/* Billing Address */}
      <Card>
        <CardHeader>
          <CardTitle>Billing Address</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="billingFullName">Full Name *</Label>
            <Input
              id="billingFullName"
              required
              value={formData.billing.fullName}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, fullName: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billingEmail">Email *</Label>
            <Input
              id="billingEmail"
              type="email"
              required
              value={formData.billing.email}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, email: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billingPhone">Phone</Label>
            <Input
              id="billingPhone"
              value={formData.billing.phone}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, phone: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="billingLine1">Address Line 1 *</Label>
            <Input
              id="billingLine1"
              required
              value={formData.billing.line1}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, line1: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="billingLine2">Address Line 2</Label>
            <Input
              id="billingLine2"
              value={formData.billing.line2}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, line2: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billingCity">City *</Label>
            <Input
              id="billingCity"
              required
              value={formData.billing.city}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, city: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billingState">State *</Label>
            <Input
              id="billingState"
              required
              value={formData.billing.state}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, state: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billingCountry">Country *</Label>
            <Input
              id="billingCountry"
              required
              value={formData.billing.country}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, country: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billingPostalCode">Postal Code *</Label>
            <Input
              id="billingPostalCode"
              required
              value={formData.billing.postalCode}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, postalCode: e.target.value }
              }))}
            />
          </div>
        </CardContent>
      </Card>

      {/* Order Items */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Order Items</CardTitle>
            <Button type="button" variant="outline" size="sm" onClick={addItem}>
              <Plus className="mr-2 h-4 w-4" />
              Add Item
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {formData.items.map((item, index) => (
            <div key={index} className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium">Item {index + 1}</h4>
                {formData.items.length > 1 && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => removeItem(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label>Product ID *</Label>
                  <Input
                    type="number"
                    required
                    value={item.productId}
                    onChange={(e) => updateItem(index, 'productId', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Variant ID *</Label>
                  <Input
                    type="number"
                    required
                    value={item.variantId}
                    onChange={(e) => updateItem(index, 'variantId', e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Quantity *</Label>
                  <Input
                    type="number"
                    min="1"
                    required
                    value={item.quantity}
                    onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 1)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Price *</Label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    required
                    value={item.price}
                    onChange={(e) => updateItem(index, 'price', parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Tax</Label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={item.tax}
                    onChange={(e) => updateItem(index, 'tax', parseFloat(e.target.value) || 0)}
                  />
                </div>
                <div className="space-y-2">
                  <Label>Discount</Label>
                  <Input
                    type="number"
                    step="0.01"
                    min="0"
                    value={item.discount}
                    onChange={(e) => updateItem(index, 'discount', parseFloat(e.target.value) || 0)}
                  />
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Order Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Order Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label>Subtotal</Label>
              <Input
                type="number"
                step="0.01"
                value={formData.subTotal}
                readOnly
                className="bg-gray-50"
              />
            </div>
            <div className="space-y-2">
              <Label>Shipping Cost</Label>
              <Input
                type="number"
                step="0.01"
                min="0"
                value={formData.shippingCost}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  shippingCost: parseFloat(e.target.value) || 0
                }))}
              />
            </div>
            <div className="space-y-2">
              <Label>Total Tax</Label>
              <Input
                type="number"
                step="0.01"
                value={formData.totalTax}
                readOnly
                className="bg-gray-50"
              />
            </div>
            <div className="space-y-2">
              <Label>Total Price</Label>
              <Input
                type="number"
                step="0.01"
                value={formData.totalPrice}
                readOnly
                className="bg-gray-50 font-bold"
              />
            </div>
          </div>
          
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label>Payment Type</Label>
              <Select
                value={formData.paymentType}
                onValueChange={(value) => setFormData(prev => ({ ...prev, paymentType: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="CASH_ON_DELIVERY">Cash on Delivery</SelectItem>
                  <SelectItem value="STRIPE">Stripe</SelectItem>
                  <SelectItem value="SSLCOMMERZ">SSLCommerz</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Currency</Label>
              <Select
                value={formData.currency}
                onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USD">USD</SelectItem>
                  <SelectItem value="EUR">EUR</SelectItem>
                  <SelectItem value="GBP">GBP</SelectItem>
                  <SelectItem value="BDT">BDT</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Admin Notes</Label>
            <Textarea
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              placeholder="Internal notes about this order..."
            />
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? "Creating..." : "Create Order"}
        </Button>
      </div>
    </form>
  );
}
