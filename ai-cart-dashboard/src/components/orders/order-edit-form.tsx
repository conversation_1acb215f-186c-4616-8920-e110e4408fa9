"use client";

import * as React from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { OrderDetail } from "@/types/order";
import { formatCurrency } from "@/lib/utils";

interface OrderEditFormProps {
  order: OrderDetail;
  onSubmit: (updateData: any) => void;
  loading: boolean;
  onCancel: () => void;
}

export function OrderEditForm({ order, onSubmit, loading, onCancel }: OrderEditFormProps) {
  const [formData, setFormData] = React.useState({
    customerInfo: {
      firstName: order.customer?.firstName || "",
      lastName: order.customer?.lastName || "",
      email: order.customer?.email || order.billing?.email || "",
      phone: order.customer?.phone || order.billing?.phone || "",
    },
    billing: {
      fullName: order.billing?.fullName || "",
      email: order.billing?.email || "",
      phone: order.billing?.phone || "",
      line1: order.billing?.line1 || "",
      line2: order.billing?.line2 || "",
      city: order.billing?.city || "",
      state: order.billing?.state || "",
      country: order.billing?.country || "",
      postalCode: order.billing?.postalCode || "",
    },
    shipping: {
      fullName: order.shipping?.fullName || "",
      phone: order.shipping?.phone || "",
      line1: order.shipping?.line1 || "",
      line2: order.shipping?.line2 || "",
      city: order.shipping?.city || "",
      state: order.shipping?.state || "",
      country: order.shipping?.country || "",
      postalCode: order.shipping?.postalCode || "",
    },
    notes: order.notes || "",
    customerNotes: order.customerNotes || "",
    shippingMethod: order.shippingMethod || "",
    deliveryInstructions: order.deliveryInstructions || "",
    updateReason: "",
    notifyCustomer: false,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Only send fields that have changed
    const updateData: any = {};
    
    if (formData.updateReason) {
      updateData.updateReason = formData.updateReason;
    }
    
    updateData.notifyCustomer = formData.notifyCustomer;
    
    // Check if customer info changed
    const customerChanged = 
      formData.customerInfo.firstName !== (order.customer?.firstName || "") ||
      formData.customerInfo.lastName !== (order.customer?.lastName || "") ||
      formData.customerInfo.email !== (order.customer?.email || order.billing?.email || "") ||
      formData.customerInfo.phone !== (order.customer?.phone || order.billing?.phone || "");
    
    if (customerChanged) {
      updateData.customerInfo = formData.customerInfo;
    }
    
    // Check if billing changed
    const billingChanged = 
      formData.billing.fullName !== (order.billing?.fullName || "") ||
      formData.billing.email !== (order.billing?.email || "") ||
      formData.billing.line1 !== (order.billing?.line1 || "") ||
      formData.billing.city !== (order.billing?.city || "");
    
    if (billingChanged) {
      updateData.billing = formData.billing;
    }
    
    // Check if shipping changed
    const shippingChanged = 
      formData.shipping.fullName !== (order.shipping?.fullName || "") ||
      formData.shipping.line1 !== (order.shipping?.line1 || "") ||
      formData.shipping.city !== (order.shipping?.city || "");
    
    if (shippingChanged) {
      updateData.shipping = formData.shipping;
    }
    
    // Check if other fields changed
    if (formData.notes !== (order.notes || "")) {
      updateData.notes = formData.notes;
    }
    
    if (formData.customerNotes !== (order.customerNotes || "")) {
      updateData.customerNotes = formData.customerNotes;
    }
    
    if (formData.shippingMethod !== (order.shippingMethod || "")) {
      updateData.shippingMethod = formData.shippingMethod;
    }
    
    if (formData.deliveryInstructions !== (order.deliveryInstructions || "")) {
      updateData.deliveryInstructions = formData.deliveryInstructions;
    }

    onSubmit(updateData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Order Status Warning */}
      {!["PENDING", "CONFIRMED", "PROCESSING"].includes(order.status) && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-800">
            <strong>Warning:</strong> This order is in {order.status} status. 
            Some changes may not be possible or may require special handling.
          </p>
        </div>
      )}

      {/* Update Reason */}
      <Card>
        <CardHeader>
          <CardTitle>Update Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="updateReason">Reason for Update *</Label>
            <Textarea
              id="updateReason"
              required
              value={formData.updateReason}
              onChange={(e) => setFormData(prev => ({ ...prev, updateReason: e.target.value }))}
              placeholder="Explain why this order is being updated..."
            />
          </div>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="notifyCustomer"
              checked={formData.notifyCustomer}
              onChange={(e) => setFormData(prev => ({ ...prev, notifyCustomer: e.target.checked }))}
            />
            <Label htmlFor="notifyCustomer">Notify customer of changes</Label>
          </div>
        </CardContent>
      </Card>

      {/* Customer Information */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Information</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              value={formData.customerInfo.firstName}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                customerInfo: { ...prev.customerInfo, firstName: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              value={formData.customerInfo.lastName}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                customerInfo: { ...prev.customerInfo, lastName: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.customerInfo.email}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                customerInfo: { ...prev.customerInfo, email: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              value={formData.customerInfo.phone}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                customerInfo: { ...prev.customerInfo, phone: e.target.value }
              }))}
            />
          </div>
        </CardContent>
      </Card>

      {/* Billing Address */}
      <Card>
        <CardHeader>
          <CardTitle>Billing Address</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="billingFullName">Full Name</Label>
            <Input
              id="billingFullName"
              value={formData.billing.fullName}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, fullName: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billingEmail">Email</Label>
            <Input
              id="billingEmail"
              type="email"
              value={formData.billing.email}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, email: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billingPhone">Phone</Label>
            <Input
              id="billingPhone"
              value={formData.billing.phone}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, phone: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="billingLine1">Address Line 1</Label>
            <Input
              id="billingLine1"
              value={formData.billing.line1}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, line1: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="billingLine2">Address Line 2</Label>
            <Input
              id="billingLine2"
              value={formData.billing.line2}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, line2: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billingCity">City</Label>
            <Input
              id="billingCity"
              value={formData.billing.city}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, city: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billingState">State</Label>
            <Input
              id="billingState"
              value={formData.billing.state}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, state: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billingCountry">Country</Label>
            <Input
              id="billingCountry"
              value={formData.billing.country}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, country: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billingPostalCode">Postal Code</Label>
            <Input
              id="billingPostalCode"
              value={formData.billing.postalCode}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                billing: { ...prev.billing, postalCode: e.target.value }
              }))}
            />
          </div>
        </CardContent>
      </Card>

      {/* Shipping Address */}
      <Card>
        <CardHeader>
          <CardTitle>Shipping Address</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="shippingFullName">Full Name</Label>
            <Input
              id="shippingFullName"
              value={formData.shipping.fullName}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                shipping: { ...prev.shipping, fullName: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="shippingPhone">Phone</Label>
            <Input
              id="shippingPhone"
              value={formData.shipping.phone}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                shipping: { ...prev.shipping, phone: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="shippingMethod">Shipping Method</Label>
            <Input
              id="shippingMethod"
              value={formData.shippingMethod}
              onChange={(e) => setFormData(prev => ({ ...prev, shippingMethod: e.target.value }))}
            />
          </div>
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="shippingLine1">Address Line 1</Label>
            <Input
              id="shippingLine1"
              value={formData.shipping.line1}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                shipping: { ...prev.shipping, line1: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="shippingLine2">Address Line 2</Label>
            <Input
              id="shippingLine2"
              value={formData.shipping.line2}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                shipping: { ...prev.shipping, line2: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="shippingCity">City</Label>
            <Input
              id="shippingCity"
              value={formData.shipping.city}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                shipping: { ...prev.shipping, city: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="shippingState">State</Label>
            <Input
              id="shippingState"
              value={formData.shipping.state}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                shipping: { ...prev.shipping, state: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="shippingCountry">Country</Label>
            <Input
              id="shippingCountry"
              value={formData.shipping.country}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                shipping: { ...prev.shipping, country: e.target.value }
              }))}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="shippingPostalCode">Postal Code</Label>
            <Input
              id="shippingPostalCode"
              value={formData.shipping.postalCode}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                shipping: { ...prev.shipping, postalCode: e.target.value }
              }))}
            />
          </div>
        </CardContent>
      </Card>

      {/* Order Notes */}
      <Card>
        <CardHeader>
          <CardTitle>Order Notes</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="notes">Admin Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              placeholder="Internal notes about this order..."
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="customerNotes">Customer Notes</Label>
            <Textarea
              id="customerNotes"
              value={formData.customerNotes}
              onChange={(e) => setFormData(prev => ({ ...prev, customerNotes: e.target.value }))}
              placeholder="Customer's notes or special requests..."
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="deliveryInstructions">Delivery Instructions</Label>
            <Textarea
              id="deliveryInstructions"
              value={formData.deliveryInstructions}
              onChange={(e) => setFormData(prev => ({ ...prev, deliveryInstructions: e.target.value }))}
              placeholder="Special delivery instructions..."
            />
          </div>
        </CardContent>
      </Card>

      {/* Current Order Summary (Read-only) */}
      <Card>
        <CardHeader>
          <CardTitle>Current Order Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Label>Subtotal</Label>
              <div className="text-lg font-medium">{formatCurrency(order.subTotal, order.currency)}</div>
            </div>
            <div>
              <Label>Shipping</Label>
              <div className="text-lg font-medium">{formatCurrency(order.shippingCost, order.currency)}</div>
            </div>
            <div>
              <Label>Tax</Label>
              <div className="text-lg font-medium">{formatCurrency(order.totalTax, order.currency)}</div>
            </div>
            <div>
              <Label>Total</Label>
              <div className="text-xl font-bold">{formatCurrency(order.totalPrice, order.currency)}</div>
            </div>
          </div>
          <div className="mt-4 text-sm text-muted-foreground">
            <p><strong>Note:</strong> To modify order items or pricing, please use the specific order management tools or contact system administrator.</p>
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? "Updating..." : "Update Order"}
        </Button>
      </div>
    </form>
  );
}
