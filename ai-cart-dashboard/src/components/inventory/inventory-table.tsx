"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Search, Package, AlertTriangle, TrendingDown } from "lucide-react";
import { InventoryItem, InventoryFilters, Warehouse } from "@/types/inventory";
import { getInventoryItemsAction, getWarehousesAction } from "@/app/actions/inventory";
import { StockAdjustmentDialog } from "./stock-adjustment-dialog";

interface InventoryTableProps {
  onItemSelect?: (item: InventoryItem) => void;
}

export function InventoryTable({ onItemSelect }: InventoryTableProps) {
  const [items, setItems] = useState<InventoryItem[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [loading, setLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(0);
  const [size] = useState(20);
  const [sortField, setSortField] = useState("productName");
  const [sortOrder, setSortOrder] = useState("asc");
  const [filters, setFilters] = useState<InventoryFilters>({});
  const [selectedItem, setSelectedItem] = useState<InventoryItem | null>(null);
  const [adjustmentDialogOpen, setAdjustmentDialogOpen] = useState(false);

  useEffect(() => {
    loadWarehouses();
  }, []);

  useEffect(() => {
    loadInventoryItems();
  }, [page, sortField, sortOrder, filters]);

  const loadWarehouses = async () => {
    try {
      const warehouseData = await getWarehousesAction();
      setWarehouses(warehouseData);
    } catch (error) {
      console.error("Failed to load warehouses:", error);
    }
  };

  const loadInventoryItems = async () => {
    try {
      setLoading(true);
      const response = await getInventoryItemsAction(page, size, sortField, sortOrder, filters);
      setItems(response.data);
      setTotal(response.total);
    } catch (error) {
      console.error("Failed to load inventory items:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key: keyof InventoryFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === "" ? undefined : value
    }));
    setPage(0);
  };

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortOrder("asc");
    }
    setPage(0);
  };

  const getStockStatus = (item: InventoryItem) => {
    if (item.availableStock === 0) {
      return { label: "Out of Stock", variant: "destructive" as const, icon: AlertTriangle };
    } else if (item.availableStock < 10) {
      return { label: "Low Stock", variant: "secondary" as const, icon: TrendingDown };
    }
    return { label: "In Stock", variant: "default" as const, icon: Package };
  };

  const handleAdjustStock = (item: InventoryItem) => {
    setSelectedItem(item);
    setAdjustmentDialogOpen(true);
  };

  const handleAdjustmentComplete = () => {
    setAdjustmentDialogOpen(false);
    setSelectedItem(null);
    loadInventoryItems(); // Refresh the data
  };

  if (loading && items.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading inventory...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Inventory Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search products..."
                value={filters.search || ""}
                onChange={(e) => handleFilterChange("search", e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select
              value={filters.warehouseId?.toString() || ""}
              onValueChange={(value) => handleFilterChange("warehouseId", value ? parseInt(value) : undefined)}
            >
              <SelectTrigger>
                <SelectValue placeholder="All Warehouses" />
              </SelectTrigger>
              <SelectContent>
                {warehouses.map((warehouse) => (
                  <SelectItem key={warehouse.id} value={warehouse.id.toString()}>
                    {warehouse.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={filters.lowStock ? "true" : filters.outOfStock ? "out" : ""}
              onValueChange={(value) => {
                if (value === "true") {
                  handleFilterChange("lowStock", true);
                  handleFilterChange("outOfStock", undefined);
                } else if (value === "out") {
                  handleFilterChange("outOfStock", true);
                  handleFilterChange("lowStock", undefined);
                } else {
                  handleFilterChange("lowStock", undefined);
                  handleFilterChange("outOfStock", undefined);
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Stock Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="true">Low Stock</SelectItem>
                <SelectItem value="out">Out of Stock</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              onClick={() => {
                setFilters({});
                setPage(0);
              }}
            >
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Inventory Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort("productName")}
                >
                  Product {sortField === "productName" && (sortOrder === "asc" ? "↑" : "↓")}
                </TableHead>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort("variantSku")}
                >
                  SKU {sortField === "variantSku" && (sortOrder === "asc" ? "↑" : "↓")}
                </TableHead>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => handleSort("currentStock")}
                >
                  Current Stock {sortField === "currentStock" && (sortOrder === "asc" ? "↑" : "↓")}
                </TableHead>
                <TableHead>Reserved</TableHead>
                <TableHead>Available</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {items.map((item) => {
                const status = getStockStatus(item);
                const StatusIcon = status.icon;
                
                return (
                  <TableRow 
                    key={`${item.variantId}-${item.productId}`}
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => onItemSelect?.(item)}
                  >
                    <TableCell className="font-medium">
                      {item.productName}
                    </TableCell>
                    <TableCell>{item.variantSku}</TableCell>
                    <TableCell>{item.currentStock}</TableCell>
                    <TableCell>{item.reservedStock}</TableCell>
                    <TableCell>{item.availableStock}</TableCell>
                    <TableCell>
                      <Badge variant={status.variant} className="flex items-center gap-1 w-fit">
                        <StatusIcon className="h-3 w-3" />
                        {status.label}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAdjustStock(item);
                        }}
                      >
                        Adjust Stock
                      </Button>
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
          
          {items.length === 0 && !loading && (
            <div className="text-center py-8 text-muted-foreground">
              No inventory items found
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {total > size && (
        <div className="flex justify-between items-center">
          <div className="text-sm text-muted-foreground">
            Showing {page * size + 1} to {Math.min((page + 1) * size, total)} of {total} items
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              disabled={page === 0}
              onClick={() => setPage(page - 1)}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={(page + 1) * size >= total}
              onClick={() => setPage(page + 1)}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Stock Adjustment Dialog */}
      {selectedItem && (
        <StockAdjustmentDialog
          open={adjustmentDialogOpen}
          onOpenChange={setAdjustmentDialogOpen}
          item={selectedItem}
          warehouses={warehouses}
          onComplete={handleAdjustmentComplete}
        />
      )}
    </div>
  );
}
