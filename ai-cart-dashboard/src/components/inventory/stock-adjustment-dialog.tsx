"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Package, AlertTriangle, TrendingDown } from "lucide-react";
import { InventoryItem, Warehouse, InventoryUpdateRequest } from "@/types/inventory";
import { adjustInventoryAction } from "@/app/actions/inventory";
import { toast } from "sonner";

const adjustmentSchema = z.object({
  warehouseId: z.string().min(1, "Please select a warehouse"),
  adjustmentType: z.enum(["SET", "INCREASE", "DECREASE"]),
  quantity: z.number().min(0, "Quantity must be positive"),
  reason: z.string().min(1, "Please provide a reason"),
  notes: z.string().optional(),
});

type AdjustmentFormData = z.infer<typeof adjustmentSchema>;

interface StockAdjustmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  item: InventoryItem;
  warehouses: Warehouse[];
  onComplete: () => void;
}

export function StockAdjustmentDialog({
  open,
  onOpenChange,
  item,
  warehouses,
  onComplete,
}: StockAdjustmentDialogProps) {
  const [loading, setLoading] = useState(false);

  const form = useForm<AdjustmentFormData>({
    resolver: zodResolver(adjustmentSchema),
    defaultValues: {
      warehouseId: "",
      adjustmentType: "SET",
      quantity: 0,
      reason: "",
      notes: "",
    },
  });

  const getStockStatus = (item: InventoryItem) => {
    if (item.availableStock === 0) {
      return { label: "Out of Stock", variant: "destructive" as const, icon: AlertTriangle };
    } else if (item.availableStock < 10) {
      return { label: "Low Stock", variant: "secondary" as const, icon: TrendingDown };
    }
    return { label: "In Stock", variant: "default" as const, icon: Package };
  };

  const onSubmit = async (data: AdjustmentFormData) => {
    try {
      setLoading(true);

      const request: InventoryUpdateRequest = {
        variantId: item.variantId,
        warehouseId: parseInt(data.warehouseId),
        quantity: data.quantity,
        adjustmentType: data.adjustmentType,
        reason: data.reason,
        notes: data.notes,
      };

      const result = await adjustInventoryAction(request);

      if (result.success) {
        toast.success(result.message);
        onComplete();
        form.reset();
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Failed to adjust stock:", error);
      toast.error(error instanceof Error ? error.message : "Failed to adjust stock");
    } finally {
      setLoading(false);
    }
  };

  const status = getStockStatus(item);
  const StatusIcon = status.icon;

  const adjustmentType = form.watch("adjustmentType");
  const quantity = form.watch("quantity");

  const calculateNewStock = () => {
    const currentStock = item.currentStock;
    switch (adjustmentType) {
      case "SET":
        return quantity;
      case "INCREASE":
        return currentStock + quantity;
      case "DECREASE":
        return Math.max(0, currentStock - quantity);
      default:
        return currentStock;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Adjust Stock</DialogTitle>
          <DialogDescription>
            Adjust inventory levels for this product variant
          </DialogDescription>
        </DialogHeader>

        {/* Product Info */}
        <div className="space-y-3 p-4 bg-muted/50 rounded-lg">
          <div className="flex justify-between items-start">
            <div>
              <h4 className="font-medium">{item.productName}</h4>
              <p className="text-sm text-muted-foreground">SKU: {item.variantSku}</p>
            </div>
            <Badge variant={status.variant} className="flex items-center gap-1">
              <StatusIcon className="h-3 w-3" />
              {status.label}
            </Badge>
          </div>
          
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Current Stock</span>
              <p className="font-medium">{item.currentStock}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Reserved</span>
              <p className="font-medium">{item.reservedStock}</p>
            </div>
            <div>
              <span className="text-muted-foreground">Available</span>
              <p className="font-medium">{item.availableStock}</p>
            </div>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="warehouseId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Warehouse</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select warehouse" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {warehouses.map((warehouse) => (
                        <SelectItem key={warehouse.id} value={warehouse.id.toString()}>
                          {warehouse.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="adjustmentType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Adjustment Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="SET">Set to specific amount</SelectItem>
                      <SelectItem value="INCREASE">Add to current stock</SelectItem>
                      <SelectItem value="DECREASE">Subtract from current stock</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    Quantity
                    {adjustmentType === "SET" && " (New total)"}
                    {adjustmentType === "INCREASE" && " (Amount to add)"}
                    {adjustmentType === "DECREASE" && " (Amount to subtract)"}
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Stock Preview */}
            {quantity > 0 && (
              <div className="p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                <div className="text-sm">
                  <span className="text-muted-foreground">New stock level: </span>
                  <span className="font-medium">{calculateNewStock()}</span>
                  <span className="text-muted-foreground ml-2">
                    (Change: {adjustmentType === "SET"
                      ? (quantity - item.currentStock > 0 ? "+" : "") + (quantity - item.currentStock)
                      : adjustmentType === "INCREASE"
                        ? "+" + quantity
                        : "-" + quantity
                    })
                  </span>
                </div>
              </div>
            )}

            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reason</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select reason" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Initial stock setup">Initial stock setup</SelectItem>
                      <SelectItem value="Stock received">Stock received</SelectItem>
                      <SelectItem value="Stock sold">Stock sold</SelectItem>
                      <SelectItem value="Stock damaged">Stock damaged</SelectItem>
                      <SelectItem value="Stock returned">Stock returned</SelectItem>
                      <SelectItem value="Stock transfer">Stock transfer</SelectItem>
                      <SelectItem value="Inventory correction">Inventory correction</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional notes about this adjustment..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "Adjusting..." : "Adjust Stock"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
