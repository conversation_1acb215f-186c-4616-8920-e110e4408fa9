"use client";

import * as React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { createCustomerFromEmail } from "@/lib/api/customers";
import { toast } from "sonner";
import { Mail, MapPin, Users } from "lucide-react";

// Simple email validation
const validateEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

interface CustomerEmailSignupFormProps {
  onSuccess?: (customer: any) => void;
  source?: string; // "newsletter", "popup", "footer", etc.
  campaign?: string;
  className?: string;
}

export function CustomerEmailSignupForm({
  onSuccess,
  source = "dashboard",
  campaign,
  className
}: CustomerEmailSignupFormProps) {
  const [loading, setLoading] = React.useState(false);
  const [email, setEmail] = React.useState("");
  const [newsletterSubscribe, setNewsletterSubscribe] = React.useState(true);
  const [emailSubscribe, setEmailSubscribe] = React.useState(true);
  const [errors, setErrors] = React.useState<Record<string, string>>({});
  const [geolocation, setGeolocation] = React.useState<{
    country?: string;
    city?: string;
    countryCode?: string;
  }>({});

  // Get geolocation data on component mount
  React.useEffect(() => {
    // Try to get geolocation from browser API or IP geolocation service
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            // You could use a reverse geocoding service here
            // For now, we'll just note that we have coordinates
            setGeolocation({
              country: "Unknown", // Would be filled by geocoding service
              city: "Unknown",
              countryCode: "US", // Default
            });
          } catch (error) {
            console.log("Geolocation error:", error);
          }
        },
        (error) => {
          console.log("Geolocation permission denied:", error);
        }
      );
    }
  }, []);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!email.trim()) {
      newErrors.email = "Email is required";
    } else if (!validateEmail(email)) {
      newErrors.email = "Please enter a valid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      const signupData = {
        email,
        newsletterSubscribe,
        emailSubscribe,
        source,
        campaign,
        referrer: typeof window !== 'undefined' ? document.referrer : undefined,
        ...geolocation,
      };

      const result = await createCustomerFromEmail(signupData);

      if (result.isNewCustomer) {
        toast.success("Welcome! Customer created successfully.");
      } else {
        toast.success("Thank you! Your preferences have been updated.");
      }

      // Reset form
      setEmail("");
      setNewsletterSubscribe(true);
      setEmailSubscribe(true);
      setErrors({});

      onSuccess?.(result.customer);

    } catch (error) {
      console.error("Email signup error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to sign up. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Mail className="h-5 w-5" />
          <span>Quick Customer Signup</span>
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Create a customer with just an email address. Perfect for newsletter signups and lead generation.
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email Address</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => {
                setEmail(e.target.value);
                if (errors.email) {
                  setErrors(prev => ({ ...prev, email: "" }));
                }
              }}
              className={errors.email ? "border-red-500" : ""}
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email}</p>
            )}
            <p className="text-sm text-muted-foreground">
              We'll create a customer profile with this email address.
            </p>
          </div>

          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <Checkbox
                id="newsletterSubscribe"
                checked={newsletterSubscribe}
                onCheckedChange={setNewsletterSubscribe}
              />
              <div className="space-y-1 leading-none">
                <Label htmlFor="newsletterSubscribe">Subscribe to newsletter</Label>
                <p className="text-sm text-muted-foreground">
                  Receive our weekly newsletter with updates and offers.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <Checkbox
                id="emailSubscribe"
                checked={emailSubscribe}
                onCheckedChange={setEmailSubscribe}
              />
              <div className="space-y-1 leading-none">
                <Label htmlFor="emailSubscribe">Email marketing</Label>
                <p className="text-sm text-muted-foreground">
                  Receive promotional emails and special offers.
                </p>
              </div>
            </div>
          </div>

          {/* Geolocation info */}
          {geolocation.country && (
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <MapPin className="h-4 w-4" />
              <span>Location: {geolocation.city}, {geolocation.country}</span>
            </div>
          )}

          <div className="flex items-center justify-between pt-4">
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Users className="h-4 w-4" />
              <span>Customer will be created as a prospect</span>
            </div>

            <Button type="submit" disabled={loading}>
              {loading ? "Creating..." : "Create Customer"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
