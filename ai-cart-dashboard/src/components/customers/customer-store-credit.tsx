"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  CreditCard, 
  Plus, 
  Minus,
  History,
  DollarSign,
  Calendar,
  User
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { toast } from "sonner";
import { format } from "date-fns";

interface StoreCreditTransaction {
  id: number;
  type: "CREDIT" | "DEBIT";
  amount: number;
  reason: string;
  createdBy: string;
  createdAt: string;
  orderId?: number;
}

interface CustomerStoreCreditProps {
  customerId: number;
}

export function CustomerStoreCredit({ customerId }: CustomerStoreCreditProps) {
  const [loading, setLoading] = React.useState(false);
  const [showAddCredit, setShowAddCredit] = React.useState(false);
  const [amount, setAmount] = React.useState("");
  const [reason, setReason] = React.useState("");
  
  const [currentBalance, setCurrentBalance] = React.useState(0);
  const [transactions, setTransactions] = React.useState<StoreCreditTransaction[]>([]);
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    fetchStoreCreditData();
  }, [customerId]);

  const fetchStoreCreditData = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/customers/${customerId}/store-credit`, {
        headers: {
          'Shop-Id': '1', // TODO: Get from context
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCurrentBalance(data.current_balance || 0);
        setTransactions(data.transactions || []);
      } else {
        // If API not implemented yet, show empty state
        setCurrentBalance(0);
        setTransactions([]);
      }
    } catch (error) {
      console.error('Error fetching store credit data:', error);
      setCurrentBalance(0);
      setTransactions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddCredit = async () => {
    if (!amount || !reason.trim()) {
      toast.error("Please enter both amount and reason");
      return;
    }

    try {
      setLoading(true);
      
      // TODO: Implement API call to add store credit
      // await addStoreCredit(customerId, { amount: parseFloat(amount), reason });
      
      toast.success("Store credit added successfully");
      setAmount("");
      setReason("");
      setShowAddCredit(false);
    } catch (error) {
      toast.error("Failed to add store credit");
    } finally {
      setLoading(false);
    }
  };

  const handleDeductCredit = async () => {
    if (!amount || !reason.trim()) {
      toast.error("Please enter both amount and reason");
      return;
    }

    try {
      setLoading(true);
      
      // TODO: Implement API call to deduct store credit
      // await deductStoreCredit(customerId, { amount: parseFloat(amount), reason });
      
      toast.success("Store credit deducted successfully");
      setAmount("");
      setReason("");
      setShowAddCredit(false);
    } catch (error) {
      toast.error("Failed to deduct store credit");
    } finally {
      setLoading(false);
    }
  };

  const getTransactionIcon = (type: string) => {
    return type === "CREDIT" ? Plus : Minus;
  };

  const getTransactionColor = (type: string) => {
    return type === "CREDIT" 
      ? "text-green-600 bg-green-100" 
      : "text-red-600 bg-red-100";
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <CreditCard className="h-5 w-5" />
            <span>Store Credit</span>
          </CardTitle>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setShowAddCredit(!showAddCredit)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Manage
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <div className="space-y-4">
            <div className="animate-pulse">
              <div className="h-20 bg-gray-200 rounded-lg mb-4"></div>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Current Balance */}
            <div className="text-center p-4 rounded-lg bg-gradient-to-r from-blue-50 to-purple-50 border">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <DollarSign className="h-5 w-5 text-blue-600" />
            <span className="text-sm font-medium text-blue-900">Current Balance</span>
          </div>
          <p className="text-3xl font-bold text-blue-700">
            {formatCurrency(currentBalance, "USD")}
          </p>
          <p className="text-xs text-blue-600 mt-1">
            Available for future purchases
          </p>
        </div>

        {/* Add/Deduct Credit Form */}
        {showAddCredit && (
          <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
            <h4 className="font-medium text-sm">Adjust Store Credit</h4>
            
            <div className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="amount">Amount</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="reason">Reason</Label>
                <Textarea
                  id="reason"
                  placeholder="Explain the reason for this adjustment..."
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  rows={2}
                />
              </div>
              
              <div className="flex space-x-2">
                <Button 
                  onClick={handleAddCredit} 
                  disabled={loading}
                  className="flex-1"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Credit
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handleDeductCredit} 
                  disabled={loading}
                  className="flex-1"
                >
                  <Minus className="h-4 w-4 mr-2" />
                  Deduct Credit
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Transaction History */}
        <div className="space-y-3">
          <div className="flex items-center space-x-2">
            <History className="h-4 w-4" />
            <h4 className="font-medium text-sm">Transaction History</h4>
          </div>
          
          {transactions.length > 0 ? (
            <div className="space-y-2">
              {transactions.map((transaction) => {
                const Icon = getTransactionIcon(transaction.type);
                const colorClass = getTransactionColor(transaction.type);
                
                return (
                  <div key={transaction.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`p-1.5 rounded-full ${colorClass}`}>
                        <Icon className="h-3 w-3" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">{transaction.reason}</p>
                        <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <User className="h-3 w-3" />
                            <span>{transaction.createdBy}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span>{format(new Date(transaction.createdAt), "MMM dd, yyyy")}</span>
                          </div>
                          {transaction.orderId && (
                            <Badge variant="secondary" className="text-xs">
                              Order #{transaction.orderId}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-medium ${transaction.type === 'CREDIT' ? 'text-green-600' : 'text-red-600'}`}>
                        {transaction.type === 'CREDIT' ? '+' : ''}
                        {formatCurrency(transaction.amount, "USD")}
                      </p>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-6 border-2 border-dashed border-gray-200 rounded-lg">
              <History className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No transactions yet</p>
            </div>
          )}
        </div>

        {/* Store Credit Info */}
        <div className="p-3 rounded-lg bg-green-50 border border-green-200">
          <div className="flex items-start space-x-2">
            <CreditCard className="h-4 w-4 text-green-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-green-900">About Store Credit</p>
              <p className="text-xs text-green-700 mt-1">
                Store credit can be applied to future orders during checkout. 
                Credits don't expire and can be used partially across multiple orders.
              </p>
            </div>
          </div>
        </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
