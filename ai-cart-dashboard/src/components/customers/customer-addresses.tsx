"use client";

import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  MapPin, 
  Plus, 
  Edit, 
  Trash2, 
  Star, 
  Building, 
  Phone,
  MoreHorizontal
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CustomerAddress } from "@/types/customer";
import { toast } from "sonner";

interface CustomerAddressesProps {
  customerId: number;
  addresses: CustomerAddress[];
  onAddressCreate?: () => void;
  onAddressEdit?: (address: CustomerAddress) => void;
  onAddressDelete?: (addressId: number) => void;
  onSetDefault?: (addressId: number) => void;
  loading?: boolean;
}

export function CustomerAddresses({
  customerId,
  addresses,
  onAddressCreate,
  onAddressEdit,
  onAddressDelete,
  onSetDefault,
  loading = false
}: CustomerAddressesProps) {

  const getAddressTypeColor = (type: string) => {
    switch (type) {
      case 'BILLING':
        return 'bg-blue-100 text-blue-800';
      case 'SHIPPING':
        return 'bg-green-100 text-green-800';
      case 'BOTH':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleDeleteAddress = (addressId: number) => {
    if (addresses.length <= 1) {
      toast.error("Cannot delete the only address. Customer must have at least one address.");
      return;
    }
    
    if (confirm("Are you sure you want to delete this address?")) {
      onAddressDelete?.(addressId);
    }
  };

  const handleSetDefault = (addressId: number) => {
    onSetDefault?.(addressId);
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Customer Addresses</span>
            <Button disabled>
              <Plus className="mr-2 h-4 w-4" />
              Add Address
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 2 }).map((_, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Customer Addresses ({addresses.length})</span>
          <Button onClick={() => onAddressCreate?.()}>
            <Plus className="mr-2 h-4 w-4" />
            Add Address
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {addresses.length === 0 ? (
          <div className="text-center py-8">
            <MapPin className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No addresses</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by creating a new address for this customer.
            </p>
            <div className="mt-6">
              <Button onClick={() => onAddressCreate?.()}>
                <Plus className="mr-2 h-4 w-4" />
                Add Address
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {addresses.map((address) => (
              <div
                key={address.id}
                className="relative p-4 border rounded-lg hover:shadow-sm transition-shadow"
              >
                {/* Default badge */}
                {address.isDefault && (
                  <div className="absolute top-2 right-2">
                    <Badge variant="secondary" className="text-xs">
                      <Star className="mr-1 h-3 w-3 fill-current" />
                      Default
                    </Badge>
                  </div>
                )}

                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-2">
                    {/* Address type and name */}
                    <div className="flex items-center space-x-2">
                      <Badge className={getAddressTypeColor(address.type)}>
                        {address.type}
                      </Badge>
                      <span className="font-medium">
                        {address.firstName} {address.lastName}
                      </span>
                    </div>

                    {/* Company */}
                    {address.company && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Building className="mr-2 h-4 w-4" />
                        {address.company}
                      </div>
                    )}

                    {/* Address */}
                    <div className="flex items-start text-sm text-gray-600">
                      <MapPin className="mr-2 h-4 w-4 mt-0.5 flex-shrink-0" />
                      <div>
                        <div>{address.line1}</div>
                        {address.line2 && <div>{address.line2}</div>}
                        <div>
                          {address.city}, {address.state} {address.postalCode}
                        </div>
                        <div>{address.country}</div>
                      </div>
                    </div>

                    {/* Phone */}
                    {address.phone && (
                      <div className="flex items-center text-sm text-gray-600">
                        <Phone className="mr-2 h-4 w-4" />
                        {address.phone}
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="ml-4">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => onAddressEdit?.(address)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        {!address.isDefault && (
                          <DropdownMenuItem onClick={() => handleSetDefault(address.id)}>
                            <Star className="mr-2 h-4 w-4" />
                            Set as Default
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => handleDeleteAddress(address.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
