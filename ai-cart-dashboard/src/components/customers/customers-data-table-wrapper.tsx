"use client";

import { CustomersDataTable } from "./customers-data-table";
import { Customer, CustomerFilters } from "@/types/customer";
import { useRouter, useSearchParams } from "next/navigation";

interface CustomersDataTableWrapperProps {
  data: Customer[];
  pageCount: number;
  pageIndex: number;
  pageSize: number;
  total: number;
  filters: CustomerFilters;
}

export function CustomersDataTableWrapper({
  data,
  pageCount,
  pageIndex,
  pageSize,
  total,
  filters
}: CustomersDataTableWrapperProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handlePageChange = (newPageIndex: number) => {
    const params = new URLSearchParams(searchParams);
    params.set('page', (newPageIndex + 1).toString());
    router.push(`/dashboard/customers?${params.toString()}`);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    const params = new URLSearchParams(searchParams);
    params.set('page', '1'); // Reset to first page
    params.delete('pageSize'); // Remove if default, or set if different
    if (newPageSize !== 20) {
      params.set('pageSize', newPageSize.toString());
    }
    router.push(`/dashboard/customers?${params.toString()}`);
  };

  const handleFiltersChange = (newFilters: CustomerFilters) => {
    const params = new URLSearchParams();
    params.set('page', '1'); // Reset to first page
    
    if (newFilters.search) {
      params.set('search', newFilters.search);
    }
    if (newFilters.sortBy && newFilters.sortBy !== 'createdAt') {
      params.set('sortBy', newFilters.sortBy);
    }
    if (newFilters.order && newFilters.order !== 'desc') {
      params.set('order', newFilters.order);
    }
    
    router.push(`/dashboard/customers?${params.toString()}`);
  };

  const handleRefresh = () => {
    router.refresh();
  };

  return (
    <CustomersDataTable
      data={data}
      loading={false}
      pageCount={pageCount}
      pageIndex={pageIndex}
      pageSize={pageSize}
      onPageChange={handlePageChange}
      onPageSizeChange={handlePageSizeChange}
      onFiltersChange={handleFiltersChange}
      onRefresh={handleRefresh}
    />
  );
}
