"use client";

import * as React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { CustomerDetail, CustomerUpdateRequest, Gender, CustomerType, CustomerTier } from "@/types/customer";
import { toast } from "sonner";

interface CustomerEditFormProps {
  customer: CustomerDetail;
  onSubmit: (updateData: CustomerUpdateRequest) => void;
  loading: boolean;
  onCancel: () => void;
}

export function CustomerEditForm({ customer, onSubmit, loading, onCancel }: CustomerEditFormProps) {
  const [formData, setFormData] = React.useState<CustomerUpdateRequest>({
    firstName: customer.firstName,
    lastName: customer.lastName,
    email: customer.email,
    phone: customer.phone || "",
    dateOfBirth: customer.dateOfBirth || "",
    gender: customer.gender,
    company: customer.company || "",
    jobTitle: customer.jobTitle || "",
    languageCode: customer.languageCode,
    currencyCode: customer.currencyCode,
    timezone: customer.timezone,
    avatarUrl: customer.avatarUrl || "",
    newsletterSubscribe: customer.newsletterSubscribe,
    emailSubscribe: customer.emailSubscribe,
    phoneSubscribe: customer.phoneSubscribe,
    smsSubscribe: customer.smsSubscribe,
    customerType: customer.customerType,
    customerTier: customer.customerTier,
    tags: customer.tags || "",
    notes: customer.notes || "",
    accountLocked: customer.accountLocked,
    accountLockedReason: customer.accountLockedReason || "",
    taxExempt: customer.taxExempt,
    taxExemptionReason: customer.taxExemptionReason || "",
    vatNumber: customer.vatNumber || "",
    taxId: customer.taxId || "",
    updateReason: "",
    notifyCustomer: false,
  });

  const [errors, setErrors] = React.useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName?.trim()) {
      newErrors.firstName = "First name is required";
    }

    if (!formData.email?.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    if (!formData.updateReason?.trim()) {
      newErrors.updateReason = "Update reason is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Please fix the errors in the form");
      return;
    }

    // Only send fields that have changed
    const updateData: CustomerUpdateRequest = {
      updateReason: formData.updateReason,
      notifyCustomer: formData.notifyCustomer,
    };

    // Check for changes and add to update data
    if (formData.firstName !== customer.firstName) updateData.firstName = formData.firstName;
    if (formData.lastName !== customer.lastName) updateData.lastName = formData.lastName;
    if (formData.email !== customer.email) updateData.email = formData.email;
    if (formData.phone !== (customer.phone || "")) updateData.phone = formData.phone;
    if (formData.dateOfBirth !== (customer.dateOfBirth || "")) updateData.dateOfBirth = formData.dateOfBirth;
    if (formData.gender !== customer.gender) updateData.gender = formData.gender;
    if (formData.company !== (customer.company || "")) updateData.company = formData.company;
    if (formData.jobTitle !== (customer.jobTitle || "")) updateData.jobTitle = formData.jobTitle;
    if (formData.languageCode !== customer.languageCode) updateData.languageCode = formData.languageCode;
    if (formData.currencyCode !== customer.currencyCode) updateData.currencyCode = formData.currencyCode;
    if (formData.timezone !== customer.timezone) updateData.timezone = formData.timezone;
    if (formData.avatarUrl !== (customer.avatarUrl || "")) updateData.avatarUrl = formData.avatarUrl;
    if (formData.newsletterSubscribe !== customer.newsletterSubscribe) updateData.newsletterSubscribe = formData.newsletterSubscribe;
    if (formData.emailSubscribe !== customer.emailSubscribe) updateData.emailSubscribe = formData.emailSubscribe;
    if (formData.phoneSubscribe !== customer.phoneSubscribe) updateData.phoneSubscribe = formData.phoneSubscribe;
    if (formData.smsSubscribe !== customer.smsSubscribe) updateData.smsSubscribe = formData.smsSubscribe;
    if (formData.customerType !== customer.customerType) updateData.customerType = formData.customerType;
    if (formData.customerTier !== customer.customerTier) updateData.customerTier = formData.customerTier;
    if (formData.tags !== (customer.tags || "")) updateData.tags = formData.tags;
    if (formData.notes !== (customer.notes || "")) updateData.notes = formData.notes;
    if (formData.accountLocked !== customer.accountLocked) {
      updateData.accountLocked = formData.accountLocked;
      updateData.accountLockedReason = formData.accountLockedReason;
    }
    if (formData.taxExempt !== customer.taxExempt) {
      updateData.taxExempt = formData.taxExempt;
      updateData.taxExemptionReason = formData.taxExemptionReason;
    }
    if (formData.vatNumber !== (customer.vatNumber || "")) updateData.vatNumber = formData.vatNumber;
    if (formData.taxId !== (customer.taxId || "")) updateData.taxId = formData.taxId;

    onSubmit(updateData);
  };

  const updateField = (field: keyof CustomerUpdateRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Update Information */}
      <Card>
        <CardHeader>
          <CardTitle>Update Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="updateReason">Reason for Update *</Label>
            <Textarea
              id="updateReason"
              required
              value={formData.updateReason}
              onChange={(e) => updateField('updateReason', e.target.value)}
              placeholder="Explain why this customer is being updated..."
              className={errors.updateReason ? "border-red-500" : ""}
            />
            {errors.updateReason && (
              <p className="text-sm text-red-500">{errors.updateReason}</p>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="notifyCustomer"
              checked={formData.notifyCustomer}
              onCheckedChange={(checked) => updateField('notifyCustomer', checked)}
            />
            <Label htmlFor="notifyCustomer">Notify customer of changes</Label>
          </div>
        </CardContent>
      </Card>

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              value={formData.firstName}
              onChange={(e) => updateField('firstName', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              value={formData.lastName}
              onChange={(e) => updateField('lastName', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => updateField('email', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              value={formData.phone}
              onChange={(e) => updateField('phone', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="company">Company</Label>
            <Input
              id="company"
              value={formData.company}
              onChange={(e) => updateField('company', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="jobTitle">Job Title</Label>
            <Input
              id="jobTitle"
              value={formData.jobTitle}
              onChange={(e) => updateField('jobTitle', e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Personal Information */}
      <Card>
        <CardHeader>
          <CardTitle>Personal Information</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="dateOfBirth">Date of Birth</Label>
            <Input
              id="dateOfBirth"
              type="date"
              value={formData.dateOfBirth}
              onChange={(e) => updateField('dateOfBirth', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="gender">Gender</Label>
            <Select value={formData.gender} onValueChange={(value: Gender) => updateField('gender', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select gender" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="MALE">Male</SelectItem>
                <SelectItem value="FEMALE">Female</SelectItem>
                <SelectItem value="OTHER">Other</SelectItem>
                <SelectItem value="PREFER_NOT_TO_SAY">Prefer not to say</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Customer Classification */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Classification</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="customerType">Customer Type</Label>
            <Select value={formData.customerType} onValueChange={(value: CustomerType) => updateField('customerType', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="REGULAR">Regular</SelectItem>
                <SelectItem value="VIP">VIP</SelectItem>
                <SelectItem value="WHOLESALE">Wholesale</SelectItem>
                <SelectItem value="CORPORATE">Corporate</SelectItem>
                <SelectItem value="GUEST">Guest</SelectItem>
                <SelectItem value="RETURNING">Returning</SelectItem>
                <SelectItem value="LOYAL">Loyal</SelectItem>
                <SelectItem value="PROSPECT">Prospect</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="customerTier">Customer Tier</Label>
            <Select value={formData.customerTier} onValueChange={(value: CustomerTier) => updateField('customerTier', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="BRONZE">Bronze</SelectItem>
                <SelectItem value="SILVER">Silver</SelectItem>
                <SelectItem value="GOLD">Gold</SelectItem>
                <SelectItem value="PLATINUM">Platinum</SelectItem>
                <SelectItem value="DIAMOND">Diamond</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="tags">Tags</Label>
            <Input
              id="tags"
              value={formData.tags}
              onChange={(e) => updateField('tags', e.target.value)}
              placeholder="Comma-separated tags"
            />
          </div>
        </CardContent>
      </Card>

      {/* Marketing Preferences */}
      <Card>
        <CardHeader>
          <CardTitle>Marketing Preferences</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="newsletterSubscribe"
              checked={formData.newsletterSubscribe}
              onCheckedChange={(checked) => updateField('newsletterSubscribe', checked)}
            />
            <Label htmlFor="newsletterSubscribe">Subscribe to newsletter</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="emailSubscribe"
              checked={formData.emailSubscribe}
              onCheckedChange={(checked) => updateField('emailSubscribe', checked)}
            />
            <Label htmlFor="emailSubscribe">Email marketing</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="phoneSubscribe"
              checked={formData.phoneSubscribe}
              onCheckedChange={(checked) => updateField('phoneSubscribe', checked)}
            />
            <Label htmlFor="phoneSubscribe">Phone marketing</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="smsSubscribe"
              checked={formData.smsSubscribe}
              onCheckedChange={(checked) => updateField('smsSubscribe', checked)}
            />
            <Label htmlFor="smsSubscribe">SMS marketing</Label>
          </div>
        </CardContent>
      </Card>

      {/* Account Management */}
      <Card>
        <CardHeader>
          <CardTitle>Account Management</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="accountLocked"
              checked={formData.accountLocked}
              onCheckedChange={(checked) => updateField('accountLocked', checked)}
            />
            <Label htmlFor="accountLocked">Lock account</Label>
          </div>
          {formData.accountLocked && (
            <div className="space-y-2">
              <Label htmlFor="accountLockedReason">Lock Reason</Label>
              <Input
                id="accountLockedReason"
                value={formData.accountLockedReason}
                onChange={(e) => updateField('accountLockedReason', e.target.value)}
                placeholder="Reason for locking the account"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tax Information */}
      <Card>
        <CardHeader>
          <CardTitle>Tax Information</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div className="flex items-center space-x-2 md:col-span-2">
            <Checkbox
              id="taxExempt"
              checked={formData.taxExempt}
              onCheckedChange={(checked) => updateField('taxExempt', checked)}
            />
            <Label htmlFor="taxExempt">Tax exempt</Label>
          </div>
          {formData.taxExempt && (
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="taxExemptionReason">Tax Exemption Reason</Label>
              <Input
                id="taxExemptionReason"
                value={formData.taxExemptionReason}
                onChange={(e) => updateField('taxExemptionReason', e.target.value)}
              />
            </div>
          )}
          <div className="space-y-2">
            <Label htmlFor="vatNumber">VAT Number</Label>
            <Input
              id="vatNumber"
              value={formData.vatNumber}
              onChange={(e) => updateField('vatNumber', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="taxId">Tax ID</Label>
            <Input
              id="taxId"
              value={formData.taxId}
              onChange={(e) => updateField('taxId', e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Admin Notes */}
      <Card>
        <CardHeader>
          <CardTitle>Admin Notes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => updateField('notes', e.target.value)}
              placeholder="Internal notes about this customer..."
            />
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? "Updating..." : "Update Customer"}
        </Button>
      </div>
    </form>
  );
}
