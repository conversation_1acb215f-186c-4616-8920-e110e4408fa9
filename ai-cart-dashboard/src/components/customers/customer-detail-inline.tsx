"use client";

import * as React from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Building, 
  Calendar, 
  Globe, 
  DollarSign,
  ShoppingCart,
  Award,
  Shield,
  CheckCircle,
  XCircle,
  AlertCircle,
  Edit,
  Save,
  X,
  Settings
} from "lucide-react";
import { CustomerDetail } from "@/types/customer";
import { formatCurrency } from "@/lib/utils";
import { format } from "date-fns";
import { toast } from "sonner";
import {
  updateCustomerBasicInfo,
  updateCustomerClassification,
  updateCustomerPreferences,
  updateCustomerMarketing,
  updateCustomerTax,
  updateCustomerNotes
} from "@/app/actions/customer-actions";
import { CustomerTagManager, SimpleTagInput } from "./customer-tag-manager";
import { CustomerTierManager } from "./customer-tier-manager";

interface CustomerDetailInlineProps {
  customer: CustomerDetail;
  onUpdate?: (updatedCustomer: Partial<CustomerDetail>) => void;
}

export function CustomerDetailInline({ customer, onUpdate }: CustomerDetailInlineProps) {
  const [editingSection, setEditingSection] = React.useState<string | null>(null);
  const [formData, setFormData] = React.useState<Partial<CustomerDetail>>({});
  const [isLoading, setIsLoading] = React.useState(false);
  const [selectedTags, setSelectedTags] = React.useState<any[]>([]);

  const handleEdit = (section: string) => {
    console.log('handle eeit', section)
    setEditingSection(section);
    setFormData(customer);
  };

  const handleCancel = () => {
    setEditingSection(null);
    setFormData({});
  };

  const handleSave = async (section: string) => {
    setIsLoading(true);
    try {
      let result;

      switch (section) {
        case 'basic':
          result = await updateCustomerBasicInfo(customer.id, {
            firstName: formData.firstName,
            lastName: formData.lastName,
            email: formData.email,
            phone: formData.phone,
            company: formData.company,
            jobTitle: formData.jobTitle,
          });
          break;
        case 'classification':
          result = await updateCustomerClassification(customer.id, {
            customerType: formData.customerType,
            customerTier: formData.customerTier,
            tags: formData.tags,
          });
          break;
        case 'preferences':
          result = await updateCustomerPreferences(customer.id, {
            languageCode: formData.languageCode,
            currencyCode: formData.currencyCode,
            timezone: formData.timezone,
          });
          break;
        case 'marketing':
          result = await updateCustomerMarketing(customer.id, {
            newsletterSubscribe: formData.newsletterSubscribe,
            emailSubscribe: formData.emailSubscribe,
            phoneSubscribe: formData.phoneSubscribe,
            smsSubscribe: formData.smsSubscribe,
          });
          break;
        case 'tax':
          result = await updateCustomerTax(customer.id, {
            taxExempt: formData.taxExempt,
            taxExemptionReason: formData.taxExemptionReason,
            vatNumber: formData.vatNumber,
            taxId: formData.taxId,
          });
          break;
        case 'notes':
          result = await updateCustomerNotes(customer.id, formData.notes || '');
          break;
        default:
          throw new Error('Unknown section');
      }

      if (result.success) {
        onUpdate?.(result.data || formData);
        toast.success(`${section} updated successfully`);
        setEditingSection(null);
        setFormData({});
        // Refresh the page to show updated data
        window.location.reload();
      } else {
        throw new Error(result.error || 'Failed to update customer');
      }
    } catch (error) {
      toast.error(`Failed to update ${section}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error('Update error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getCustomerTypeColor = (type: string) => {
    const colors = {
      VIP: "bg-purple-100 text-purple-800",
      WHOLESALE: "bg-blue-100 text-blue-800",
      CORPORATE: "bg-green-100 text-green-800",
      REGULAR: "bg-gray-100 text-gray-800",
      GUEST: "bg-orange-100 text-orange-800",
      RETURNING: "bg-cyan-100 text-cyan-800",
      LOYAL: "bg-pink-100 text-pink-800",
      PROSPECT: "bg-yellow-100 text-yellow-800"
    };
    return colors[type as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  const getTierColor = (tier: string) => {
    const colors = {
      BRONZE: "bg-amber-100 text-amber-800",
      SILVER: "bg-gray-100 text-gray-800",
      GOLD: "bg-yellow-100 text-yellow-800",
      PLATINUM: "bg-slate-100 text-slate-800",
      DIAMOND: "bg-blue-100 text-blue-800"
    };
    return colors[tier as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  const EditableSection = ({ 
    section, 
    title, 
    icon: Icon, 
    children 
  }: { 
    section: string; 
    title: string; 
    icon: React.ComponentType<any>; 
    children: React.ReactNode;
  }) => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Icon className="mr-2 h-5 w-5" />
            {title}
          </div>
          {editingSection === section ? (
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                variant="outline"
                type="button"
                onClick={handleCancel}
                disabled={isLoading}
              >
                <X className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                onClick={() => handleSave(section)}
                disabled={isLoading}
                type="button"
              >
                <Save className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <Button
              size="sm"
              variant="outline"
              type="button"
              onClick={() => handleEdit(section)}
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <EditableSection section="basic" title="Basic Information" icon={User}>
        {editingSection === "basic" ? (
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="firstName">First Name</Label>
              <Input
                id="firstName"
                value={formData.firstName || ""}
                onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName">Last Name</Label>
              <Input
                id="lastName"
                value={formData.lastName || ""}
                onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email || ""}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                value={formData.phone || ""}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="company">Company</Label>
              <Input
                id="company"
                value={formData.company || ""}
                onChange={(e) => setFormData({ ...formData, company: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="jobTitle">Job Title</Label>
              <Input
                id="jobTitle"
                value={formData.jobTitle || ""}
                onChange={(e) => setFormData({ ...formData, jobTitle: e.target.value })}
              />
            </div>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-gray-500">Full Name</label>
              <p className="text-sm font-medium">{customer.firstName} {customer.lastName}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Email</label>
              <div className="flex items-center space-x-2">
                <p className="text-sm">{customer.email}</p>
                {customer.emailVerified ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Phone</label>
              <div className="flex items-center space-x-2">
                <p className="text-sm">{customer.phone || "Not provided"}</p>
                {customer.phoneVerified ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Company</label>
              <p className="text-sm">{customer.company || "Not provided"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Job Title</label>
              <p className="text-sm">{customer.jobTitle || "Not provided"}</p>
            </div>
          </div>
        )}
      </EditableSection>

      {/* Customer Classification */}
      <EditableSection section="classification" title="Customer Classification" icon={Award}>
        {editingSection === "classification" ? (
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="customerType">Customer Type</Label>
              <Select
                value={formData.customerType || customer.customerType}
                onValueChange={(value) => setFormData({ ...formData, customerType: value as any })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="REGULAR">Regular</SelectItem>
                  <SelectItem value="VIP">VIP</SelectItem>
                  <SelectItem value="WHOLESALE">Wholesale</SelectItem>
                  <SelectItem value="CORPORATE">Corporate</SelectItem>
                  <SelectItem value="GUEST">Guest</SelectItem>
                  <SelectItem value="RETURNING">Returning</SelectItem>
                  <SelectItem value="LOYAL">Loyal</SelectItem>
                  <SelectItem value="PROSPECT">Prospect</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="customerTier">Customer Tier</Label>
              <Select
                value={formData.customerTier || customer.customerTier}
                onValueChange={(value) => setFormData({ ...formData, customerTier: value as any })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="BRONZE">Bronze</SelectItem>
                  <SelectItem value="SILVER">Silver</SelectItem>
                  <SelectItem value="GOLD">Gold</SelectItem>
                  <SelectItem value="PLATINUM">Platinum</SelectItem>
                  <SelectItem value="DIAMOND">Diamond</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2 md:col-span-2">
              <SimpleTagInput
                value={formData.tags || ""}
                onChange={(value) => setFormData({ ...formData, tags: value })}
                placeholder="Comma-separated tags"
              />
              <p className="text-xs text-muted-foreground">
                Note: Advanced tag management with colors and analytics is coming soon!
              </p>
            </div>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-gray-500">Customer Type</label>
              <div className="mt-1">
                <Badge className={getCustomerTypeColor(customer.customerType)}>
                  {customer.customerType}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Customer Tier</label>
              <div className="mt-1">
                <Badge className={getTierColor(customer.customerTier)}>
                  {customer.customerTier}
                </Badge>
              </div>
            </div>
            <div className="md:col-span-2">
              <label className="text-sm font-medium text-gray-500">Tags</label>
              <div className="mt-1 flex flex-wrap gap-2">
                {customer.tags ? (
                  customer.tags.split(',').map((tag, index) => (
                    <Badge key={index} variant="outline">
                      {tag.trim()}
                    </Badge>
                  ))
                ) : (
                  <p className="text-sm text-gray-500">No tags</p>
                )}
              </div>
            </div>
          </div>
        )}
      </EditableSection>

      {/* Preferences & Settings */}
      <EditableSection section="preferences" title="Preferences & Settings" icon={Settings}>
        {editingSection === "preferences" ? (
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="languageCode">Language</Label>
              <Select
                value={formData.languageCode || customer.languageCode}
                onValueChange={(value) => setFormData({ ...formData, languageCode: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="es">Spanish</SelectItem>
                  <SelectItem value="fr">French</SelectItem>
                  <SelectItem value="de">German</SelectItem>
                  <SelectItem value="it">Italian</SelectItem>
                  <SelectItem value="pt">Portuguese</SelectItem>
                  <SelectItem value="zh">Chinese</SelectItem>
                  <SelectItem value="ja">Japanese</SelectItem>
                  <SelectItem value="ko">Korean</SelectItem>
                  <SelectItem value="ar">Arabic</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="currencyCode">Currency</Label>
              <Select
                value={formData.currencyCode || customer.currencyCode}
                onValueChange={(value) => setFormData({ ...formData, currencyCode: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USD">USD - US Dollar</SelectItem>
                  <SelectItem value="EUR">EUR - Euro</SelectItem>
                  <SelectItem value="GBP">GBP - British Pound</SelectItem>
                  <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
                  <SelectItem value="AUD">AUD - Australian Dollar</SelectItem>
                  <SelectItem value="JPY">JPY - Japanese Yen</SelectItem>
                  <SelectItem value="CNY">CNY - Chinese Yuan</SelectItem>
                  <SelectItem value="INR">INR - Indian Rupee</SelectItem>
                  <SelectItem value="BRL">BRL - Brazilian Real</SelectItem>
                  <SelectItem value="MXN">MXN - Mexican Peso</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="timezone">Timezone</Label>
              <Select
                value={formData.timezone || customer.timezone}
                onValueChange={(value) => setFormData({ ...formData, timezone: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="UTC">UTC</SelectItem>
                  <SelectItem value="America/New_York">Eastern Time</SelectItem>
                  <SelectItem value="America/Chicago">Central Time</SelectItem>
                  <SelectItem value="America/Denver">Mountain Time</SelectItem>
                  <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                  <SelectItem value="Europe/London">London</SelectItem>
                  <SelectItem value="Europe/Paris">Paris</SelectItem>
                  <SelectItem value="Europe/Berlin">Berlin</SelectItem>
                  <SelectItem value="Asia/Tokyo">Tokyo</SelectItem>
                  <SelectItem value="Asia/Shanghai">Shanghai</SelectItem>
                  <SelectItem value="Asia/Kolkata">Mumbai</SelectItem>
                  <SelectItem value="Australia/Sydney">Sydney</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-3">
            <div>
              <label className="text-sm font-medium text-gray-500">Language</label>
              <p className="text-sm">{customer.languageCode?.toUpperCase() || "EN"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Currency</label>
              <p className="text-sm">{customer.currencyCode || "USD"}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Timezone</label>
              <p className="text-sm">{customer.timezone || "UTC"}</p>
            </div>
          </div>
        )}
      </EditableSection>

      {/* Marketing Preferences */}
      <EditableSection section="marketing" title="Marketing Preferences" icon={Mail}>
        {editingSection === "marketing" ? (
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="newsletterSubscribe"
                checked={formData.newsletterSubscribe ?? customer.newsletterSubscribe}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, newsletterSubscribe: checked as boolean })
                }
              />
              <Label htmlFor="newsletterSubscribe">Subscribe to newsletter</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="emailSubscribe"
                checked={formData.emailSubscribe ?? customer.emailSubscribe}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, emailSubscribe: checked as boolean })
                }
              />
              <Label htmlFor="emailSubscribe">Email marketing</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="phoneSubscribe"
                checked={formData.phoneSubscribe ?? customer.phoneSubscribe}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, phoneSubscribe: checked as boolean })
                }
              />
              <Label htmlFor="phoneSubscribe">Phone marketing</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="smsSubscribe"
                checked={formData.smsSubscribe ?? customer.smsSubscribe}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, smsSubscribe: checked as boolean })
                }
              />
              <Label htmlFor="smsSubscribe">SMS marketing</Label>
            </div>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2">
            <div className="flex items-center justify-between">
              <span className="text-sm">Newsletter</span>
              <Badge variant={customer.newsletterSubscribe ? "default" : "secondary"}>
                {customer.newsletterSubscribe ? "Subscribed" : "Not Subscribed"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Email Marketing</span>
              <Badge variant={customer.emailSubscribe ? "default" : "secondary"}>
                {customer.emailSubscribe ? "Subscribed" : "Not Subscribed"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Phone Marketing</span>
              <Badge variant={customer.phoneSubscribe ? "default" : "secondary"}>
                {customer.phoneSubscribe ? "Subscribed" : "Not Subscribed"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">SMS Marketing</span>
              <Badge variant={customer.smsSubscribe ? "default" : "secondary"}>
                {customer.smsSubscribe ? "Subscribed" : "Not Subscribed"}
              </Badge>
            </div>
          </div>
        )}
      </EditableSection>

      {/* Tax Settings */}
      <EditableSection section="tax" title="Tax Settings" icon={Shield}>
        {editingSection === "tax" ? (
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="taxExempt"
                checked={formData.taxExempt ?? customer.taxExempt}
                onCheckedChange={(checked) =>
                  setFormData({ ...formData, taxExempt: checked as boolean })
                }
              />
              <Label htmlFor="taxExempt">Tax exempt</Label>
            </div>
            {(formData.taxExempt ?? customer.taxExempt) && (
              <div className="space-y-2">
                <Label htmlFor="taxExemptionReason">Tax Exemption Reason</Label>
                <Input
                  id="taxExemptionReason"
                  value={formData.taxExemptionReason || ""}
                  onChange={(e) => setFormData({ ...formData, taxExemptionReason: e.target.value })}
                  placeholder="Reason for tax exemption"
                />
              </div>
            )}
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="vatNumber">VAT Number</Label>
                <Input
                  id="vatNumber"
                  value={formData.vatNumber || ""}
                  onChange={(e) => setFormData({ ...formData, vatNumber: e.target.value })}
                  placeholder="VAT number"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="taxId">Tax ID</Label>
                <Input
                  id="taxId"
                  value={formData.taxId || ""}
                  onChange={(e) => setFormData({ ...formData, taxId: e.target.value })}
                  placeholder="Tax ID"
                />
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Tax Status</span>
              <Badge variant={customer.taxExempt ? "default" : "secondary"}>
                {customer.taxExempt ? "Tax Exempt" : "Taxable"}
              </Badge>
            </div>
            {customer.taxExempt && customer.taxExemptionReason && (
              <div>
                <label className="text-sm font-medium text-gray-500">Exemption Reason</label>
                <p className="text-sm">{customer.taxExemptionReason}</p>
              </div>
            )}
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <label className="text-sm font-medium text-gray-500">VAT Number</label>
                <p className="text-sm">{customer.vatNumber || "Not provided"}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Tax ID</label>
                <p className="text-sm">{customer.taxId || "Not provided"}</p>
              </div>
            </div>
          </div>
        )}
      </EditableSection>

      {/* Admin Notes */}
      <EditableSection section="notes" title="Admin Notes" icon={AlertCircle}>
        {editingSection === "notes" ? (
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes || ""}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              placeholder="Internal notes about this customer..."
              rows={4}
            />
          </div>
        ) : (
          <p className="text-sm text-gray-600 whitespace-pre-wrap">
            {customer.notes || "No notes available"}
          </p>
        )}
      </EditableSection>

      {/* Customer Tier Management */}
      <CustomerTierManager customerId={customer.id} />
    </div>
  );
}
