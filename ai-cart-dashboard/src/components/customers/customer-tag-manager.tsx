"use client";

import * as React from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { X, Plus, Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface CustomerTag {
  id: number;
  name: string;
  slug: string;
  color?: string;
  customer_count: number;
}

interface CustomerTagManagerProps {
  customerId: number;
  selectedTags: CustomerTag[];
  onTagsChange: (tags: CustomerTag[]) => void;
  className?: string;
}

export function CustomerTagManager({ 
  customerId, 
  selectedTags, 
  onTagsChange, 
  className 
}: CustomerTagManagerProps) {
  const [availableTags, setAvailableTags] = React.useState<CustomerTag[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isOpen, setIsOpen] = React.useState(false);
  const [searchValue, setSearchValue] = React.useState("");
  const [isCreating, setIsCreating] = React.useState(false);

  // Fetch available tags
  React.useEffect(() => {
    fetchAvailableTags();
  }, []);

  const fetchAvailableTags = async () => {
    try {
      const response = await fetch('/api/customer-tags?with_counts=true', {
        headers: {
          'Shop-Id': '1', // TODO: Get from context
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setAvailableTags(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching tags:', error);
    }
  };

  const createNewTag = async (tagName: string) => {
    if (!tagName.trim()) return;

    setIsCreating(true);
    try {
      const response = await fetch('/api/customer-tags', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Shop-Id': '1', // TODO: Get from context
        },
        body: JSON.stringify({
          name: tagName.trim(),
          color: generateRandomColor(),
        }),
      });

      if (response.ok) {
        const newTag = await response.json();
        setAvailableTags(prev => [...prev, newTag]);
        handleTagSelect(newTag);
        setSearchValue("");
        toast.success(`Tag "${tagName}" created and added`);
      } else {
        throw new Error('Failed to create tag');
      }
    } catch (error) {
      toast.error('Failed to create tag');
      console.error('Error creating tag:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const handleTagSelect = (tag: CustomerTag) => {
    if (!selectedTags.find(t => t.id === tag.id)) {
      onTagsChange([...selectedTags, tag]);
    }
    setIsOpen(false);
  };

  const handleTagRemove = (tagId: number) => {
    onTagsChange(selectedTags.filter(tag => tag.id !== tagId));
  };

  const generateRandomColor = () => {
    const colors = [
      '#ef4444', '#f97316', '#f59e0b', '#eab308', '#84cc16',
      '#22c55e', '#10b981', '#14b8a6', '#06b6d4', '#0ea5e9',
      '#3b82f6', '#6366f1', '#8b5cf6', '#a855f7', '#d946ef',
      '#ec4899', '#f43f5e'
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  const filteredTags = availableTags.filter(tag =>
    tag.name.toLowerCase().includes(searchValue.toLowerCase()) &&
    !selectedTags.find(selected => selected.id === tag.id)
  );

  const canCreateNew = searchValue.trim() && 
    !availableTags.find(tag => 
      tag.name.toLowerCase() === searchValue.toLowerCase()
    );

  return (
    <div className={cn("space-y-2", className)}>
      <Label>Tags</Label>
      
      {/* Selected Tags */}
      <div className="flex flex-wrap gap-2 min-h-[2.5rem] p-2 border rounded-md">
        {selectedTags.map((tag) => (
          <Badge
            key={tag.id}
            variant="secondary"
            className="flex items-center gap-1"
            style={{ backgroundColor: tag.color + '20', color: tag.color }}
          >
            {tag.name}
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-transparent"
              onClick={() => handleTagRemove(tag.id)}
            >
              <X className="h-3 w-3" />
            </Button>
          </Badge>
        ))}
        
        {/* Add Tag Button */}
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-6 border-dashed"
              disabled={isLoading}
            >
              <Plus className="h-3 w-3 mr-1" />
              Add Tag
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[300px] p-0" align="start">
            <Command>
              <CommandInput
                placeholder="Search or create tags..."
                value={searchValue}
                onValueChange={setSearchValue}
              />
              <CommandList>
                <CommandEmpty>
                  {canCreateNew ? (
                    <div className="p-2">
                      <Button
                        variant="ghost"
                        className="w-full justify-start"
                        onClick={() => createNewTag(searchValue)}
                        disabled={isCreating}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Create "{searchValue}"
                      </Button>
                    </div>
                  ) : (
                    "No tags found."
                  )}
                </CommandEmpty>
                
                {filteredTags.length > 0 && (
                  <CommandGroup heading="Available Tags">
                    {filteredTags.map((tag) => (
                      <CommandItem
                        key={tag.id}
                        onSelect={() => handleTagSelect(tag)}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center">
                          <div
                            className="w-3 h-3 rounded-full mr-2"
                            style={{ backgroundColor: tag.color || '#6b7280' }}
                          />
                          <span>{tag.name}</span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {tag.customer_count}
                        </Badge>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                )}
                
                {canCreateNew && filteredTags.length > 0 && (
                  <CommandGroup>
                    <CommandItem
                      onSelect={() => createNewTag(searchValue)}
                      disabled={isCreating}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create "{searchValue}"
                    </CommandItem>
                  </CommandGroup>
                )}
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>
      
      {selectedTags.length === 0 && (
        <p className="text-sm text-muted-foreground">
          No tags selected. Click "Add Tag" to assign tags to this customer.
        </p>
      )}
    </div>
  );
}

// Simple tag input for backward compatibility
interface SimpleTagInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export function SimpleTagInput({ 
  value, 
  onChange, 
  placeholder = "Comma-separated tags",
  className 
}: SimpleTagInputProps) {
  return (
    <div className={cn("space-y-2", className)}>
      <Label>Tags</Label>
      <Input
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
      />
      <p className="text-xs text-muted-foreground">
        Enter tags separated by commas. Example: VIP, Wholesale, Premium
      </p>
    </div>
  );
}
