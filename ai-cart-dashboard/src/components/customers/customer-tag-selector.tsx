"use client";

import { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Check, Plus, X, Tag } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "sonner";

interface CustomerTag {
  id: number;
  name: string;
  slug: string;
  color: string;
  description?: string;
  customer_count: number;
}

interface CustomerTagSelectorProps {
  customerId: string;
  selectedTags: CustomerTag[];
  onTagsChange: (tags: CustomerTag[]) => void;
  className?: string;
}

export function CustomerTagSelector({
  customerId,
  selectedTags,
  onTagsChange,
  className,
}: CustomerTagSelectorProps) {
  const [availableTags, setAvailableTags] = useState<CustomerTag[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const [newTagName, setNewTagName] = useState("");
  const [newTagColor, setNewTagColor] = useState("#3b82f6");

  // Fetch available tags
  useEffect(() => {
    fetchAvailableTags();
  }, []);

  const fetchAvailableTags = async () => {
    try {
      const response = await fetch("/api/customer-tags?size=100");
      if (!response.ok) throw new Error("Failed to fetch tags");
      const data = await response.json();
      setAvailableTags(data.data || []);
    } catch (error) {
      console.error("Error fetching tags:", error);
      toast.error("Failed to load tags");
    }
  };

  const handleAddTag = async (tag: CustomerTag) => {
    if (selectedTags.some((t) => t.id === tag.id)) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/customers/${customerId}/tags/${tag.id}`, {
        method: "POST",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to add tag");
      }

      const updatedTags = [...selectedTags, tag];
      onTagsChange(updatedTags);
      toast.success(`Tag "${tag.name}" added successfully`);
    } catch (error) {
      console.error("Error adding tag:", error);
      toast.error(error instanceof Error ? error.message : "Failed to add tag");
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveTag = async (tagId: number) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/customers/${customerId}/tags/${tagId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to remove tag");
      }

      const updatedTags = selectedTags.filter((t) => t.id !== tagId);
      onTagsChange(updatedTags);
      toast.success("Tag removed successfully");
    } catch (error) {
      console.error("Error removing tag:", error);
      toast.error(error instanceof Error ? error.message : "Failed to remove tag");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateTag = async () => {
    if (!newTagName.trim()) return;

    setIsLoading(true);
    try {
      const response = await fetch("/api/customer-tags", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          name: newTagName.trim(),
          color: newTagColor,
          description: `Custom tag: ${newTagName.trim()}`,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create tag");
      }

      const newTag = await response.json();
      setAvailableTags((prev) => [...prev, newTag]);
      
      // Automatically add the new tag to the customer
      await handleAddTag(newTag);
      
      setNewTagName("");
      setNewTagColor("#3b82f6");
      setIsCreating(false);
      toast.success(`Tag "${newTag.name}" created and added successfully`);
    } catch (error) {
      console.error("Error creating tag:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create tag");
    } finally {
      setIsLoading(false);
    }
  };

  const filteredTags = availableTags.filter(
    (tag) =>
      !selectedTags.some((selected) => selected.id === tag.id) &&
      tag.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div className={cn("space-y-3", className)}>
      <Label className="text-sm font-medium">Customer Tags</Label>
      
      {/* Selected Tags */}
      <div className="flex flex-wrap gap-2">
        {selectedTags.map((tag) => (
          <Badge
            key={tag.id}
            variant="secondary"
            className="flex items-center gap-1 px-2 py-1"
            style={{ backgroundColor: `${tag.color}20`, borderColor: tag.color }}
          >
            <div
              className="w-2 h-2 rounded-full"
              style={{ backgroundColor: tag.color }}
            />
            {tag.name}
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-transparent"
              onClick={() => handleRemoveTag(tag.id)}
              disabled={isLoading}
            >
              <X className="h-3 w-3" />
            </Button>
          </Badge>
        ))}
      </div>

      {/* Add Tag Popover */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" className="h-8">
            <Plus className="h-4 w-4 mr-1" />
            Add Tag
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="start">
          <Command>
            <CommandInput
              placeholder="Search tags..."
              value={searchQuery}
              onValueChange={setSearchQuery}
            />
            <CommandList>
              <CommandEmpty>
                <div className="p-4 text-center">
                  <p className="text-sm text-muted-foreground mb-2">No tags found</p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsCreating(true)}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Create New Tag
                  </Button>
                </div>
              </CommandEmpty>
              <CommandGroup>
                {filteredTags.map((tag) => (
                  <CommandItem
                    key={tag.id}
                    onSelect={() => {
                      handleAddTag(tag);
                      setIsOpen(false);
                    }}
                    className="flex items-center gap-2"
                  >
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: tag.color }}
                    />
                    <span className="flex-1">{tag.name}</span>
                    <Badge variant="secondary" className="text-xs">
                      {tag.customer_count}
                    </Badge>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
          
          {/* Create New Tag Form */}
          {isCreating && (
            <div className="border-t p-4 space-y-3">
              <div className="space-y-2">
                <Label htmlFor="tag-name" className="text-sm">Tag Name</Label>
                <Input
                  id="tag-name"
                  placeholder="Enter tag name"
                  value={newTagName}
                  onChange={(e) => setNewTagName(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="tag-color" className="text-sm">Color</Label>
                <div className="flex items-center gap-2">
                  <input
                    id="tag-color"
                    type="color"
                    value={newTagColor}
                    onChange={(e) => setNewTagColor(e.target.value)}
                    className="w-8 h-8 rounded border"
                  />
                  <Input
                    value={newTagColor}
                    onChange={(e) => setNewTagColor(e.target.value)}
                    placeholder="#3b82f6"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={handleCreateTag}
                  disabled={!newTagName.trim() || isLoading}
                >
                  Create & Add
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setIsCreating(false);
                    setNewTagName("");
                    setNewTagColor("#3b82f6");
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </PopoverContent>
      </Popover>
    </div>
  );
}
