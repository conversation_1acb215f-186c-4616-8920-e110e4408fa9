"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  User, 
  Calendar, 
  ShoppingCart, 
  DollarSign, 
  TrendingUp,
  Clock,
  Star,
  Target
} from "lucide-react";
import { CustomerDetail } from "@/types/customer";
import { formatCurrency } from "@/lib/utils";
import { format } from "date-fns";

interface CustomerOverviewProps {
  customer: CustomerDetail;
}

export function CustomerOverview({ customer }: CustomerOverviewProps) {
  
  const getCustomerTypeColor = (type: string) => {
    switch (type) {
      case 'VIP': return 'bg-purple-100 text-purple-800';
      case 'WHOLESALE': return 'bg-blue-100 text-blue-800';
      case 'CORPORATE': return 'bg-indigo-100 text-indigo-800';
      case 'LOYAL': return 'bg-green-100 text-green-800';
      case 'PROSPECT': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'DIAMOND': return 'bg-cyan-100 text-cyan-800';
      case 'PLATINUM': return 'bg-slate-100 text-slate-800';
      case 'GOLD': return 'bg-yellow-100 text-yellow-800';
      case 'SILVER': return 'bg-gray-100 text-gray-600';
      default: return 'bg-orange-100 text-orange-800';
    }
  };

  // Calculate RFM group (simplified)
  const getRFMGroup = () => {
    const daysSinceLastOrder = customer.lastOrderAt 
      ? Math.floor((new Date().getTime() - new Date(customer.lastOrderAt).getTime()) / (1000 * 60 * 60 * 24))
      : 999;
    
    const frequency = customer.totalOrders;
    const monetary = customer.totalSpent;

    if (frequency >= 10 && monetary >= 1000 && daysSinceLastOrder <= 30) {
      return { label: "Champions", color: "bg-green-100 text-green-800" };
    } else if (frequency >= 5 && monetary >= 500 && daysSinceLastOrder <= 60) {
      return { label: "Loyal Customers", color: "bg-blue-100 text-blue-800" };
    } else if (frequency >= 3 && monetary >= 200 && daysSinceLastOrder <= 90) {
      return { label: "Potential Loyalists", color: "bg-purple-100 text-purple-800" };
    } else if (frequency >= 2 && daysSinceLastOrder <= 30) {
      return { label: "New Customers", color: "bg-cyan-100 text-cyan-800" };
    } else if (daysSinceLastOrder > 90) {
      return { label: "At Risk", color: "bg-red-100 text-red-800" };
    } else {
      return { label: "Need Attention", color: "bg-yellow-100 text-yellow-800" };
    }
  };

  const rfmGroup = getRFMGroup();
  const averageOrderValue = customer.totalOrders > 0 ? customer.totalSpent / customer.totalOrders : 0;
  const customerSince = format(new Date(customer.createdAt), "MMMM yyyy");

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center space-x-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src={customer.avatarUrl} />
            <AvatarFallback className="text-lg">
              {customer.firstName?.[0]}{customer.lastName?.[0]}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <CardTitle className="text-2xl">
                {customer.firstName} {customer.lastName}
              </CardTitle>
              {customer.emailVerified && (
                <Badge variant="secondary" className="text-xs">
                  <Star className="mr-1 h-3 w-3 fill-current" />
                  Verified
                </Badge>
              )}
            </div>
            <p className="text-muted-foreground">{customer.email}</p>
            <div className="flex items-center space-x-2 mt-2">
              <Badge className={getCustomerTypeColor(customer.customerType)}>
                {customer.customerType}
              </Badge>
              <Badge className={getTierColor(customer.customerTier)}>
                {customer.customerTier}
              </Badge>
              <Badge className={rfmGroup.color}>
                <Target className="mr-1 h-3 w-3" />
                {rfmGroup.label}
              </Badge>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {/* Total Spent */}
          <div className="flex items-center space-x-3 p-3 rounded-lg bg-green-50">
            <div className="p-2 rounded-full bg-green-100">
              <DollarSign className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-green-900">Total Spent</p>
              <p className="text-2xl font-bold text-green-700">
                {formatCurrency(customer.totalSpent, customer.currencyCode || "USD")}
              </p>
            </div>
          </div>

          {/* Total Orders */}
          <div className="flex items-center space-x-3 p-3 rounded-lg bg-blue-50">
            <div className="p-2 rounded-full bg-blue-100">
              <ShoppingCart className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-blue-900">Total Orders</p>
              <p className="text-2xl font-bold text-blue-700">{customer.totalOrders}</p>
            </div>
          </div>

          {/* Average Order Value */}
          <div className="flex items-center space-x-3 p-3 rounded-lg bg-purple-50">
            <div className="p-2 rounded-full bg-purple-100">
              <TrendingUp className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-purple-900">Avg Order Value</p>
              <p className="text-2xl font-bold text-purple-700">
                {formatCurrency(averageOrderValue, customer.currencyCode || "USD")}
              </p>
            </div>
          </div>

          {/* Customer Since */}
          <div className="flex items-center space-x-3 p-3 rounded-lg bg-orange-50">
            <div className="p-2 rounded-full bg-orange-100">
              <Calendar className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-orange-900">Customer Since</p>
              <p className="text-lg font-bold text-orange-700">{customerSince}</p>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-4 pt-4 border-t">
          <div className="grid gap-4 md:grid-cols-3">
            <div className="flex items-center space-x-2 text-sm">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Last Order:</span>
              <span className="font-medium">
                {customer.lastOrderAt 
                  ? format(new Date(customer.lastOrderAt), "MMM dd, yyyy")
                  : "Never"
                }
              </span>
            </div>
            
            <div className="flex items-center space-x-2 text-sm">
              <User className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Lifetime Value:</span>
              <span className="font-medium">
                {formatCurrency(customer.lifetimeValue, customer.currencyCode || "USD")}
              </span>
            </div>

            <div className="flex items-center space-x-2 text-sm">
              <Target className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Language:</span>
              <span className="font-medium uppercase">
                {customer.languageCode || "EN"}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
