"use client";

import * as React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { CustomerCreateRequest, Gender, CustomerType, CustomerTier } from "@/types/customer";
import { toast } from "sonner";

// Simple validation for email-first approach
const validateEmail = (email: string) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

interface CustomerCreateFormProps {
  onSubmit: (customerData: CustomerCreateRequest) => void;
  loading: boolean;
  onCancel: () => void;
}

export function CustomerCreateForm({ onSubmit, loading, onCancel }: CustomerCreateFormProps) {
  const [formData, setFormData] = React.useState<CustomerCreateRequest>({
    email: "",
    firstName: "",
    lastName: "",
    phone: "",
    company: "",
    jobTitle: "",
    languageCode: "en",
    currencyCode: "USD",
    timezone: "UTC",
    customerType: "REGULAR",
    customerTier: "BRONZE",
    newsletterSubscribe: false,
    emailSubscribe: true,
    phoneSubscribe: false,
    smsSubscribe: false,
    taxExempt: false,
    sendWelcomeEmail: true,
    verifyEmail: false,
    notes: "",
    tags: "",
  });

  const [errors, setErrors] = React.useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.email?.trim()) {
      newErrors.email = "Email is required";
    } else if (!validateEmail(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      toast.error("Please fix the errors in the form");
      return;
    }

    onSubmit(formData);
  };

  const updateField = (field: keyof CustomerCreateRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <p className="text-sm text-muted-foreground">
            Email is the only required field. All other information can be added later.
          </p>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="email">Email Address *</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={(e) => updateField('email', e.target.value)}
              className={errors.email ? "border-red-500" : ""}
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email}</p>
            )}
            <p className="text-sm text-muted-foreground">
              This is the primary way to contact the customer.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              placeholder="John"
              value={formData.firstName}
              onChange={(e) => updateField('firstName', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              placeholder="Doe"
              value={formData.lastName}
              onChange={(e) => updateField('lastName', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              placeholder="+****************"
              value={formData.phone}
              onChange={(e) => updateField('phone', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="company">Company</Label>
            <Input
              id="company"
              placeholder="Acme Corp"
              value={formData.company}
              onChange={(e) => updateField('company', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="jobTitle">Job Title</Label>
            <Input
              id="jobTitle"
              placeholder="Marketing Manager"
              value={formData.jobTitle}
              onChange={(e) => updateField('jobTitle', e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Customer Classification */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Classification</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="customerType">Customer Type</Label>
            <Select value={formData.customerType} onValueChange={(value: CustomerType) => updateField('customerType', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="REGULAR">Regular</SelectItem>
                <SelectItem value="VIP">VIP</SelectItem>
                <SelectItem value="WHOLESALE">Wholesale</SelectItem>
                <SelectItem value="CORPORATE">Corporate</SelectItem>
                <SelectItem value="GUEST">Guest</SelectItem>
                <SelectItem value="RETURNING">Returning</SelectItem>
                <SelectItem value="LOYAL">Loyal</SelectItem>
                <SelectItem value="PROSPECT">Prospect</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="customerTier">Customer Tier</Label>
            <Select value={formData.customerTier} onValueChange={(value: CustomerTier) => updateField('customerTier', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="BRONZE">Bronze</SelectItem>
                <SelectItem value="SILVER">Silver</SelectItem>
                <SelectItem value="GOLD">Gold</SelectItem>
                <SelectItem value="PLATINUM">Platinum</SelectItem>
                <SelectItem value="DIAMOND">Diamond</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="tags">Tags</Label>
            <Input
              id="tags"
              placeholder="Comma-separated tags"
              value={formData.tags}
              onChange={(e) => updateField('tags', e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Marketing Preferences */}
      <Card>
        <CardHeader>
          <CardTitle>Marketing Preferences</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="newsletterSubscribe"
              checked={formData.newsletterSubscribe}
              onCheckedChange={(checked) => updateField('newsletterSubscribe', checked)}
            />
            <Label htmlFor="newsletterSubscribe">Subscribe to newsletter</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="emailSubscribe"
              checked={formData.emailSubscribe}
              onCheckedChange={(checked) => updateField('emailSubscribe', checked)}
            />
            <Label htmlFor="emailSubscribe">Email marketing</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="phoneSubscribe"
              checked={formData.phoneSubscribe}
              onCheckedChange={(checked) => updateField('phoneSubscribe', checked)}
            />
            <Label htmlFor="phoneSubscribe">Phone marketing</Label>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="smsSubscribe"
              checked={formData.smsSubscribe}
              onCheckedChange={(checked) => updateField('smsSubscribe', checked)}
            />
            <Label htmlFor="smsSubscribe">SMS marketing</Label>
          </div>
        </CardContent>
      </Card>

      {/* Admin Notes */}
      <Card>
        <CardHeader>
          <CardTitle>Admin Notes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              placeholder="Internal notes about this customer..."
              value={formData.notes}
              onChange={(e) => updateField('notes', e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Personal Information */}
      <Card>
        <CardHeader>
          <CardTitle>Personal Information</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="dateOfBirth">Date of Birth</Label>
            <Input
              id="dateOfBirth"
              type="date"
              value={formData.dateOfBirth}
              onChange={(e) => updateField('dateOfBirth', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="gender">Gender</Label>
            <Select value={formData.gender} onValueChange={(value: Gender) => updateField('gender', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select gender" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="MALE">Male</SelectItem>
                <SelectItem value="FEMALE">Female</SelectItem>
                <SelectItem value="OTHER">Other</SelectItem>
                <SelectItem value="PREFER_NOT_TO_SAY">Prefer not to say</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Customer Classification */}
      <Card>
        <CardHeader>
          <CardTitle>Customer Classification</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="customerType">Customer Type</Label>
            <Select value={formData.customerType} onValueChange={(value: CustomerType) => updateField('customerType', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="REGULAR">Regular</SelectItem>
                <SelectItem value="VIP">VIP</SelectItem>
                <SelectItem value="WHOLESALE">Wholesale</SelectItem>
                <SelectItem value="CORPORATE">Corporate</SelectItem>
                <SelectItem value="GUEST">Guest</SelectItem>
                <SelectItem value="RETURNING">Returning</SelectItem>
                <SelectItem value="LOYAL">Loyal</SelectItem>
                <SelectItem value="PROSPECT">Prospect</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="customerTier">Customer Tier</Label>
            <Select value={formData.customerTier} onValueChange={(value: CustomerTier) => updateField('customerTier', value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="BRONZE">Bronze</SelectItem>
                <SelectItem value="SILVER">Silver</SelectItem>
                <SelectItem value="GOLD">Gold</SelectItem>
                <SelectItem value="PLATINUM">Platinum</SelectItem>
                <SelectItem value="DIAMOND">Diamond</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="tags">Tags</Label>
            <Input
              id="tags"
              value={formData.tags}
              onChange={(e) => updateField('tags', e.target.value)}
              placeholder="Comma-separated tags"
            />
          </div>
        </CardContent>
      </Card>

      {/* Marketing Preferences */}
      <Card>
        <CardHeader>
          <CardTitle>Marketing Preferences</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="newsletterSubscribe"
              checked={formData.newsletterSubscribe}
              onCheckedChange={(checked) => updateField('newsletterSubscribe', checked)}
            />
            <Label htmlFor="newsletterSubscribe">Subscribe to newsletter</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="emailSubscribe"
              checked={formData.emailSubscribe}
              onCheckedChange={(checked) => updateField('emailSubscribe', checked)}
            />
            <Label htmlFor="emailSubscribe">Email marketing</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="phoneSubscribe"
              checked={formData.phoneSubscribe}
              onCheckedChange={(checked) => updateField('phoneSubscribe', checked)}
            />
            <Label htmlFor="phoneSubscribe">Phone marketing</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="smsSubscribe"
              checked={formData.smsSubscribe}
              onCheckedChange={(checked) => updateField('smsSubscribe', checked)}
            />
            <Label htmlFor="smsSubscribe">SMS marketing</Label>
          </div>
        </CardContent>
      </Card>

      {/* Tax Information */}
      <Card>
        <CardHeader>
          <CardTitle>Tax Information</CardTitle>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-2">
          <div className="flex items-center space-x-2 md:col-span-2">
            <Checkbox
              id="taxExempt"
              checked={formData.taxExempt}
              onCheckedChange={(checked) => updateField('taxExempt', checked)}
            />
            <Label htmlFor="taxExempt">Tax exempt</Label>
          </div>
          {formData.taxExempt && (
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="taxExemptionReason">Tax Exemption Reason</Label>
              <Input
                id="taxExemptionReason"
                value={formData.taxExemptionReason}
                onChange={(e) => updateField('taxExemptionReason', e.target.value)}
              />
            </div>
          )}
          <div className="space-y-2">
            <Label htmlFor="vatNumber">VAT Number</Label>
            <Input
              id="vatNumber"
              value={formData.vatNumber}
              onChange={(e) => updateField('vatNumber', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="taxId">Tax ID</Label>
            <Input
              id="taxId"
              value={formData.taxId}
              onChange={(e) => updateField('taxId', e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Admin Notes */}
      <Card>
        <CardHeader>
          <CardTitle>Admin Notes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="notes">Notes</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => updateField('notes', e.target.value)}
              placeholder="Internal notes about this customer..."
            />
          </div>
        </CardContent>
      </Card>

      {/* Account Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Account Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="sendWelcomeEmail"
              checked={formData.sendWelcomeEmail}
              onCheckedChange={(checked) => updateField('sendWelcomeEmail', checked)}
            />
            <Label htmlFor="sendWelcomeEmail">Send welcome email</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="verifyEmail"
              checked={formData.verifyEmail}
              onCheckedChange={(checked) => updateField('verifyEmail', checked)}
            />
            <Label htmlFor="verifyEmail">Mark email as verified</Label>
          </div>
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={loading}>
          {loading ? "Creating..." : "Create Customer"}
        </Button>
      </div>
    </form>
  );
}
