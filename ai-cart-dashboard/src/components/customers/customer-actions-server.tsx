import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Lock, Unlock, Mail, Download } from "lucide-react";
import { CustomerDetail } from "@/types/customer";
import { toggleCustomerLock, verifyCustomerEmail } from "@/lib/api/customers";
import { redirect } from "next/navigation";

interface CustomerActionsServerProps {
  customer: CustomerDetail;
}

// Server action for toggling customer lock
async function toggleLockAction(customerId: number, currentLocked: boolean) {
  "use server";
  
  try {
    await toggleCustomerLock(customerId, !currentLocked);
    redirect(`/dashboard/customers/${customerId}`);
  } catch (error) {
    // Handle error - in a real app you'd want better error handling
    redirect(`/dashboard/customers/${customerId}?error=lock-failed`);
  }
}

// Server action for email verification
async function verifyEmailAction(customerId: number) {
  "use server";
  
  try {
    await verifyCustomerEmail(customerId);
    redirect(`/dashboard/customers/${customerId}?success=email-sent`);
  } catch (error) {
    redirect(`/dashboard/customers/${customerId}?error=email-failed`);
  }
}

// Server action for data export
async function exportDataAction(customerId: number) {
  "use server";
  
  // TODO: Implement actual data export
  redirect(`/dashboard/customers/${customerId}?info=export-coming-soon`);
}

export function CustomerActionsServer({ customer }: CustomerActionsServerProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <form action={toggleLockAction.bind(null, customer.id, customer.accountLocked)}>
          <DropdownMenuItem asChild>
            <button type="submit" className="w-full flex items-center">
              {customer.accountLocked ? (
                <>
                  <Unlock className="mr-2 h-4 w-4" />
                  Unlock Account
                </>
              ) : (
                <>
                  <Lock className="mr-2 h-4 w-4" />
                  Lock Account
                </>
              )}
            </button>
          </DropdownMenuItem>
        </form>
        
        {!customer.emailVerified && (
          <form action={verifyEmailAction.bind(null, customer.id)}>
            <DropdownMenuItem asChild>
              <button type="submit" className="w-full flex items-center">
                <Mail className="mr-2 h-4 w-4" />
                Send Email Verification
              </button>
            </DropdownMenuItem>
          </form>
        )}
        
        <DropdownMenuSeparator />
        
        <form action={exportDataAction.bind(null, customer.id)}>
          <DropdownMenuItem asChild>
            <button type="submit" className="w-full flex items-center">
              <Download className="mr-2 h-4 w-4" />
              Export Customer Data
            </button>
          </DropdownMenuItem>
        </form>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
