"use client";

import * as React from "react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  Award, 
  TrendingUp, 
  RefreshCw, 
  Settings,
  Crown,
  Star,
  Gem,
  Shield,
  Medal
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { toast } from "sonner";

interface TierInfo {
  current_tier: string;
  tier_overridden: boolean;
  tier_override_reason?: string;
  tier_updated_at?: string;
  total_spent: number;
  total_orders: number;
  requirements: {
    bronze: number;
    silver: number;
    gold: number;
    platinum: number;
    diamond: number;
  };
}

interface CustomerTierManagerProps {
  customerId: number;
  className?: string;
}

export function CustomerTierManager({ customerId, className }: CustomerTierManagerProps) {
  const [tierInfo, setTierInfo] = React.useState<TierInfo | null>(null);
  const [isLoading, setIsLoading] = React.useState(true);
  const [isOverrideDialogOpen, setIsOverrideDialogOpen] = React.useState(false);
  const [overrideForm, setOverrideForm] = React.useState({
    tier: '',
    reason: '',
    updatedBy: 'admin' // TODO: Get from session
  });

  React.useEffect(() => {
    fetchTierInfo();
  }, [customerId]);

  const fetchTierInfo = async () => {
    try {
      const response = await fetch(`/api/customers/${customerId}/tier`, {
        headers: {
          'Shop-Id': '1', // TODO: Get from context
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        setTierInfo(data);
      }
    } catch (error) {
      console.error('Error fetching tier info:', error);
      toast.error('Failed to load tier information');
    } finally {
      setIsLoading(false);
    }
  };

  const recalculateTier = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/customers/${customerId}/tier/recalculate`, {
        method: 'POST',
        headers: {
          'Shop-Id': '1', // TODO: Get from context
        },
      });
      
      if (response.ok) {
        await fetchTierInfo();
        toast.success('Tier recalculated successfully');
      } else {
        throw new Error('Failed to recalculate tier');
      }
    } catch (error) {
      toast.error('Failed to recalculate tier');
    } finally {
      setIsLoading(false);
    }
  };

  const overrideTier = async () => {
    if (!overrideForm.tier || !overrideForm.reason) {
      toast.error('Please select a tier and provide a reason');
      return;
    }

    try {
      const response = await fetch(`/api/customers/${customerId}/tier/override`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Shop-Id': '1', // TODO: Get from context
        },
        body: JSON.stringify(overrideForm),
      });
      
      if (response.ok) {
        await fetchTierInfo();
        setIsOverrideDialogOpen(false);
        setOverrideForm({ tier: '', reason: '', updatedBy: 'admin' });
        toast.success('Tier overridden successfully');
      } else {
        throw new Error('Failed to override tier');
      }
    } catch (error) {
      toast.error('Failed to override tier');
    }
  };

  const resetTierOverride = async () => {
    try {
      const response = await fetch(`/api/customers/${customerId}/tier/override?updated_by=admin`, {
        method: 'DELETE',
        headers: {
          'Shop-Id': '1', // TODO: Get from context
        },
      });
      
      if (response.ok) {
        await fetchTierInfo();
        toast.success('Tier override reset successfully');
      } else {
        throw new Error('Failed to reset tier override');
      }
    } catch (error) {
      toast.error('Failed to reset tier override');
    }
  };

  const getTierIcon = (tier: string) => {
    const icons = {
      BRONZE: Medal,
      SILVER: Shield,
      GOLD: Star,
      PLATINUM: Crown,
      DIAMOND: Gem,
    };
    return icons[tier as keyof typeof icons] || Award;
  };

  const getTierColor = (tier: string) => {
    const colors = {
      BRONZE: "bg-amber-100 text-amber-800 border-amber-200",
      SILVER: "bg-gray-100 text-gray-800 border-gray-200",
      GOLD: "bg-yellow-100 text-yellow-800 border-yellow-200",
      PLATINUM: "bg-slate-100 text-slate-800 border-slate-200",
      DIAMOND: "bg-blue-100 text-blue-800 border-blue-200",
    };
    return colors[tier as keyof typeof colors] || "bg-gray-100 text-gray-800 border-gray-200";
  };

  const calculateProgress = (currentSpent: number, nextTierThreshold: number, currentTierThreshold: number) => {
    if (currentSpent >= nextTierThreshold) return 100;
    const progress = ((currentSpent - currentTierThreshold) / (nextTierThreshold - currentTierThreshold)) * 100;
    return Math.max(0, Math.min(100, progress));
  };

  const getNextTierInfo = (currentTier: string, totalSpent: number, requirements: any) => {
    const tiers = ['BRONZE', 'SILVER', 'GOLD', 'PLATINUM', 'DIAMOND'];
    const currentIndex = tiers.indexOf(currentTier);
    
    if (currentIndex === -1 || currentIndex === tiers.length - 1) {
      return null; // Already at highest tier
    }

    const nextTier = tiers[currentIndex + 1];
    const nextThreshold = requirements[nextTier.toLowerCase()];
    const currentThreshold = requirements[currentTier.toLowerCase()];
    
    return {
      tier: nextTier,
      threshold: nextThreshold,
      remaining: Math.max(0, nextThreshold - totalSpent),
      progress: calculateProgress(totalSpent, nextThreshold, currentThreshold)
    };
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Award className="mr-2 h-5 w-5" />
            Customer Tier
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-8 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!tierInfo) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Award className="mr-2 h-5 w-5" />
            Customer Tier
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">Failed to load tier information</p>
        </CardContent>
      </Card>
    );
  }

  const TierIcon = getTierIcon(tierInfo.current_tier);
  const nextTierInfo = getNextTierInfo(tierInfo.current_tier, tierInfo.total_spent, tierInfo.requirements);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Award className="mr-2 h-5 w-5" />
            Customer Tier
          </div>
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              onClick={recalculateTier}
              disabled={isLoading}
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
            <Dialog open={isOverrideDialogOpen} onOpenChange={setIsOverrideDialogOpen}>
              <DialogTrigger asChild>
                <Button size="sm" variant="outline">
                  <Settings className="h-4 w-4" />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Override Customer Tier</DialogTitle>
                  <DialogDescription>
                    Manually set the customer tier. This will override automatic tier calculation.
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="tier">New Tier</Label>
                    <Select
                      value={overrideForm.tier}
                      onValueChange={(value) => setOverrideForm({ ...overrideForm, tier: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select tier" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="BRONZE">Bronze</SelectItem>
                        <SelectItem value="SILVER">Silver</SelectItem>
                        <SelectItem value="GOLD">Gold</SelectItem>
                        <SelectItem value="PLATINUM">Platinum</SelectItem>
                        <SelectItem value="DIAMOND">Diamond</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="reason">Reason</Label>
                    <Textarea
                      id="reason"
                      value={overrideForm.reason}
                      onChange={(e) => setOverrideForm({ ...overrideForm, reason: e.target.value })}
                      placeholder="Explain why you're overriding the tier..."
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsOverrideDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={overrideTier}>
                    Override Tier
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Tier */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <TierIcon className="h-6 w-6 text-muted-foreground" />
            <div>
              <Badge className={getTierColor(tierInfo.current_tier)}>
                {tierInfo.current_tier}
              </Badge>
              {tierInfo.tier_overridden && (
                <p className="text-xs text-muted-foreground mt-1">
                  Manually overridden
                </p>
              )}
            </div>
          </div>
          <div className="text-right">
            <p className="text-sm font-medium">{formatCurrency(tierInfo.total_spent)}</p>
            <p className="text-xs text-muted-foreground">{tierInfo.total_orders} orders</p>
          </div>
        </div>

        {/* Override Info */}
        {tierInfo.tier_overridden && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-800">Tier Override Active</p>
                {tierInfo.tier_override_reason && (
                  <p className="text-xs text-yellow-700 mt-1">{tierInfo.tier_override_reason}</p>
                )}
              </div>
              <Button
                size="sm"
                variant="outline"
                onClick={resetTierOverride}
                className="text-yellow-800 border-yellow-300 hover:bg-yellow-100"
              >
                Reset
              </Button>
            </div>
          </div>
        )}

        {/* Progress to Next Tier */}
        {nextTierInfo && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium">Progress to {nextTierInfo.tier}</p>
              <p className="text-xs text-muted-foreground">
                {formatCurrency(nextTierInfo.remaining)} remaining
              </p>
            </div>
            <Progress value={nextTierInfo.progress} className="h-2" />
            <p className="text-xs text-muted-foreground">
              Spend {formatCurrency(nextTierInfo.threshold)} total to reach {nextTierInfo.tier}
            </p>
          </div>
        )}

        {/* Tier Requirements */}
        <div className="space-y-2">
          <p className="text-sm font-medium">Tier Requirements</p>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex justify-between">
              <span>Bronze:</span>
              <span>{formatCurrency(tierInfo.requirements.bronze)}+</span>
            </div>
            <div className="flex justify-between">
              <span>Silver:</span>
              <span>{formatCurrency(tierInfo.requirements.silver)}+</span>
            </div>
            <div className="flex justify-between">
              <span>Gold:</span>
              <span>{formatCurrency(tierInfo.requirements.gold)}+</span>
            </div>
            <div className="flex justify-between">
              <span>Platinum:</span>
              <span>{formatCurrency(tierInfo.requirements.platinum)}+</span>
            </div>
            <div className="flex justify-between col-span-2">
              <span>Diamond:</span>
              <span>{formatCurrency(tierInfo.requirements.diamond)}+</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
