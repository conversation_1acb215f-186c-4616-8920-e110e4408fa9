"use client";

import * as React from "react";
import { CustomerAddresses } from "./customer-addresses";
import { CustomerAddressForm } from "./customer-address-form";
import { CustomerAddress, CustomerAddressCreateRequest, CustomerAddressUpdateRequest } from "@/types/customer";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface CustomerAddressesManagerProps {
  customerId: number;
  initialAddresses: CustomerAddress[];
  onAddressesChange?: () => void;
}

export function CustomerAddressesManager({
  customerId,
  initialAddresses,
  onAddressesChange
}: CustomerAddressesManagerProps) {
  const [addresses, setAddresses] = React.useState<CustomerAddress[]>(initialAddresses);
  const [loading, setLoading] = React.useState(false);
  const [showAddForm, setShowAddForm] = React.useState(false);
  const [editingAddress, setEditingAddress] = React.useState<CustomerAddress | null>(null);

  // Update addresses when initialAddresses changes
  React.useEffect(() => {
    setAddresses(initialAddresses);
  }, [initialAddresses]);

  const handleAddressCreate = async (addressData: CustomerAddressCreateRequest) => {
    try {
      setLoading(true);

      const response = await fetch(`/api/customers/${customerId}/addresses`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(addressData),
      });

      if (response.ok) {
        const newAddress = await response.json();
        setAddresses(prev => [...prev, newAddress]);
        setShowAddForm(false);
        toast.success("Address created successfully");
        onAddressesChange?.();
      } else {
        throw new Error('Failed to create address');
      }
    } catch (error) {
      toast.error("Failed to create address");
      console.error('Error creating address:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddressEdit = (address: CustomerAddress) => {
    setEditingAddress(address);
  };

  const handleAddressUpdate = async (addressData: CustomerAddressCreateRequest) => {
    if (!editingAddress) return;

    try {
      setLoading(true);

      const updateData: CustomerAddressUpdateRequest = {
        ...addressData,
      };

      const response = await fetch(`/api/customers/${customerId}/addresses/${editingAddress.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (response.ok) {
        const updatedAddress = await response.json();
        setAddresses(prev => 
          prev.map(addr => addr.id === editingAddress.id ? updatedAddress : addr)
        );
        setEditingAddress(null);
        toast.success("Address updated successfully");
        onAddressesChange?.();
      } else {
        throw new Error('Failed to update address');
      }
    } catch (error) {
      toast.error("Failed to update address");
      console.error('Error updating address:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddressDelete = async (addressId: number) => {
    try {
      setLoading(true);

      const response = await fetch(`/api/customers/${customerId}/addresses/${addressId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setAddresses(prev => prev.filter(addr => addr.id !== addressId));
        toast.success("Address deleted successfully");
        onAddressesChange?.();
      } else {
        throw new Error('Failed to delete address');
      }
    } catch (error) {
      toast.error("Failed to delete address");
      console.error('Error deleting address:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSetDefault = async (addressId: number) => {
    try {
      setLoading(true);

      const addressToUpdate = addresses.find(addr => addr.id === addressId);
      if (!addressToUpdate) return;

      const updateData: CustomerAddressUpdateRequest = {
        isDefault: true,
      };

      const response = await fetch(`/api/customers/${customerId}/addresses/${addressId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      if (response.ok) {
        const updatedAddress = await response.json();
        setAddresses(prev => 
          prev.map(addr => ({
            ...addr,
            isDefault: addr.id === addressId ? true : false
          }))
        );
        toast.success("Default address updated successfully");
        onAddressesChange?.();
      } else {
        throw new Error('Failed to set default address');
      }
    } catch (error) {
      toast.error("Failed to set default address");
      console.error('Error setting default address:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <CustomerAddresses
        customerId={customerId}
        addresses={addresses}
        onAddressCreate={() => setShowAddForm(true)}
        onAddressEdit={handleAddressEdit}
        onAddressDelete={handleAddressDelete}
        onSetDefault={handleSetDefault}
        loading={loading}
      />

      {/* Add Address Dialog */}
      <Dialog open={showAddForm} onOpenChange={setShowAddForm}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Address</DialogTitle>
          </DialogHeader>
          <CustomerAddressForm
            customerId={customerId}
            onSubmit={handleAddressCreate}
            onCancel={() => setShowAddForm(false)}
            loading={loading}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Address Dialog */}
      <Dialog open={!!editingAddress} onOpenChange={(open) => !open && setEditingAddress(null)}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Address</DialogTitle>
          </DialogHeader>
          {editingAddress && (
            <CustomerAddressForm
              customerId={customerId}
              address={editingAddress}
              onSubmit={handleAddressUpdate}
              onCancel={() => setEditingAddress(null)}
              loading={loading}
            />
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
