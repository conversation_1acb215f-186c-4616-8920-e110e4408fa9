"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { 
  Receipt, 
  Edit, 
  Save, 
  X,
  Shield,
  AlertTriangle,
  CheckCircle
} from "lucide-react";
import { CustomerDetail } from "@/types/customer";
import { toast } from "sonner";

interface CustomerTaxDetailsProps {
  customer: CustomerDetail;
  onUpdate?: () => void;
}

export function CustomerTaxDetails({ customer, onUpdate }: CustomerTaxDetailsProps) {
  const [isEditing, setIsEditing] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [formData, setFormData] = React.useState({
    taxExempt: customer.taxExempt,
    taxExemptionReason: customer.taxExemptionReason || "",
    vatNumber: customer.vatNumber || "",
    taxId: customer.taxId || "",
  });

  const handleSave = async () => {
    try {
      setLoading(true);
      
      // TODO: Implement API call to update tax details
      // await updateCustomerTaxDetails(customer.id, formData);
      
      toast.success("Tax details updated successfully");
      setIsEditing(false);
      onUpdate?.();
    } catch (error) {
      toast.error("Failed to update tax details");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      taxExempt: customer.taxExempt,
      taxExemptionReason: customer.taxExemptionReason || "",
      vatNumber: customer.vatNumber || "",
      taxId: customer.taxId || "",
    });
    setIsEditing(false);
  };

  const getTaxStatus = () => {
    if (formData.taxExempt) {
      return {
        icon: Shield,
        text: "Tax Exempt",
        color: "text-blue-600",
        bgColor: "bg-blue-100",
        description: "This customer is exempt from taxes"
      };
    } else {
      return {
        icon: CheckCircle,
        text: "Taxable",
        color: "text-green-600",
        bgColor: "bg-green-100",
        description: "Standard tax rates apply"
      };
    }
  };

  const taxStatus = getTaxStatus();
  const TaxIcon = taxStatus.icon;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Receipt className="h-5 w-5" />
            <span>Tax Details</span>
          </CardTitle>
          {!isEditing ? (
            <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          ) : (
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button size="sm" onClick={handleSave} disabled={loading}>
                <Save className="h-4 w-4 mr-2" />
                {loading ? "Saving..." : "Save"}
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Tax Status */}
        <div className="flex items-center justify-between p-3 rounded-lg border">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full ${taxStatus.bgColor}`}>
              <TaxIcon className={`h-4 w-4 ${taxStatus.color}`} />
            </div>
            <div>
              <p className="font-medium">Tax Status</p>
              <p className="text-sm text-muted-foreground">
                {taxStatus.description}
              </p>
            </div>
          </div>
          <Badge variant="secondary" className={taxStatus.bgColor}>
            {taxStatus.text}
          </Badge>
        </div>

        {/* Tax Exempt Toggle */}
        <div className="space-y-4">
          <div className="flex items-center justify-between p-3 rounded-lg border">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-full bg-orange-100">
                <AlertTriangle className="h-4 w-4 text-orange-600" />
              </div>
              <div>
                <Label htmlFor="taxExempt" className="font-medium">Tax Exempt Status</Label>
                <p className="text-sm text-muted-foreground">
                  Enable if customer is exempt from taxes
                </p>
              </div>
            </div>
            <Switch
              id="taxExempt"
              checked={formData.taxExempt}
              onCheckedChange={(checked) => 
                isEditing && setFormData(prev => ({ ...prev, taxExempt: checked }))
              }
              disabled={!isEditing}
            />
          </div>

          {/* Tax Exemption Reason */}
          {formData.taxExempt && (
            <div className="space-y-2">
              <Label htmlFor="taxExemptionReason">Tax Exemption Reason</Label>
              {isEditing ? (
                <Textarea
                  id="taxExemptionReason"
                  placeholder="Explain why this customer is tax exempt..."
                  value={formData.taxExemptionReason}
                  onChange={(e) => setFormData(prev => ({ ...prev, taxExemptionReason: e.target.value }))}
                />
              ) : (
                <p className="text-sm font-medium p-3 bg-gray-50 rounded-lg">
                  {customer.taxExemptionReason || "No reason provided"}
                </p>
              )}
            </div>
          )}
        </div>

        {/* Tax Identification Numbers */}
        <div className="space-y-4">
          <h4 className="font-medium text-sm">Tax Identification</h4>
          
          {/* VAT Number */}
          <div className="space-y-2">
            <Label htmlFor="vatNumber">VAT Number</Label>
            {isEditing ? (
              <Input
                id="vatNumber"
                placeholder="e.g., GB123456789"
                value={formData.vatNumber}
                onChange={(e) => setFormData(prev => ({ ...prev, vatNumber: e.target.value }))}
              />
            ) : (
              <p className="text-sm font-medium">
                {customer.vatNumber || "Not provided"}
              </p>
            )}
            <p className="text-xs text-muted-foreground">
              Value Added Tax identification number for EU customers
            </p>
          </div>

          {/* Tax ID */}
          <div className="space-y-2">
            <Label htmlFor="taxId">Tax ID / EIN</Label>
            {isEditing ? (
              <Input
                id="taxId"
                placeholder="e.g., 12-3456789"
                value={formData.taxId}
                onChange={(e) => setFormData(prev => ({ ...prev, taxId: e.target.value }))}
              />
            ) : (
              <p className="text-sm font-medium">
                {customer.taxId || "Not provided"}
              </p>
            )}
            <p className="text-xs text-muted-foreground">
              Tax identification number or Employer Identification Number
            </p>
          </div>
        </div>

        {/* Tax Compliance Note */}
        <div className="p-3 rounded-lg bg-yellow-50 border border-yellow-200">
          <div className="flex items-start space-x-2">
            <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-yellow-900">Tax Compliance</p>
              <p className="text-xs text-yellow-700 mt-1">
                Ensure tax exemption status is properly documented and complies with local tax regulations. 
                Changes to tax status may affect future orders.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
