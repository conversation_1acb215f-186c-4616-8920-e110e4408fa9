"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  User, 
  Mail, 
  ShoppingCart, 
  CreditCard, 
  Lock, 
  Unlock, 
  UserCheck,
  Calendar,
  Clock
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface ActivityItem {
  id: string;
  type: 'registration' | 'order' | 'payment' | 'email' | 'account' | 'verification';
  title: string;
  description: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

interface CustomerActivityTimelineProps {
  customerId: number;
}

export function CustomerActivityTimeline({ customerId }: CustomerActivityTimelineProps) {
  const [activities, setActivities] = React.useState<ActivityItem[]>([]);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    fetchActivityTimeline();
  }, [customerId]);

  const fetchActivityTimeline = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/customers/${customerId}/activity`, {
        headers: {
          'Shop-Id': '1', // TODO: Get from context
        },
      });

      if (response.ok) {
        const data = await response.json();
        setActivities(data.activities || []);
      } else {
        // If API not implemented yet, show empty state
        setActivities([]);
      }
    } catch (error) {
      console.error('Error fetching activity timeline:', error);
      setActivities([]);
    } finally {
      setLoading(false);
    }
  };

  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'registration':
        return <User className="h-4 w-4" />;
      case 'verification':
        return <UserCheck className="h-4 w-4" />;
      case 'order':
        return <ShoppingCart className="h-4 w-4" />;
      case 'payment':
        return <CreditCard className="h-4 w-4" />;
      case 'email':
        return <Mail className="h-4 w-4" />;
      case 'account':
        return <Lock className="h-4 w-4" />;
      default:
        return <Calendar className="h-4 w-4" />;
    }
  };

  const getActivityColor = (type: ActivityItem['type']) => {
    switch (type) {
      case 'registration':
        return 'bg-blue-500';
      case 'verification':
        return 'bg-green-500';
      case 'order':
        return 'bg-purple-500';
      case 'payment':
        return 'bg-emerald-500';
      case 'email':
        return 'bg-orange-500';
      case 'account':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getBadgeVariant = (type: ActivityItem['type']) => {
    switch (type) {
      case 'registration':
      case 'verification':
        return 'default';
      case 'order':
      case 'payment':
        return 'secondary';
      case 'email':
        return 'outline';
      case 'account':
        return 'destructive';
      default:
        return 'secondary';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="mr-2 h-5 w-5" />
            Activity Timeline
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  <div className="h-3 bg-gray-100 rounded animate-pulse w-3/4" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="mr-2 h-5 w-5" />
          Activity Timeline
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-96">
          <div className="space-y-4">
            {activities.map((activity, index) => (
              <div key={activity.id} className="flex items-start space-x-3">
                {/* Timeline dot */}
                <div className="relative">
                  <div className={`w-8 h-8 rounded-full ${getActivityColor(activity.type)} flex items-center justify-center text-white`}>
                    {getActivityIcon(activity.type)}
                  </div>
                  {index < activities.length - 1 && (
                    <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-0.5 h-6 bg-gray-200" />
                  )}
                </div>

                {/* Activity content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900">
                      {activity.title}
                    </h4>
                    <Badge variant={getBadgeVariant(activity.type)} className="text-xs">
                      {activity.type}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    {activity.description}
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                  </p>
                  
                  {/* Additional metadata */}
                  {activity.metadata && (
                    <div className="mt-2 text-xs text-gray-500">
                      {activity.metadata.orderId && (
                        <span>Order #{activity.metadata.orderId}</span>
                      )}
                      {activity.metadata.amount && (
                        <span className="ml-2">${activity.metadata.amount}</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>

        {activities.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Calendar className="mx-auto h-12 w-12 text-gray-300" />
            <p className="mt-2">No activity found</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
