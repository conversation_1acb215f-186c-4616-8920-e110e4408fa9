"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Mail, 
  Phone, 
  Globe, 
  Edit, 
  Save, 
  X,
  Clock,
  Languages
} from "lucide-react";
import { CustomerDetail } from "@/types/customer";
import { toast } from "sonner";

interface CustomerContactInfoProps {
  customer: CustomerDetail;
  onUpdate?: () => void;
}

export function CustomerContactInfo({ customer, onUpdate }: CustomerContactInfoProps) {
  const [isEditing, setIsEditing] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [formData, setFormData] = React.useState({
    email: customer.email,
    phone: customer.phone || "",
    languageCode: customer.languageCode || "en",
    currencyCode: customer.currencyCode || "USD",
    timezone: customer.timezone || "UTC",
  });

  const handleSave = async () => {
    try {
      setLoading(true);

      const response = await fetch(`/api/customers/${customer.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Shop-Id': '1', // TODO: Get from context
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast.success("Contact information updated successfully");
        setIsEditing(false);
        onUpdate?.();
      } else {
        throw new Error('Failed to update contact information');
      }
    } catch (error) {
      toast.error("Failed to update contact information");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      email: customer.email,
      phone: customer.phone || "",
      languageCode: customer.languageCode || "en",
      currencyCode: customer.currencyCode || "USD",
      timezone: customer.timezone || "UTC",
    });
    setIsEditing(false);
  };

  const languages = [
    { code: "en", name: "English" },
    { code: "es", name: "Spanish" },
    { code: "fr", name: "French" },
    { code: "de", name: "German" },
    { code: "it", name: "Italian" },
    { code: "pt", name: "Portuguese" },
    { code: "zh", name: "Chinese" },
    { code: "ja", name: "Japanese" },
    { code: "ko", name: "Korean" },
    { code: "ar", name: "Arabic" },
  ];

  const currencies = [
    { code: "USD", name: "US Dollar" },
    { code: "EUR", name: "Euro" },
    { code: "GBP", name: "British Pound" },
    { code: "CAD", name: "Canadian Dollar" },
    { code: "AUD", name: "Australian Dollar" },
    { code: "JPY", name: "Japanese Yen" },
    { code: "CNY", name: "Chinese Yuan" },
    { code: "INR", name: "Indian Rupee" },
  ];

  const timezones = [
    { code: "UTC", name: "UTC" },
    { code: "America/New_York", name: "Eastern Time" },
    { code: "America/Chicago", name: "Central Time" },
    { code: "America/Denver", name: "Mountain Time" },
    { code: "America/Los_Angeles", name: "Pacific Time" },
    { code: "Europe/London", name: "London" },
    { code: "Europe/Paris", name: "Paris" },
    { code: "Asia/Tokyo", name: "Tokyo" },
    { code: "Asia/Shanghai", name: "Shanghai" },
    { code: "Australia/Sydney", name: "Sydney" },
  ];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Mail className="h-5 w-5" />
            <span>Contact Information</span>
          </CardTitle>
          {!isEditing ? (
            <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          ) : (
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button size="sm" onClick={handleSave} disabled={loading}>
                <Save className="h-4 w-4 mr-2" />
                {loading ? "Saving..." : "Save"}
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Email */}
        <div className="space-y-2">
          <Label htmlFor="email" className="flex items-center space-x-2">
            <Mail className="h-4 w-4" />
            <span>Email Address</span>
          </Label>
          {isEditing ? (
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
            />
          ) : (
            <p className="text-sm font-medium">{customer.email}</p>
          )}
        </div>

        {/* Phone */}
        <div className="space-y-2">
          <Label htmlFor="phone" className="flex items-center space-x-2">
            <Phone className="h-4 w-4" />
            <span>Phone Number</span>
          </Label>
          {isEditing ? (
            <Input
              id="phone"
              type="tel"
              placeholder="+****************"
              value={formData.phone}
              onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
            />
          ) : (
            <p className="text-sm font-medium">{customer.phone || "Not provided"}</p>
          )}
        </div>

        {/* Language */}
        <div className="space-y-2">
          <Label htmlFor="language" className="flex items-center space-x-2">
            <Languages className="h-4 w-4" />
            <span>Language</span>
          </Label>
          {isEditing ? (
            <Select 
              value={formData.languageCode} 
              onValueChange={(value) => setFormData(prev => ({ ...prev, languageCode: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {languages.map((lang) => (
                  <SelectItem key={lang.code} value={lang.code}>
                    {lang.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <p className="text-sm font-medium">
              {languages.find(l => l.code === customer.languageCode)?.name || "English"}
            </p>
          )}
        </div>

        {/* Currency */}
        <div className="space-y-2">
          <Label htmlFor="currency" className="flex items-center space-x-2">
            <Globe className="h-4 w-4" />
            <span>Currency</span>
          </Label>
          {isEditing ? (
            <Select 
              value={formData.currencyCode} 
              onValueChange={(value) => setFormData(prev => ({ ...prev, currencyCode: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {currencies.map((currency) => (
                  <SelectItem key={currency.code} value={currency.code}>
                    {currency.code} - {currency.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <p className="text-sm font-medium">
              {customer.currencyCode} - {currencies.find(c => c.code === customer.currencyCode)?.name || "US Dollar"}
            </p>
          )}
        </div>

        {/* Timezone */}
        <div className="space-y-2">
          <Label htmlFor="timezone" className="flex items-center space-x-2">
            <Clock className="h-4 w-4" />
            <span>Timezone</span>
          </Label>
          {isEditing ? (
            <Select 
              value={formData.timezone} 
              onValueChange={(value) => setFormData(prev => ({ ...prev, timezone: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {timezones.map((tz) => (
                  <SelectItem key={tz.code} value={tz.code}>
                    {tz.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <p className="text-sm font-medium">
              {timezones.find(tz => tz.code === customer.timezone)?.name || "UTC"}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
