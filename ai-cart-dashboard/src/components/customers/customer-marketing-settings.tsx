"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Mail, 
  MessageSquare, 
  Phone, 
  Bell,
  Edit, 
  Save, 
  X,
  Shield,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { CustomerDetail } from "@/types/customer";
import { toast } from "sonner";

interface CustomerMarketingSettingsProps {
  customer: CustomerDetail;
  onUpdate?: () => void;
}

export function CustomerMarketingSettings({ customer, onUpdate }: CustomerMarketingSettingsProps) {
  const [isEditing, setIsEditing] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [formData, setFormData] = React.useState({
    newsletterSubscribe: customer.newsletterSubscribe,
    emailSubscribe: customer.emailSubscribe,
    phoneSubscribe: customer.phoneSubscribe,
    smsSubscribe: customer.smsSubscribe,
  });

  const handleSave = async () => {
    try {
      setLoading(true);
      
      // TODO: Implement API call to update marketing settings
      // await updateCustomerMarketingSettings(customer.id, formData);
      
      toast.success("Marketing preferences updated successfully");
      setIsEditing(false);
      onUpdate?.();
    } catch (error) {
      toast.error("Failed to update marketing preferences");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      newsletterSubscribe: customer.newsletterSubscribe,
      emailSubscribe: customer.emailSubscribe,
      phoneSubscribe: customer.phoneSubscribe,
      smsSubscribe: customer.smsSubscribe,
    });
    setIsEditing(false);
  };

  const getConsentStatus = () => {
    const hasAnyConsent = formData.newsletterSubscribe || formData.emailSubscribe || 
                         formData.phoneSubscribe || formData.smsSubscribe;
    
    if (hasAnyConsent) {
      return {
        icon: CheckCircle,
        text: "Consent Given",
        color: "text-green-600",
        bgColor: "bg-green-100"
      };
    } else {
      return {
        icon: AlertCircle,
        text: "No Consent",
        color: "text-red-600",
        bgColor: "bg-red-100"
      };
    }
  };

  const consentStatus = getConsentStatus();
  const ConsentIcon = consentStatus.icon;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Bell className="h-5 w-5" />
            <span>Marketing Settings</span>
          </CardTitle>
          {!isEditing ? (
            <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          ) : (
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button size="sm" onClick={handleSave} disabled={loading}>
                <Save className="h-4 w-4 mr-2" />
                {loading ? "Saving..." : "Save"}
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Consent Status */}
        <div className="flex items-center justify-between p-3 rounded-lg border">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full ${consentStatus.bgColor}`}>
              <ConsentIcon className={`h-4 w-4 ${consentStatus.color}`} />
            </div>
            <div>
              <p className="font-medium">Marketing Consent</p>
              <p className="text-sm text-muted-foreground">
                Customer's permission for marketing communications
              </p>
            </div>
          </div>
          <Badge variant="secondary" className={consentStatus.bgColor}>
            <Shield className="h-3 w-3 mr-1" />
            {consentStatus.text}
          </Badge>
        </div>

        {/* Marketing Preferences */}
        <div className="space-y-4">
          <h4 className="font-medium text-sm">Communication Preferences</h4>
          
          {/* Newsletter */}
          <div className="flex items-center justify-between p-3 rounded-lg border">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-full bg-blue-100">
                <Mail className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <Label htmlFor="newsletter" className="font-medium">Newsletter</Label>
                <p className="text-sm text-muted-foreground">
                  Weekly newsletter with updates and news
                </p>
              </div>
            </div>
            <Switch
              id="newsletter"
              checked={formData.newsletterSubscribe}
              onCheckedChange={(checked) => 
                isEditing && setFormData(prev => ({ ...prev, newsletterSubscribe: checked }))
              }
              disabled={!isEditing}
            />
          </div>

          {/* Email Marketing */}
          <div className="flex items-center justify-between p-3 rounded-lg border">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-full bg-green-100">
                <Mail className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <Label htmlFor="email" className="font-medium">Email Marketing</Label>
                <p className="text-sm text-muted-foreground">
                  Promotional emails and special offers
                </p>
              </div>
            </div>
            <Switch
              id="email"
              checked={formData.emailSubscribe}
              onCheckedChange={(checked) => 
                isEditing && setFormData(prev => ({ ...prev, emailSubscribe: checked }))
              }
              disabled={!isEditing}
            />
          </div>

          {/* Phone Marketing */}
          <div className="flex items-center justify-between p-3 rounded-lg border">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-full bg-purple-100">
                <Phone className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <Label htmlFor="phone" className="font-medium">Phone Marketing</Label>
                <p className="text-sm text-muted-foreground">
                  Marketing calls and voice messages
                </p>
              </div>
            </div>
            <Switch
              id="phone"
              checked={formData.phoneSubscribe}
              onCheckedChange={(checked) => 
                isEditing && setFormData(prev => ({ ...prev, phoneSubscribe: checked }))
              }
              disabled={!isEditing}
            />
          </div>

          {/* SMS Marketing */}
          <div className="flex items-center justify-between p-3 rounded-lg border">
            <div className="flex items-center space-x-3">
              <div className="p-2 rounded-full bg-orange-100">
                <MessageSquare className="h-4 w-4 text-orange-600" />
              </div>
              <div>
                <Label htmlFor="sms" className="font-medium">SMS Marketing</Label>
                <p className="text-sm text-muted-foreground">
                  Text messages with promotions and updates
                </p>
              </div>
            </div>
            <Switch
              id="sms"
              checked={formData.smsSubscribe}
              onCheckedChange={(checked) => 
                isEditing && setFormData(prev => ({ ...prev, smsSubscribe: checked }))
              }
              disabled={!isEditing}
            />
          </div>
        </div>

        {/* Compliance Note */}
        <div className="p-3 rounded-lg bg-gray-50 border">
          <div className="flex items-start space-x-2">
            <Shield className="h-4 w-4 text-gray-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-gray-900">Compliance Note</p>
              <p className="text-xs text-gray-600 mt-1">
                All marketing communications require explicit customer consent. 
                Changes are logged for compliance purposes.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
