"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  Edit, 
  Save, 
  X,
  Lock,
  Eye,
  Calendar
} from "lucide-react";
import { CustomerDetail } from "@/types/customer";
import { toast } from "sonner";
import { format } from "date-fns";

interface CustomerNotesProps {
  customer: CustomerDetail;
  onUpdate?: () => void;
}

export function CustomerNotes({ customer, onUpdate }: CustomerNotesProps) {
  const [isEditing, setIsEditing] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [notes, setNotes] = React.useState(customer.notes || "");

  const handleSave = async () => {
    try {
      setLoading(true);
      
      // TODO: Implement API call to update customer notes
      // await updateCustomerNotes(customer.id, { notes });
      
      toast.success("Notes updated successfully");
      setIsEditing(false);
      onUpdate?.();
    } catch (error) {
      toast.error("Failed to update notes");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setNotes(customer.notes || "");
    setIsEditing(false);
  };

  const wordCount = notes.trim().split(/\s+/).filter(Boolean).length;
  const characterCount = notes.length;
  const lastUpdated = customer.updatedAt ? format(new Date(customer.updatedAt), "MMM dd, yyyy 'at' h:mm a") : "Never";

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Private Notes</span>
          </CardTitle>
          {!isEditing ? (
            <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          ) : (
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button size="sm" onClick={handleSave} disabled={loading}>
                <Save className="h-4 w-4 mr-2" />
                {loading ? "Saving..." : "Save"}
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Privacy Notice */}
        <div className="flex items-center space-x-2 p-2 rounded-lg bg-amber-50 border border-amber-200">
          <Lock className="h-4 w-4 text-amber-600" />
          <p className="text-sm text-amber-800">
            <span className="font-medium">Staff Only:</span> These notes are private and only visible to staff members.
          </p>
        </div>

        {/* Notes Content */}
        <div className="space-y-2">
          {isEditing ? (
            <div className="space-y-2">
              <Textarea
                placeholder="Add private notes about this customer..."
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={6}
                className="resize-none"
              />
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>{characterCount} characters</span>
                <span>{wordCount} words</span>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              {customer.notes ? (
                <div className="p-3 rounded-lg bg-gray-50 border min-h-[120px]">
                  <p className="text-sm whitespace-pre-wrap">{customer.notes}</p>
                </div>
              ) : (
                <div className="p-6 text-center border-2 border-dashed border-gray-200 rounded-lg">
                  <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No notes added yet</p>
                  <p className="text-xs text-gray-400 mt-1">
                    Click Edit to add private notes about this customer
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Notes Metadata */}
        {customer.notes && (
          <div className="flex items-center justify-between pt-2 border-t">
            <div className="flex items-center space-x-4 text-xs text-muted-foreground">
              <div className="flex items-center space-x-1">
                <Calendar className="h-3 w-3" />
                <span>Last updated: {lastUpdated}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Eye className="h-3 w-3" />
                <span>{wordCount} words</span>
              </div>
            </div>
            <Badge variant="secondary" className="text-xs">
              <Lock className="h-3 w-3 mr-1" />
              Private
            </Badge>
          </div>
        )}

        {/* Notes Guidelines */}
        <div className="p-3 rounded-lg bg-blue-50 border border-blue-200">
          <div className="flex items-start space-x-2">
            <FileText className="h-4 w-4 text-blue-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-blue-900">Notes Guidelines</p>
              <ul className="text-xs text-blue-700 mt-1 space-y-1">
                <li>• Record important customer preferences and history</li>
                <li>• Note any special requirements or instructions</li>
                <li>• Document customer service interactions</li>
                <li>• Keep information professional and relevant</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
