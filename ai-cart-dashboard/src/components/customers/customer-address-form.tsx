"use client";

import * as React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { CustomerAddress, CustomerAddressCreateRequest } from "@/types/customer";
import { toast } from "sonner";
import { MapPin, Building, Phone, Star } from "lucide-react";

// Zod schema for address creation with proper validation
const addressCreateSchema = z.object({
  type: z.enum(["BILLING", "SHIPPING", "BOTH"], {
    required_error: "Please select an address type",
  }),
  firstName: z.string()
    .min(1, "First name is required")
    .max(30, "First name must be less than 30 characters"),
  lastName: z.string()
    .max(30, "Last name must be less than 30 characters")
    .optional(),
  company: z.string()
    .max(100, "Company must be less than 100 characters")
    .optional(),
  line1: z.string()
    .min(1, "Address line 1 is required")
    .max(255, "Address line 1 must be less than 255 characters"),
  line2: z.string()
    .max(255, "Address line 2 must be less than 255 characters")
    .optional(),
  city: z.string()
    .min(1, "City is required")
    .max(30, "City must be less than 30 characters"),
  state: z.string()
    .min(1, "State is required")
    .max(30, "State must be less than 30 characters"),
  country: z.string()
    .min(1, "Country is required")
    .max(50, "Country must be less than 50 characters"),
  countryCode: z.string()
    .length(2, "Country code must be 2 characters")
    .optional(),
  postalCode: z.string()
    .min(1, "Postal code is required")
    .max(20, "Postal code must be less than 20 characters"),
  phone: z.string()
    .max(20, "Phone must be less than 20 characters")
    .optional(),
  isDefault: z.boolean().default(false),
});

type AddressFormData = z.infer<typeof addressCreateSchema>;

interface CustomerAddressFormProps {
  customerId: number;
  address?: CustomerAddress; // For editing
  onSubmit: (addressData: CustomerAddressCreateRequest) => void;
  onCancel: () => void;
  loading?: boolean;
}

export function CustomerAddressForm({
  customerId,
  address,
  onSubmit,
  onCancel,
  loading = false
}: CustomerAddressFormProps) {
  const isEditing = !!address;

  const form = useForm<AddressFormData>({
    resolver: zodResolver(addressCreateSchema),
    defaultValues: {
      type: address?.type || "BOTH",
      firstName: address?.firstName || "",
      lastName: address?.lastName || "",
      company: address?.company || "",
      line1: address?.line1 || "",
      line2: address?.line2 || "",
      city: address?.city || "",
      state: address?.state || "",
      country: address?.country || "United States",
      countryCode: address?.countryCode || "US",
      postalCode: address?.postalCode || "",
      phone: address?.phone || "",
      isDefault: address?.isDefault || false,
    },
  });

  const handleSubmit = (data: AddressFormData) => {
    const addressData: CustomerAddressCreateRequest = {
      ...data,
      // Convert empty strings to undefined for optional fields
      lastName: data.lastName || undefined,
      company: data.company || undefined,
      line2: data.line2 || undefined,
      countryCode: data.countryCode || undefined,
      phone: data.phone || undefined,
    };

    onSubmit(addressData);
  };

  const getAddressTypeDescription = (type: string) => {
    switch (type) {
      case 'BILLING':
        return 'Used for billing and invoicing';
      case 'SHIPPING':
        return 'Used for shipping and delivery';
      case 'BOTH':
        return 'Used for both billing and shipping';
      default:
        return '';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MapPin className="h-5 w-5" />
          <span>{isEditing ? 'Edit Address' : 'Add New Address'}</span>
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          {isEditing 
            ? 'Update the address information below.'
            : 'Add a new address for easy checkout later.'
          }
        </p>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Address Type */}
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address Type</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select address type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="BILLING">Billing Address</SelectItem>
                      <SelectItem value="SHIPPING">Shipping Address</SelectItem>
                      <SelectItem value="BOTH">Billing & Shipping</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    {getAddressTypeDescription(field.value)}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Name Fields */}
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="John" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Company */}
            <FormField
              control={form.control}
              name="company"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center space-x-2">
                    <Building className="h-4 w-4" />
                    <span>Company (Optional)</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="Acme Corp" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Address Lines */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="line1"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address Line 1 *</FormLabel>
                    <FormControl>
                      <Input placeholder="123 Main Street" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="line2"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address Line 2 (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Apt 4B, Suite 100, etc." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* City, State, Postal Code */}
            <div className="grid gap-4 md:grid-cols-3">
              <FormField
                control={form.control}
                name="city"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>City *</FormLabel>
                    <FormControl>
                      <Input placeholder="New York" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="state"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>State *</FormLabel>
                    <FormControl>
                      <Input placeholder="NY" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="postalCode"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Postal Code *</FormLabel>
                    <FormControl>
                      <Input placeholder="10001" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Country */}
            <FormField
              control={form.control}
              name="country"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Country *</FormLabel>
                  <FormControl>
                    <Input placeholder="United States" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Phone */}
            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center space-x-2">
                    <Phone className="h-4 w-4" />
                    <span>Phone (Optional)</span>
                  </FormLabel>
                  <FormControl>
                    <Input placeholder="+****************" {...field} />
                  </FormControl>
                  <FormDescription>
                    Phone number for delivery notifications
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Default Address */}
            <FormField
              control={form.control}
              name="isDefault"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel className="flex items-center space-x-2">
                      <Star className="h-4 w-4" />
                      <span>Set as default address</span>
                    </FormLabel>
                    <FormDescription>
                      This address will be used by default for this address type during checkout.
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-4 pt-4">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "Saving..." : isEditing ? "Update Address" : "Add Address"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
