import * as React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Building, 
  Calendar, 
  Globe, 
  DollarSign,
  ShoppingCart,
  Award,
  Shield,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react";
import { CustomerDetail } from "@/types/customer";
import { formatCurrency } from "@/lib/utils";
import { format } from "date-fns";

interface CustomerDetailViewProps {
  customer: CustomerDetail;
}

export function CustomerDetailView({ customer }: CustomerDetailViewProps) {
  const getCustomerTypeColor = (type: string) => {
    switch (type) {
      case 'VIP':
        return 'bg-purple-100 text-purple-800';
      case 'WHOLESALE':
        return 'bg-blue-100 text-blue-800';
      case 'CORPORATE':
        return 'bg-indigo-100 text-indigo-800';
      case 'LOYAL':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCustomerTierColor = (tier: string) => {
    switch (tier) {
      case 'DIAMOND':
        return 'bg-cyan-100 text-cyan-800';
      case 'PLATINUM':
        return 'bg-slate-100 text-slate-800';
      case 'GOLD':
        return 'bg-yellow-100 text-yellow-800';
      case 'SILVER':
        return 'bg-gray-100 text-gray-600';
      default:
        return 'bg-orange-100 text-orange-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <User className="mr-2 h-5 w-5" />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-gray-500">Full Name</label>
              <p className="text-sm font-medium">{customer.firstName} {customer.lastName}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Email</label>
              <div className="flex items-center space-x-2">
                <p className="text-sm">{customer.email}</p>
                {customer.emailVerified ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
              </div>
            </div>
            {customer.phone && (
              <div>
                <label className="text-sm font-medium text-gray-500">Phone</label>
                <div className="flex items-center space-x-2">
                  <p className="text-sm">{customer.phone}</p>
                  {customer.phoneVerified ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                </div>
              </div>
            )}
            {customer.company && (
              <div>
                <label className="text-sm font-medium text-gray-500">Company</label>
                <p className="text-sm">{customer.company}</p>
              </div>
            )}
            {customer.jobTitle && (
              <div>
                <label className="text-sm font-medium text-gray-500">Job Title</label>
                <p className="text-sm">{customer.jobTitle}</p>
              </div>
            )}
            {customer.dateOfBirth && (
              <div>
                <label className="text-sm font-medium text-gray-500">Date of Birth</label>
                <p className="text-sm">{format(new Date(customer.dateOfBirth), 'PPP')}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Customer Classification */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Award className="mr-2 h-5 w-5" />
            Customer Classification
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-4">
            <div>
              <label className="text-sm font-medium text-gray-500">Customer Type</label>
              <div className="mt-1">
                <Badge className={getCustomerTypeColor(customer.customerType)}>
                  {customer.customerType}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Customer Tier</label>
              <div className="mt-1">
                <Badge className={getCustomerTierColor(customer.customerTier)}>
                  {customer.customerTier}
                </Badge>
              </div>
            </div>
          </div>
          
          {customer.tags && (
            <div>
              <label className="text-sm font-medium text-gray-500">Tags</label>
              <div className="mt-1 flex flex-wrap gap-1">
                {customer.tags.split(',').map((tag, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {tag.trim()}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Customer Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DollarSign className="mr-2 h-5 w-5" />
            Customer Metrics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <ShoppingCart className="mx-auto h-8 w-8 text-blue-600 mb-2" />
              <p className="text-2xl font-bold text-blue-600">{customer.totalOrders}</p>
              <p className="text-sm text-gray-600">Total Orders</p>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <DollarSign className="mx-auto h-8 w-8 text-green-600 mb-2" />
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(customer.totalSpent, customer.currencyCode)}
              </p>
              <p className="text-sm text-gray-600">Total Spent</p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <Award className="mx-auto h-8 w-8 text-purple-600 mb-2" />
              <p className="text-2xl font-bold text-purple-600">
                {formatCurrency(customer.averageOrderValue, customer.currencyCode)}
              </p>
              <p className="text-sm text-gray-600">Avg Order Value</p>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <Globe className="mx-auto h-8 w-8 text-orange-600 mb-2" />
              <p className="text-2xl font-bold text-orange-600">
                {formatCurrency(customer.lifetimeValue, customer.currencyCode)}
              </p>
              <p className="text-sm text-gray-600">Lifetime Value</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Account Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Shield className="mr-2 h-5 w-5" />
            Account Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-gray-500">Account Status</label>
              <div className="flex items-center space-x-2 mt-1">
                {customer.accountLocked ? (
                  <>
                    <AlertCircle className="h-4 w-4 text-red-500" />
                    <Badge variant="destructive">Locked</Badge>
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <Badge variant="secondary">Active</Badge>
                  </>
                )}
              </div>
              {customer.accountLocked && customer.accountLockedReason && (
                <p className="text-sm text-red-600 mt-1">{customer.accountLockedReason}</p>
              )}
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-500">Tax Status</label>
              <div className="flex items-center space-x-2 mt-1">
                {customer.taxExempt ? (
                  <Badge variant="outline">Tax Exempt</Badge>
                ) : (
                  <Badge variant="secondary">Taxable</Badge>
                )}
              </div>
              {customer.taxExempt && customer.taxExemptionReason && (
                <p className="text-sm text-gray-600 mt-1">{customer.taxExemptionReason}</p>
              )}
            </div>
          </div>

          {(customer.vatNumber || customer.taxId) && (
            <div className="grid gap-4 md:grid-cols-2">
              {customer.vatNumber && (
                <div>
                  <label className="text-sm font-medium text-gray-500">VAT Number</label>
                  <p className="text-sm">{customer.vatNumber}</p>
                </div>
              )}
              {customer.taxId && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Tax ID</label>
                  <p className="text-sm">{customer.taxId}</p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Marketing Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Mail className="mr-2 h-5 w-5" />
            Marketing Preferences
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="flex items-center justify-between">
              <span className="text-sm">Newsletter</span>
              <Badge variant={customer.newsletterSubscribe ? "default" : "secondary"}>
                {customer.newsletterSubscribe ? "Subscribed" : "Not Subscribed"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Email Marketing</span>
              <Badge variant={customer.emailSubscribe ? "default" : "secondary"}>
                {customer.emailSubscribe ? "Subscribed" : "Not Subscribed"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Phone Marketing</span>
              <Badge variant={customer.phoneSubscribe ? "default" : "secondary"}>
                {customer.phoneSubscribe ? "Subscribed" : "Not Subscribed"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">SMS Marketing</span>
              <Badge variant={customer.smsSubscribe ? "default" : "secondary"}>
                {customer.smsSubscribe ? "Subscribed" : "Not Subscribed"}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Addresses */}
      {customer.addresses && customer.addresses.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MapPin className="mr-2 h-5 w-5" />
              Addresses
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {customer.addresses.map((address, index) => (
                <div key={address.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{address.type}</h4>
                    {address.isDefault && (
                      <Badge variant="outline">Default</Badge>
                    )}
                  </div>
                  <div className="text-sm text-gray-600">
                    <p>{address.firstName} {address.lastName}</p>
                    {address.company && <p>{address.company}</p>}
                    <p>{address.line1}</p>
                    {address.line2 && <p>{address.line2}</p>}
                    <p>{address.city}, {address.state} {address.postalCode}</p>
                    <p>{address.country}</p>
                    {address.phone && <p>{address.phone}</p>}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Admin Notes */}
      {customer.notes && (
        <Card>
          <CardHeader>
            <CardTitle>Admin Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 whitespace-pre-wrap">{customer.notes}</p>
          </CardContent>
        </Card>
      )}

      {/* Account Dates */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Account Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-sm font-medium text-gray-500">Created</label>
              <p className="text-sm">{format(new Date(customer.createdAt), 'PPP')}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Last Updated</label>
              <p className="text-sm">{format(new Date(customer.updatedAt), 'PPP')}</p>
            </div>
            {customer.firstOrderAt && (
              <div>
                <label className="text-sm font-medium text-gray-500">First Order</label>
                <p className="text-sm">{format(new Date(customer.firstOrderAt), 'PPP')}</p>
              </div>
            )}
            {customer.lastOrderAt && (
              <div>
                <label className="text-sm font-medium text-gray-500">Last Order</label>
                <p className="text-sm">{format(new Date(customer.lastOrderAt), 'PPP')}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
