"use client";

import * as React from "react";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { 
  Users, 
  UserCheck, 
  Crown, 
  Lock, 
  TrendingUp, 
  Activity,
  DollarSign,
  ShoppingCart
} from "lucide-react";
import { CustomerStats } from "@/types/customer";
import { formatCurrency } from "@/lib/utils";

interface CustomerStatsCardsProps {
  stats: CustomerStats;
}

export function CustomerStatsCards({ stats }: CustomerStatsCardsProps) {
  const statsCards = [
    {
      title: "Total Customers",
      value: stats.totalCustomers.toLocaleString(),
      description: "All registered customers",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Verified Customers",
      value: stats.verifiedCustomers.toLocaleString(),
      description: `${((stats.verifiedCustomers / stats.totalCustomers) * 100).toFixed(1)}% verified`,
      icon: User<PERSON><PERSON><PERSON>,
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "VIP Customers",
      value: stats.vipCustomers.toLocaleString(),
      description: `${((stats.vipCustomers / stats.totalCustomers) * 100).toFixed(1)}% of total`,
      icon: Crown,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      title: "Locked Accounts",
      value: stats.lockedCustomers.toLocaleString(),
      description: stats.lockedCustomers > 0 ? "Requires attention" : "All accounts active",
      icon: Lock,
      color: stats.lockedCustomers > 0 ? "text-red-600" : "text-gray-600",
      bgColor: stats.lockedCustomers > 0 ? "bg-red-50" : "bg-gray-50",
    },
    {
      title: "New This Month",
      value: stats.newCustomersThisMonth.toLocaleString(),
      description: "Recently registered",
      icon: TrendingUp,
      color: "text-emerald-600",
      bgColor: "bg-emerald-50",
    },
    {
      title: "Active Customers",
      value: stats.activeCustomers.toLocaleString(),
      description: "Ordered in last 30 days",
      icon: Activity,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
    },
    {
      title: "Avg Lifetime Value",
      value: formatCurrency(stats.averageLifetimeValue, "USD"),
      description: "Per customer LTV",
      icon: DollarSign,
      color: "text-indigo-600",
      bgColor: "bg-indigo-50",
    },
    {
      title: "Avg Order Value",
      value: formatCurrency(Math.round(stats.averageOrderValue), "USD"),
      description: "Per order average",
      icon: ShoppingCart,
      color: "text-cyan-600",
      bgColor: "bg-cyan-50",
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {statsCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-full ${stat.bgColor}`}>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
