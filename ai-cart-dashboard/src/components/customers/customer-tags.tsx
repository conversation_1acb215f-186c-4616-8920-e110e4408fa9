"use client";

import * as React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tag } from "lucide-react";
import { CustomerDetail } from "@/types/customer";
import { CustomerTagSelector } from "./customer-tag-selector";

interface CustomerTag {
  id: number;
  name: string;
  slug: string;
  color: string;
  description?: string;
  customer_count: number;
}

interface CustomerTagsProps {
  customer: CustomerDetail;
  onUpdate?: () => void;
}

export function CustomerTags({ customer, onUpdate }: CustomerTagsProps) {
  const [customerTags, setCustomerTags] = React.useState<CustomerTag[]>([]);
  const [loading, setLoading] = React.useState(true);

  // Fetch customer tags on mount
  React.useEffect(() => {
    fetchCustomerTags();
  }, [customer.id]);

  const fetchCustomerTags = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/customers/${customer.id}/tags`);
      if (!response.ok) throw new Error("Failed to fetch customer tags");
      const data = await response.json();
      setCustomerTags(data.data || []);
    } catch (error) {
      console.error("Error fetching customer tags:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleTagsChange = (newTags: CustomerTag[]) => {
    setCustomerTags(newTags);
    onUpdate?.();
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Tag className="h-5 w-5" />
            <span>Customer Tags</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-sm text-muted-foreground">Loading tags...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Tag className="h-5 w-5" />
          <span>Customer Tags</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <CustomerTagSelector
          customerId={customer.id.toString()}
          selectedTags={customerTags}
          onTagsChange={handleTagsChange}
        />

        {/* Tags Info */}
        <div className="p-3 rounded-lg bg-blue-50 border border-blue-200">
          <div className="flex items-start space-x-2">
            <Tag className="h-4 w-4 text-blue-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-blue-900">About Tags</p>
              <p className="text-xs text-blue-700 mt-1">
                Use tags to organize and categorize customers. Tags help with filtering,
                segmentation, and creating targeted marketing campaigns.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
