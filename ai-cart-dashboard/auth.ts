import NextAuth from "next-auth"
import Gith<PERSON><PERSON><PERSON><PERSON> from 'next-auth/providers/github';
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials"
import { z } from 'zod';
import { loginByCredential, loginByOauthCredential } from "@/lib/actions/auth-login";
import { User } from "@/types/next-auth";
import { env } from "@/env";

export const { 
    handlers,
    auth,
    signIn,
    signOut
  } = NextAuth({
    providers: [
      GithubProvider({
        clientId: process.env.GITHUB_CLIENT_ID || '',
        clientSecret: process.env.GITHUB_CLIENT_SECRET || ''
      }),
      GoogleProvider({
        clientId: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET
      }),
      CredentialsProvider({
        name: "Credential<PERSON>",
        credentials: {
          email: { label: "Email", type: "text"},
          password: { label: "Password", type: "password" }
        },
        async authorize(credentials) {
          const parsedCredentials = z
            .object({ email: z.string().email(), password: z.string().min(6) })
            .safeParse(credentials);

          if (!parsedCredentials.success) {
            return null;
          }

          try {
            const { email, password } = parsedCredentials.data;
            const {data, error} = await loginByCredential({email, password});

            if(error) {
              return null;
            }

            return data;

          } catch (error) {
            if (process.env.NODE_ENV === "development") {
              console.error("[Auth Error]:", error);
            }
            return null;
          }
        }
      })
    ],
    pages: {
      signIn: '/signin',
      error: '/signin',
    },
    callbacks: {
      async signIn(res) {

        if(res.account?.provider === 'credentials') {
          return true;
        }

        if(res.account?.provider === 'google' || res.account?.provider === 'github') {
          const profile = res.profile;
          return !!profile?.email
        }

        return false;
      },
      async session({ session, token }) {
        session.user.id = token.sub || "";
        session.accessToken = token.accessToken as string;
        session.refreshToken = token.refreshToken as string;
        session.tokenExpiresAt = token.tokenExpiresAt as number;
        return session;
      },
      async jwt({ token, account, user, trigger }) {
        // Handle manual token refresh
        if (trigger === "update") {
          return token;
        }

        if (account && user) {
          const _user = user as User;

          if(account.provider == 'github' || account.provider == 'google') {
            const {data, error} = await loginByOauthCredential({
              provider: account.provider,
              access_token: account?.access_token || '',
              name: user.name || '',
              email: user.email || '',
            });

            if(!error && data?.id){
              _user.id = data.id;
              token.verifiedAt = data.verifiedAt;
              _user.token = data.token;
              token.sub = data.id;

              // Store refresh token info
              if (data.refreshToken) {
                token.refreshToken = data.refreshToken;
                token.tokenExpiresAt = data.expiresAt;
              }
            } else {
              return null;
            }
          }

          token.accessToken = _user.token;

          // For credentials login, extract token info
          if (account.provider === 'credentials' && _user.token) {
            try {
              const payload = JSON.parse(atob(_user.token.split('.')[1]));
              if (payload?.exp) {
                token.tokenExpiresAt = payload.exp;
              }
            } catch (error) {
              console.error('Error extracting token info:', error);
            }
          }
        }

        // Check token expiry and refresh if needed
        if (token.accessToken && token.refreshToken && token.tokenExpiresAt) {
          const currentTime = Math.floor(Date.now() / 1000);
          const timeUntilExpiry = (token.tokenExpiresAt as number) - currentTime;

          // Refresh if token expires within 5 minutes
          if (timeUntilExpiry < 300) {
            try {
              const response = await fetch(`${env.API_BASE_URL}/auth/refresh`, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  refreshToken: token.refreshToken,
                }),
              });

              if (response.ok) {
                const refreshData = await response.json();
                token.accessToken = refreshData.token;
                token.refreshToken = refreshData.refreshToken;
                token.tokenExpiresAt = refreshData.expiresAt;
              } else {
                // Refresh failed, invalidate session
                return null;
              }
            } catch (error) {
              console.error('Token refresh failed:', error);
              return null;
            }
          }
        }

        return token;
      },
    },
    session: {
      strategy: "jwt",
    },
  })